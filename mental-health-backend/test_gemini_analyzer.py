"""
Test script for Gemini AI analyzer
"""
import asyncio
import os
from dotenv import load_dotenv
from app.services.analyzers.ai_analyzer_factory import UnifiedAIAnalyzer

# Load environment variables
load_dotenv()

async def test_gemini_analyzer():
    """Test Gemini analyzer functionality"""
    print("🤖 Testing Gemini AI Analyzer...")
    
    # Check if Gemini API key is available
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Gemini API key not found in environment variables")
        print("Please set GEMINI_API_KEY in your .env file")
        print("Get your API key from: https://makersuite.google.com/app/apikey")
        return
    
    try:
        # Initialize unified analyzer
        analyzer = UnifiedAIAnalyzer()
        print(f"✅ Initialized {analyzer.provider} analyzer")
        
        # Get provider info
        provider_info = analyzer.get_provider_info()
        print(f"📋 Provider: {provider_info['provider']}")
        print(f"📋 Model: {provider_info['model']}")
        print(f"📋 Rate limits: {provider_info['rate_limits']}")
        
        # Test content for analysis
        test_content = """
        I've been struggling with depression for the past two years. It started when I lost my job during the pandemic. 
        I felt completely hopeless and isolated. Some days I couldn't even get out of bed. 
        
        But things started to change when I finally reached out for help. I found a therapist who really understood me, 
        and I started taking medication that helped stabilize my mood. 
        
        The journey hasn't been easy - there were setbacks and dark days. But slowly, I began to see small improvements. 
        I started exercising again, reconnected with old friends, and even found a new job that I actually enjoy.
        
        Today, I can say that while I still have bad days, I have hope again. I've learned coping strategies that work for me, 
        and I know that recovery is possible. If you're struggling, please know that you're not alone and things can get better.
        """
        
        print(f"\n📝 Test content preview: {test_content[:150]}...")
        
        # Test story classification
        print(f"\n🔍 Testing story classification...")
        classification_result = await analyzer.analyze_content(
            test_content, 
            analysis_types=["classification"]
        )
        
        if classification_result["classification"]:
            cls = classification_result["classification"]
            print(f"✅ Primary category: {cls.get('primary_category', 'unknown')}")
            print(f"✅ Narrative structure: {cls.get('narrative_structure', 'unknown')}")
            print(f"✅ Key themes: {cls.get('key_themes', [])}")
            print(f"✅ Confidence score: {cls.get('confidence_score', 0.0)}")
            
            # Show emotional arc
            emotional_arc = cls.get('emotional_arc', {})
            if emotional_arc:
                print(f"✅ Emotional arc:")
                print(f"   Starting: {emotional_arc.get('starting_emotion', 'unknown')}")
                print(f"   Lowest point: {emotional_arc.get('lowest_point', 'unknown')}")
                print(f"   Turning point: {emotional_arc.get('turning_point', 'unknown')}")
                print(f"   Ending: {emotional_arc.get('ending_emotion', 'unknown')}")
        else:
            print("❌ Classification failed")
            print(f"Errors: {classification_result.get('errors', [])}")
        
        # Test sentiment analysis
        print(f"\n💭 Testing sentiment analysis...")
        sentiment_result = await analyzer.analyze_content(
            test_content, 
            analysis_types=["sentiment"]
        )
        
        if sentiment_result["sentiment"]:
            sent = sentiment_result["sentiment"]
            print(f"✅ Overall sentiment: {sent.get('overall_sentiment', 'unknown')}")
            print(f"✅ Sentiment score: {sent.get('sentiment_score', 0.0)}")
            print(f"✅ Primary emotions: {sent.get('primary_emotions', [])}")
            
            # Show hope and distress indicators
            hope = sent.get('hope_indicators', {})
            distress = sent.get('distress_indicators', {})
            risk = sent.get('risk_factors', {})
            
            print(f"✅ Hope indicators: {hope.get('has_hope', False)} (score: {hope.get('hope_score', 0.0)})")
            print(f"✅ Distress level: {distress.get('distress_level', 'unknown')}")
            print(f"✅ Suicide risk: {risk.get('suicide_risk', 'unknown')}")
            
            # Show recovery indicators
            recovery = sent.get('recovery_indicators', {})
            print(f"✅ Recovery stage: {recovery.get('recovery_stage', 'unknown')}")
            print(f"✅ Shows progress: {recovery.get('shows_progress', False)}")
        else:
            print("❌ Sentiment analysis failed")
            print(f"Errors: {sentiment_result.get('errors', [])}")
        
        # Test comprehensive analysis
        print(f"\n🔬 Testing comprehensive analysis...")
        comprehensive_result = await analyzer.analyze_content(test_content)
        
        print(f"✅ Analysis provider: {comprehensive_result.get('analysis_provider', 'unknown')}")
        print(f"✅ Analysis timestamp: {comprehensive_result.get('analysis_timestamp', 'unknown')}")
        print(f"✅ Has classification: {comprehensive_result['classification'] is not None}")
        print(f"✅ Has sentiment: {comprehensive_result['sentiment'] is not None}")
        print(f"✅ Errors: {len(comprehensive_result.get('errors', []))}")
        
        # Test batch analysis with smaller sample
        print(f"\n📦 Testing batch analysis...")
        test_contents = [
            "I'm feeling really anxious about my job interview tomorrow. Any tips?",
            "Just wanted to share that therapy has been life-changing for me. Don't give up hope!",
            "Having a really tough day with depression. Everything feels overwhelming."
        ]
        
        batch_results = await analyzer.batch_analyze_content(
            test_contents, 
            analysis_types=["classification"]
        )
        
        print(f"✅ Batch analyzed {len(batch_results)} contents")
        for i, result in enumerate(batch_results):
            if result["classification"]:
                category = result["classification"].get("primary_category", "unknown")
                print(f"   Content {i+1}: {category}")
            else:
                print(f"   Content {i+1}: Analysis failed")
        
        print(f"\n🎉 Gemini analyzer test completed successfully!")
        print(f"   Provider: {analyzer.provider}")
        print(f"   All core functions working properly")
        
    except Exception as e:
        print(f"❌ Error testing Gemini analyzer: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_gemini_analyzer())
