#!/usr/bin/env python3
"""
Simple Reddit API test without complex dependencies
"""
import os
import praw
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_reddit_connection():
    """Test Reddit API connection"""
    print("🔗 Testing Reddit API Connection...")
    print("=" * 40)
    
    # Check credentials
    client_id = os.getenv("REDDIT_CLIENT_ID")
    client_secret = os.getenv("REDDIT_CLIENT_SECRET")
    user_agent = os.getenv("REDDIT_USER_AGENT", "MentalHealthAnalyzer:v1.0:by-testuser")
    
    if not client_id or client_id == "your_reddit_client_id_here":
        print("❌ Reddit Client ID not configured")
        print("Please set REDDIT_CLIENT_ID in your .env file")
        print("Get credentials from: https://www.reddit.com/prefs/apps")
        return False
    
    if not client_secret or client_secret == "your_reddit_client_secret_here":
        print("❌ Reddit Client Secret not configured")
        print("Please set REDDIT_CLIENT_SECRET in your .env file")
        return False
    
    print(f"✅ Client ID: {client_id[:10]}...")
    print(f"✅ User Agent: {user_agent}")
    
    try:
        # Initialize Reddit instance
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        print("✅ Reddit instance created successfully")
        
        # Test by accessing a public subreddit
        print("\n🧪 Testing subreddit access...")
        subreddit = reddit.subreddit("getmotivated")
        print(f"✅ Accessed r/{subreddit.display_name}")
        print(f"   Subscribers: {subreddit.subscribers:,}")
        print(f"   Description: {subreddit.public_description[:100]}...")
        
        # Get a few recent posts
        print("\n📝 Fetching recent posts...")
        posts = []
        for post in subreddit.hot(limit=5):
            posts.append({
                "title": post.title,
                "author": str(post.author) if post.author else "[deleted]",
                "score": post.score,
                "num_comments": post.num_comments,
                "created_utc": post.created_utc,
                "url": post.url
            })
        
        print(f"✅ Retrieved {len(posts)} posts")
        
        # Display sample posts
        for i, post in enumerate(posts[:3], 1):
            print(f"\n📄 Post {i}:")
            print(f"   Title: {post['title'][:80]}...")
            print(f"   Author: {post['author']}")
            print(f"   Score: {post['score']}")
            print(f"   Comments: {post['num_comments']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Reddit API test failed: {e}")
        return False

def test_mental_health_subreddits():
    """Test accessing mental health related subreddits"""
    print("\n🧠 Testing Mental Health Subreddits...")
    print("=" * 40)
    
    client_id = os.getenv("REDDIT_CLIENT_ID")
    client_secret = os.getenv("REDDIT_CLIENT_SECRET")
    user_agent = os.getenv("REDDIT_USER_AGENT", "MentalHealthAnalyzer:v1.0:by-testuser")
    
    if not client_id or not client_secret:
        print("❌ Reddit credentials not configured")
        return False
    
    try:
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # Test mental health related subreddits
        mental_health_subs = ["depression", "anxiety", "mentalhealth", "getmotivated"]
        
        for sub_name in mental_health_subs:
            try:
                subreddit = reddit.subreddit(sub_name)
                print(f"\n✅ r/{sub_name}")
                print(f"   Subscribers: {subreddit.subscribers:,}")
                
                # Get one recent post
                for post in subreddit.hot(limit=1):
                    print(f"   Recent post: {post.title[:60]}...")
                    break
                    
            except Exception as e:
                print(f"❌ r/{sub_name}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Mental health subreddits test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Reddit API Test Suite")
    print("=" * 50)
    
    # Test basic connection
    basic_success = test_reddit_connection()
    
    if basic_success:
        # Test mental health subreddits
        mental_health_success = test_mental_health_subreddits()
        
        if mental_health_success:
            print("\n🎉 All Reddit tests PASSED!")
            print("Reddit data collection is ready to use!")
        else:
            print("\n⚠️  Basic Reddit connection works, but mental health subreddits test failed")
    else:
        print("\n❌ Reddit API tests FAILED!")
        print("Please check your Reddit API credentials")
