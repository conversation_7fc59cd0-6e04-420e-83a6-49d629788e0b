#!/usr/bin/env python3
"""
Test script using REST API for Gemini
"""
import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def analyze_mental_health_content_rest(content):
    """Analyze mental health content using REST API"""
    api_key = os.getenv("GEMINI_API_KEY")
    model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    
    url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_name}:generateContent?key={api_key}"
    
    prompt = f"""
    Analyze the following mental health related content and provide a structured analysis:

    Content: "{content}"

    Please provide:
    1. Primary mental health category (depression, anxiety, bipolar, recovery, etc.)
    2. Sentiment (positive, negative, neutral)
    3. Risk level (low, medium, high)
    4. Key themes identified
    5. Recovery indicators (if any)

    Respond in a clear, structured format.
    """
    
    headers = {"Content-Type": "application/json"}
    data = {
        "contents": [{
            "parts": [{"text": prompt}]
        }]
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                return result['candidates'][0]['content']['parts'][0]['text']
        return f"Error: {response.status_code} - {response.text}"
    except Exception as e:
        return f"Error: {e}"

def test_mental_health_analysis():
    """Test mental health content analysis"""
    print("🧠 Mental Health Content Analysis Test")
    print("=" * 50)
    
    # Test content
    test_content = """
    I've been struggling with anxiety for the past few months. 
    The panic attacks were getting worse, but I finally started therapy last week. 
    My therapist taught me some breathing techniques that are actually helping. 
    I'm feeling more hopeful about managing this condition.
    """
    
    print("📝 Test Content:")
    print(test_content.strip())
    print("\n🔬 Analyzing...")
    
    result = analyze_mental_health_content_rest(test_content)
    
    print("\n📊 Analysis Result:")
    print("-" * 30)
    print(result)
    
    return "Error:" not in result

if __name__ == "__main__":
    success = test_mental_health_analysis()
    if success:
        print("\n🎉 Mental Health Analysis Test PASSED!")
        print("The system can successfully analyze mental health content!")
    else:
        print("\n❌ Mental Health Analysis Test FAILED!")
        print("Please check the error messages above.")
