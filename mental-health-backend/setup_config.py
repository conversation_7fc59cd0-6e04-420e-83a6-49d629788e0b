"""
安全的配置设置脚本
"""
import os
import shutil
from pathlib import Path

def setup_config():
    """安全地设置配置文件"""
    print("🔧 心理健康内容分析系统 - 配置设置")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = Path.cwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查是否在正确的目录
    if not (current_dir / "app").exists():
        print("❌ 错误: 请确保你在 mental-health-backend 目录中运行此脚本")
        print("💡 请运行: cd mental-health-backend")
        return
    
    env_file = current_dir / ".env"
    template_file = current_dir / ".env.template"
    example_file = current_dir / ".env.example"
    
    # 检查现有配置
    if env_file.exists():
        print(f"📄 发现现有的 .env 文件")
        choice = input("是否要备份现有配置并创建新的? (y/n): ").lower().strip()
        if choice == 'y':
            backup_file = current_dir / f".env.backup.{int(time.time())}"
            shutil.copy2(env_file, backup_file)
            print(f"✅ 已备份到: {backup_file}")
        else:
            print("❌ 取消操作")
            return
    
    # 复制模板文件
    if template_file.exists():
        shutil.copy2(template_file, env_file)
        print(f"✅ 已创建配置文件: {env_file}")
    elif example_file.exists():
        shutil.copy2(example_file, env_file)
        print(f"✅ 已从示例创建配置文件: {env_file}")
    else:
        print("❌ 未找到配置模板文件")
        return
    
    print(f"\n📝 接下来的步骤:")
    print(f"1. 打开配置文件进行编辑:")
    print(f"   code .env          # 如果你使用VS Code")
    print(f"   nano .env          # 如果你使用nano编辑器")
    print(f"   vim .env           # 如果你使用vim编辑器")
    
    print(f"\n2. 需要替换的关键配置:")
    print(f"   🔑 GEMINI_API_KEY=YOUR_GEMINI_API_KEY_HERE")
    print(f"   🔴 REDDIT_CLIENT_ID=your_reddit_client_id_here")
    print(f"   🔴 REDDIT_CLIENT_SECRET=your_reddit_client_secret_here")
    print(f"   📺 YOUTUBE_API_KEY=your_youtube_api_key_here")
    
    print(f"\n3. 获取API密钥的链接:")
    print(f"   🤖 Gemini API: https://makersuite.google.com/app/apikey")
    print(f"   🔴 Reddit API: https://www.reddit.com/prefs/apps")
    print(f"   📺 YouTube API: https://console.cloud.google.com/")
    
    print(f"\n4. 配置完成后测试:")
    print(f"   python check_config.py")
    print(f"   python test_gemini_pro.py")
    
    print(f"\n⚠️  安全提醒:")
    print(f"   - 永远不要将API密钥分享给他人")
    print(f"   - 不要将.env文件提交到Git仓库")
    print(f"   - 定期更换API密钥")

def show_file_locations():
    """显示重要文件的位置"""
    print("\n📂 重要文件位置:")
    
    files_to_check = [
        ".env",
        ".env.template", 
        ".env.example",
        "app/core/config.py",
        "test_gemini_pro.py",
        "check_config.py"
    ]
    
    for file_name in files_to_check:
        file_path = Path(file_name)
        if file_path.exists():
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name} (不存在)")

if __name__ == "__main__":
    import time
    setup_config()
    show_file_locations()
