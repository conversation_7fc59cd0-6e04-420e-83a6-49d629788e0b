#!/usr/bin/env python3
"""
Simple Gemini API test script
"""
import os
import asyncio
from dotenv import load_dotenv
import google.generativeai as genai

# Load environment variables
load_dotenv()

async def test_gemini_connection():
    """Test basic Gemini API connection"""
    print("🧪 Simple Gemini API Test")
    print("=" * 40)
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key or api_key == "YOUR_GEMINI_API_KEY_HERE":
        print("❌ Gemini API key not found or not configured")
        print("Please check your .env file")
        return False
    
    print(f"✅ API key found: {api_key[:10]}...")
    
    # Configure Gemini
    try:
        genai.configure(api_key=api_key)
        print("✅ Gemini configured successfully")
    except Exception as e:
        print(f"❌ Failed to configure Gemini: {e}")
        return False
    
    # Test model
    model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    print(f"🤖 Testing model: {model_name}")
    
    try:
        model = genai.GenerativeModel(model_name)
        print("✅ Model initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize model: {e}")
        return False
    
    # Test simple generation
    try:
        print("🔬 Testing simple text generation...")
        response = model.generate_content("Hello, how are you?")
        print(f"✅ Response received: {response.text[:100]}...")
        return True
    except Exception as e:
        print(f"❌ Failed to generate content: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_gemini_connection())
    if success:
        print("\n🎉 Gemini API test PASSED!")
        print("Your configuration is working correctly.")
    else:
        print("\n❌ Gemini API test FAILED!")
        print("Please check your configuration.")
