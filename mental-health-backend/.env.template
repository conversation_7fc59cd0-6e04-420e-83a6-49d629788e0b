# 心理健康内容分析系统 - 环境配置模板
# 请将此文件复制为 .env 并填入你的实际API密钥

# ===========================================
# AI Provider Configuration (AI服务配置)
# ===========================================
AI_PROVIDER=gemini
# 👇 请将 YOUR_GEMINI_API_KEY_HERE 替换为你的实际Gemini API密钥
GEMINI_API_KEY=YOUR_GEMINI_API_KEY_HERE
GEMINI_MODEL=gemini-1.5-pro

# 备选AI服务（可选）
OPENAI_API_KEY=your_openai_key_if_you_have_one

# ===========================================
# Reddit API Configuration (Reddit API配置)
# ===========================================
# 👇 请将这些替换为你的Reddit API凭据
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USER_AGENT=MentalHealthAnalyzer:v1.0:by-yourusername

# ===========================================
# YouTube API Configuration (YouTube API配置)
# ===========================================
# 👇 请将这个替换为你的YouTube API密钥
YOUTUBE_API_KEY=your_youtube_api_key_here

# ===========================================
# 其他平台API (可选)
# ===========================================
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
TIKTOK_CLIENT_KEY=your_tiktok_client_key
TIKTOK_CLIENT_SECRET=your_tiktok_client_secret
TWITTER_BEARER_TOKEN=your_twitter_bearer_token

# ===========================================
# 数据库配置
# ===========================================
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/mental_health_analyzer
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# ===========================================
# 安全配置
# ===========================================
SECRET_KEY=your-secret-key-change-this-in-production-please
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
ACCESS_TOKEN_EXPIRE_MINUTES=30

# ===========================================
# 应用配置
# ===========================================
ENVIRONMENT=development
DEBUG=true
MAX_CONTENT_LENGTH=10000
BATCH_SIZE=100
RATE_LIMIT_PER_MINUTE=60
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
