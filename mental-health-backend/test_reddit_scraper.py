"""
Test script for Reddit scraper functionality
"""
import asyncio
import os
from dotenv import load_dotenv
from app.services.scrapers.reddit_scraper import RedditScraper

# Load environment variables
load_dotenv()

async def test_reddit_scraper():
    """Test Reddit scraper functionality"""
    print("🚀 Testing Reddit Scraper...")
    
    # Check if Reddit API credentials are available
    if not all([
        os.getenv("REDDIT_CLIENT_ID"),
        os.getenv("REDDIT_CLIENT_SECRET")
    ]):
        print("❌ Reddit API credentials not found in environment variables")
        print("Please set REDDIT_CLIENT_ID and REDDIT_CLIENT_SECRET in your .env file")
        return
    
    try:
        # Initialize scraper
        scraper = RedditScraper()
        print("✅ Reddit scraper initialized successfully")
        
        # Test scraping a single subreddit
        print("\n📊 Testing single subreddit scraping (r/getmotivated)...")
        posts = await scraper.scrape_subreddit(
            subreddit_name="getmotivated",
            keywords=["depression", "anxiety", "mental health", "recovery", "healing"],
            limit=10
        )
        
        print(f"✅ Scraped {len(posts)} posts from r/getmotivated")
        
        if posts:
            # Display sample post
            sample_post = posts[0]
            print(f"\n📝 Sample post:")
            print(f"   Title: {sample_post.get('title', 'N/A')[:100]}...")
            print(f"   Author: {sample_post.get('author', 'N/A')}")
            print(f"   Upvotes: {sample_post.get('likes_count', 0)}")
            print(f"   Comments: {sample_post.get('comments_count', 0)}")
            print(f"   Published: {sample_post.get('published_at', 'N/A')}")
            print(f"   Word count: {sample_post.get('word_count', 0)}")
        
        # Test scraping multiple mental health subreddits
        print(f"\n🧠 Testing mental health subreddits scraping...")
        all_posts = await scraper.scrape_mental_health_subreddits(
            keywords=["recovery", "healing", "therapy", "support"],
            limit_per_subreddit=5
        )
        
        print(f"✅ Scraped {len(all_posts)} total posts from mental health subreddits")
        
        # Analyze content distribution
        if all_posts:
            subreddit_counts = {}
            for post in all_posts:
                subreddit = post.get('subreddit', 'unknown')
                subreddit_counts[subreddit] = subreddit_counts.get(subreddit, 0) + 1
            
            print(f"\n📈 Content distribution by subreddit:")
            for subreddit, count in sorted(subreddit_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"   r/{subreddit}: {count} posts")
        
        # Test comment extraction
        if posts:
            print(f"\n💬 Testing comment extraction...")
            post_id = posts[0].get('platform_id')
            if post_id:
                comments = await scraper.get_post_comments(post_id, limit=5)
                print(f"✅ Extracted {len(comments)} comments for post {post_id}")
                
                if comments:
                    sample_comment = comments[0]
                    print(f"   Sample comment: {sample_comment.get('content_text', 'N/A')[:100]}...")
        
        print(f"\n🎉 Reddit scraper test completed successfully!")
        print(f"   Total posts collected: {len(all_posts)}")
        print(f"   Ready for integration with the main application")
        
    except Exception as e:
        print(f"❌ Error testing Reddit scraper: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_reddit_scraper())
