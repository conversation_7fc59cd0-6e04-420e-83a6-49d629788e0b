"""
Setup script for Mental Health Content Analyzer
"""
import os
import sys
import subprocess
from pathlib import Path

def run_command(command, description):
    """Run a shell command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True

def create_virtual_environment():
    """Create and activate virtual environment"""
    venv_path = Path("venv")
    if venv_path.exists():
        print("📁 Virtual environment already exists")
        return True
    
    return run_command("python -m venv venv", "Creating virtual environment")

def install_dependencies():
    """Install Python dependencies"""
    # Determine the correct pip path based on OS
    if os.name == 'nt':  # Windows
        pip_path = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        pip_path = "venv/bin/pip"
    
    commands = [
        f"{pip_path} install --upgrade pip",
        f"{pip_path} install -r requirements.txt"
    ]
    
    for command in commands:
        if not run_command(command, f"Running: {command}"):
            return False
    return True

def create_env_file():
    """Create .env file from template"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("📄 .env file already exists")
        return True
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    try:
        # Copy .env.example to .env
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file and add your API keys")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def check_database():
    """Check if PostgreSQL is available"""
    print("🗄️  Checking PostgreSQL availability...")
    if run_command("psql --version", "Checking PostgreSQL"):
        print("✅ PostgreSQL is available")
        print("📝 Please create a database named 'mental_health_analyzer'")
        print("   Example: createdb mental_health_analyzer")
        return True
    else:
        print("⚠️  PostgreSQL not found. Please install PostgreSQL:")
        print("   macOS: brew install postgresql")
        print("   Ubuntu: sudo apt-get install postgresql")
        print("   Windows: Download from https://www.postgresql.org/download/")
        return False

def check_redis():
    """Check if Redis is available"""
    print("🔴 Checking Redis availability...")
    if run_command("redis-cli ping", "Checking Redis"):
        print("✅ Redis is available")
        return True
    else:
        print("⚠️  Redis not found. Please install Redis:")
        print("   macOS: brew install redis")
        print("   Ubuntu: sudo apt-get install redis-server")
        print("   Windows: Download from https://redis.io/download")
        return False

def display_next_steps():
    """Display next steps for the user"""
    print("\n🎉 Setup completed! Next steps:")
    print("\n1. 📝 Edit the .env file and add your API keys:")
    print("   - Reddit API credentials (required)")
    print("   - OpenAI API key (required)")
    print("   - YouTube API key (optional)")
    print("   - Other platform API keys (optional)")
    
    print("\n2. 🗄️  Set up your database:")
    print("   - Create PostgreSQL database: createdb mental_health_analyzer")
    print("   - Update DATABASE_URL in .env file")
    
    print("\n3. 🚀 Start the services:")
    print("   - Start Redis: redis-server")
    print("   - Start the application: python -m uvicorn app.main:app --reload")
    
    print("\n4. 🧪 Test Reddit scraper:")
    print("   - Run: python test_reddit_scraper.py")
    
    print("\n5. 📚 API Documentation:")
    print("   - Visit: http://localhost:8000/docs")
    
    print("\n📖 For more information, check the README.md file")

def main():
    """Main setup function"""
    print("🚀 Mental Health Content Analyzer Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    if not create_virtual_environment():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create .env file
    if not create_env_file():
        sys.exit(1)
    
    # Check external dependencies
    check_database()
    check_redis()
    
    # Display next steps
    display_next_steps()
    
    print("\n✅ Setup completed successfully!")

if __name__ == "__main__":
    main()
