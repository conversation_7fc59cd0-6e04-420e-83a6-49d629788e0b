# Environment Configuration
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-here-change-in-production

# Database Configuration
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/mental_health_analyzer
DATABASE_ECHO=false

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Security Configuration
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Provider Configuration
AI_PROVIDER=gemini  # gemini or openai
GEMINI_API_KEY=your-gemini-api-key-here
GEMINI_MODEL=gemini-1.5-pro  # gemini-1.5-pro (付费) 或 gemini-1.5-flash (免费)
OPENAI_API_KEY=your-openai-api-key-here  # 备选

# Reddit API Configuration
REDDIT_CLIENT_ID=your-reddit-client-id
REDDIT_CLIENT_SECRET=your-reddit-client-secret
REDDIT_USER_AGENT=MentalHealthAnalyzer:v1.0:by-your-username

# YouTube API Configuration
YOUTUBE_API_KEY=your-youtube-api-key

# TikTok API Configuration (Optional)
TIKTOK_CLIENT_KEY=your-tiktok-client-key
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret

# Spotify API Configuration (Optional)
SPOTIFY_CLIENT_ID=your-spotify-client-id
SPOTIFY_CLIENT_SECRET=your-spotify-client-secret

# Twitter/X API Configuration (Optional)
TWITTER_BEARER_TOKEN=your-twitter-bearer-token
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret

# Content Processing Configuration
MAX_CONTENT_LENGTH=10000
BATCH_SIZE=100
RATE_LIMIT_PER_MINUTE=60

# File Storage Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB in bytes
