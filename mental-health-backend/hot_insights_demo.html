<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能热点洞察系统演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .criteria {
            background: #e8f4fd;
            border-left: 4px solid #1890ff;
            padding: 15px;
            margin: 15px 0;
        }
        .content-item {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #fafafa;
        }
        .platform-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        .reddit { background: #ff4500; color: white; }
        .youtube { background: #ff0000; color: white; }
        .metrics {
            display: flex;
            gap: 15px;
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .hot-score {
            background: #ff6b6b;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .old-system, .new-system {
            padding: 20px;
            border-radius: 8px;
        }
        .old-system {
            background: #fff2f0;
            border: 1px solid #ffccc7;
        }
        .new-system {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔥 智能热点洞察系统</h1>
        <p>基于多维度算法的心理健康内容热点分析</p>
    </div>

    <div class="section">
        <h2>📊 热点判断规则</h2>
        <div class="criteria">
            <h3>新的智能算法包含以下因素：</h3>
            <ul>
                <li><strong>互动权重</strong>：点赞数 × 1.0 + 评论数 × 2.0 + 浏览数 × 0.001</li>
                <li><strong>参与率</strong>：(点赞 + 评论) / 浏览数</li>
                <li><strong>时效性</strong>：24小时内的内容获得加权，越新越高</li>
                <li><strong>平台特性</strong>：不同平台采用不同的评分标准</li>
                <li><strong>内容质量</strong>：基于关键词匹配和内容长度</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>🎯 热点内容</h2>
        <div style="margin-bottom: 15px;">
            <button onclick="loadHotContent()">刷新热点内容</button>
            <button onclick="loadHotContent('reddit')">仅Reddit</button>
            <button onclick="loadHotContent('youtube')">仅YouTube</button>
            <button onclick="generateSampleData()">生成测试数据</button>
        </div>
        <div id="hotContent" class="loading">点击"刷新热点内容"加载数据...</div>
    </div>

    <div class="section">
        <h2>🔄 系统对比</h2>
        <div class="comparison">
            <div class="old-system">
                <h3>❌ 旧系统问题</h3>
                <ul>
                    <li>完全模拟数据，内容不真实</li>
                    <li>简单按点赞数排序</li>
                    <li>固定的内容模板</li>
                    <li>没有时效性考虑</li>
                    <li>缺乏平台特性</li>
                </ul>
            </div>
            <div class="new-system">
                <h3>✅ 新系统优势</h3>
                <ul>
                    <li>支持真实API数据采集</li>
                    <li>多维度热点算法</li>
                    <li>智能内容生成</li>
                    <li>时效性权重计算</li>
                    <li>平台差异化处理</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        async function loadHotContent(platform = null) {
            const contentDiv = document.getElementById('hotContent');
            contentDiv.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const params = new URLSearchParams({
                    limit: '10',
                    hours: '24'
                });
                if (platform) {
                    params.append('platform', platform);
                }
                
                const response = await fetch(`http://localhost:8000/content/hot?${params}`);
                const data = await response.json();
                
                if (data.content && data.content.length > 0) {
                    contentDiv.innerHTML = `
                        <div style="margin-bottom: 15px; padding: 10px; background: #e6f7ff; border-radius: 5px;">
                            <strong>找到 ${data.hot_total} 条热点内容</strong> (总共 ${data.total} 条)
                        </div>
                        ${data.content.map(item => `
                            <div class="content-item">
                                <div>
                                    <span class="platform-tag ${item.platform}">${item.platform.toUpperCase()}</span>
                                    <span class="hot-score">热度: ${Math.round(item.hot_score || 0)}</span>
                                </div>
                                <h4>${item.title}</h4>
                                <p>${item.content}</p>
                                <div class="metrics">
                                    <span>👍 ${item.upvotes || item.likes || 0}</span>
                                    <span>💬 ${item.comments || 0}</span>
                                    <span>👁️ ${item.views || 'N/A'}</span>
                                    <span>📈 参与率: ${((item.engagement_rate || 0) * 100).toFixed(2)}%</span>
                                    <span>⏰ 时效性: ${((item.recency_factor || 0) * 100).toFixed(1)}%</span>
                                </div>
                                <div style="margin-top: 8px; font-size: 12px; color: #999;">
                                    ${item.author || item.channel} • ${new Date(item.collected_at).toLocaleString()}
                                </div>
                            </div>
                        `).join('')}
                    `;
                } else {
                    contentDiv.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <h3>暂无热点内容</h3>
                            <p>请先生成一些测试数据或等待真实数据采集</p>
                        </div>
                    `;
                }
            } catch (error) {
                contentDiv.innerHTML = `
                    <div style="color: red; padding: 20px; text-align: center;">
                        <h3>加载失败</h3>
                        <p>错误: ${error.message}</p>
                        <p>请确保后端服务器正在运行 (http://localhost:8000)</p>
                    </div>
                `;
            }
        }
        
        async function generateSampleData() {
            try {
                // 创建任务
                const taskResponse = await fetch('http://localhost:8000/tasks', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        name: '热点内容演示',
                        platforms: ['reddit', 'youtube'],
                        keywords: ['mental health', 'therapy', 'wellness'],
                        max_results: 15
                    })
                });
                const task = await taskResponse.json();
                
                // 执行任务
                await fetch(`http://localhost:8000/tasks/${task.id}/execute`, {
                    method: 'POST'
                });
                
                // 等待一下然后刷新
                setTimeout(() => {
                    loadHotContent();
                }, 2000);
                
                alert('测试数据生成中，请稍等...');
            } catch (error) {
                alert('生成测试数据失败: ' + error.message);
            }
        }
    </script>
</body>
</html>
