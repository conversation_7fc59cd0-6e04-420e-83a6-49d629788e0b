#!/usr/bin/env python3
"""
REST API based Gemini test script
"""
import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_gemini_rest():
    """Test Gemini API using REST instead of gRPC"""
    print("🧪 REST-based Gemini API Test")
    print("=" * 40)
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key or api_key == "YOUR_GEMINI_API_KEY_HERE":
        print("❌ Gemini API key not found or not configured")
        print("Please check your .env file")
        return False
    
    print(f"✅ API key found: {api_key[:10]}...")
    
    # Test model
    model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    print(f"🤖 Testing model: {model_name}")
    
    # Prepare REST API request
    url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_name}:generateContent?key={api_key}"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "contents": [{
            "parts": [{
                "text": "Hello! Please respond with a simple greeting."
            }]
        }]
    }
    
    try:
        print("🔬 Testing REST API connection...")
        print(f"📡 Endpoint: {url[:80]}...")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                text = result['candidates'][0]['content']['parts'][0]['text']
                print(f"✅ Response received: {text}")
                return True
            else:
                print(f"❌ Unexpected response format: {result}")
                return False
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out - network connection issue")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gemini_rest()
    if success:
        print("\n🎉 Gemini REST API test PASSED!")
        print("Your configuration is working correctly.")
    else:
        print("\n❌ Gemini REST API test FAILED!")
        print("Please check your network connection and API key.")
