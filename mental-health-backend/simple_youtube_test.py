#!/usr/bin/env python3
"""
Simple YouTube API test
"""
import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_youtube_api():
    """Test YouTube Data API"""
    print("📺 Testing YouTube Data API...")
    print("=" * 40)
    
    # Check API key
    api_key = os.getenv("YOUTUBE_API_KEY")
    
    if not api_key or api_key == "your_youtube_api_key_here":
        print("❌ YouTube API key not configured")
        print("Please set YOUTUBE_API_KEY in your .env file")
        print("Get API key from: https://console.developers.google.com/")
        return False
    
    print(f"✅ API Key: {api_key[:10]}...")
    
    try:
        # Test API with a simple search
        url = "https://www.googleapis.com/youtube/v3/search"
        params = {
            "part": "snippet",
            "q": "mental health motivation",
            "type": "video",
            "maxResults": 5,
            "key": api_key
        }
        
        print("\n🔍 Searching for mental health videos...")
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            videos = data.get("items", [])
            
            print(f"✅ Found {len(videos)} videos")
            
            for i, video in enumerate(videos[:3], 1):
                snippet = video["snippet"]
                print(f"\n📹 Video {i}:")
                print(f"   Title: {snippet['title'][:60]}...")
                print(f"   Channel: {snippet['channelTitle']}")
                print(f"   Published: {snippet['publishedAt'][:10]}")
                print(f"   Video ID: {video['id']['videoId']}")
            
            return True
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ YouTube API test failed: {e}")
        return False

def test_youtube_video_details():
    """Test getting video details"""
    print("\n📊 Testing Video Details API...")
    print("=" * 40)
    
    api_key = os.getenv("YOUTUBE_API_KEY")
    
    if not api_key or api_key == "your_youtube_api_key_here":
        print("❌ YouTube API key not configured")
        return False
    
    try:
        # Use a known mental health video ID for testing
        # This is a public video about mental health awareness
        video_id = "DxIDKZHW3-E"  # Example video ID
        
        url = "https://www.googleapis.com/youtube/v3/videos"
        params = {
            "part": "snippet,statistics",
            "id": video_id,
            "key": api_key
        }
        
        print(f"🔍 Getting details for video: {video_id}")
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            videos = data.get("items", [])
            
            if videos:
                video = videos[0]
                snippet = video["snippet"]
                stats = video.get("statistics", {})
                
                print("✅ Video details retrieved:")
                print(f"   Title: {snippet['title'][:60]}...")
                print(f"   Channel: {snippet['channelTitle']}")
                print(f"   Views: {stats.get('viewCount', 'N/A')}")
                print(f"   Likes: {stats.get('likeCount', 'N/A')}")
                print(f"   Comments: {stats.get('commentCount', 'N/A')}")
                
                return True
            else:
                print("❌ No video found with that ID")
                return False
        else:
            print(f"❌ API request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Video details test failed: {e}")
        return False

if __name__ == "__main__":
    print("📺 YouTube API Test Suite")
    print("=" * 50)
    
    # Test basic search
    search_success = test_youtube_api()
    
    if search_success:
        # Test video details
        details_success = test_youtube_video_details()
        
        if details_success:
            print("\n🎉 All YouTube tests PASSED!")
            print("YouTube data collection is ready to use!")
        else:
            print("\n⚠️  Search works, but video details test failed")
    else:
        print("\n❌ YouTube API tests FAILED!")
        print("Please check your YouTube API key")
