"""
Enhanced test script for Gemini Pro AI analyzer
"""
import asyncio
import os
import time
from dotenv import load_dotenv
from app.services.analyzers.ai_analyzer_factory import UnifiedAIAnalyzer

# Load environment variables
load_dotenv()

async def test_gemini_pro_performance():
    """Test Gemini Pro performance and capabilities"""
    print("🚀 Testing Gemini Pro AI Analyzer...")
    print("=" * 60)
    
    # Check configuration
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Gemini API key not found in environment variables")
        print("Please set GEMINI_API_KEY in your .env file")
        print("Get your API key from: https://makersuite.google.com/app/apikey")
        return
    
    model = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    print(f"🤖 Using model: {model}")
    
    if "pro" in model.lower():
        print("✨ Pro model detected - Enhanced capabilities enabled!")
    else:
        print("⚡ Flash model detected - Fast processing mode")
    
    try:
        # Initialize unified analyzer
        analyzer = UnifiedAIAnalyzer()
        print(f"✅ Initialized {analyzer.provider} analyzer")
        
        # Get detailed provider info
        provider_info = analyzer.get_provider_info()
        print(f"\n📋 Provider Information:")
        print(f"   Model: {provider_info['model']}")
        print(f"   Tier: {provider_info.get('model_tier', 'unknown')}")
        print(f"   Rate limits: {provider_info['rate_limits']['requests_per_minute']}/min")
        print(f"   Batch size: {provider_info['rate_limits']['batch_size']}")
        print(f"   Max tokens: {provider_info['rate_limits'].get('max_tokens', 'N/A')}")
        
        # Complex test content for Pro model
        complex_test_content = """
        My journey with bipolar disorder has been like riding an emotional roller coaster for the past five years. 
        The manic episodes were initially exhilarating - I felt invincible, creative, and could go days without sleep. 
        I'd start multiple projects, spend money recklessly, and make impulsive decisions that seemed brilliant at the time.
        
        But then came the crashes. The depressive episodes were devastating. I'd spend weeks in bed, unable to shower, 
        eat, or even answer my phone. I lost jobs, relationships, and nearly lost my life to suicide attempts twice.
        
        The turning point came when my sister staged an intervention. She had researched bipolar disorder extensively 
        and helped me find a psychiatrist who specialized in mood disorders. Getting the right diagnosis was crucial - 
        I had been misdiagnosed with regular depression for years.
        
        Finding the right medication combination took months of trial and error. Lithium helped stabilize my mood, 
        but the side effects were challenging. We added lamotrigine, which made a huge difference. I also started 
        cognitive behavioral therapy to learn coping strategies and recognize early warning signs of episodes.
        
        Building a support system was essential. I joined a bipolar support group where I met others who truly 
        understood what I was going through. I learned about sleep hygiene, stress management, and the importance 
        of routine. I had to give up alcohol completely, which was difficult but necessary.
        
        Today, three years into proper treatment, I'm in the longest stable period of my adult life. I have a job 
        I love, healthy relationships, and most importantly, hope for the future. I still have challenging days, 
        but I have tools to manage them. Recovery isn't linear, but it's possible.
        
        To anyone struggling with bipolar disorder: please don't give up. Find the right healthcare team, be patient 
        with the medication process, and remember that you're not alone. There's life beyond the diagnosis.
        """
        
        print(f"\n📝 Testing with complex bipolar disorder recovery story...")
        print(f"   Content length: {len(complex_test_content)} characters")
        
        # Test comprehensive analysis with timing
        start_time = time.time()
        
        print(f"\n🔬 Running comprehensive analysis...")
        comprehensive_result = await analyzer.analyze_content(
            complex_test_content,
            analysis_types=["classification", "sentiment"]
        )
        
        analysis_time = time.time() - start_time
        print(f"⏱️  Analysis completed in {analysis_time:.2f} seconds")
        
        # Display classification results
        if comprehensive_result["classification"]:
            cls = comprehensive_result["classification"]
            print(f"\n🎯 Classification Results:")
            print(f"   Primary category: {cls.get('primary_category', 'unknown')}")
            print(f"   Secondary categories: {cls.get('secondary_categories', [])}")
            print(f"   Narrative structure: {cls.get('narrative_structure', 'unknown')}")
            print(f"   Confidence score: {cls.get('confidence_score', 0.0):.2f}")
            
            # Show key themes
            themes = cls.get('key_themes', [])
            if themes:
                print(f"   Key themes: {', '.join(themes)}")
            
            # Show story elements
            elements = cls.get('story_elements', {})
            if elements:
                print(f"   Story elements:")
                for key, value in elements.items():
                    if value:
                        print(f"     ✓ {key.replace('_', ' ').title()}")
            
            # Show emotional arc
            emotional_arc = cls.get('emotional_arc', {})
            if emotional_arc:
                print(f"   Emotional arc:")
                print(f"     Starting: {emotional_arc.get('starting_emotion', 'unknown')}")
                print(f"     Lowest: {emotional_arc.get('lowest_point', 'unknown')}")
                print(f"     Turning: {emotional_arc.get('turning_point', 'unknown')}")
                print(f"     Ending: {emotional_arc.get('ending_emotion', 'unknown')}")
        
        # Display sentiment results
        if comprehensive_result["sentiment"]:
            sent = comprehensive_result["sentiment"]
            print(f"\n💭 Sentiment Analysis Results:")
            print(f"   Overall sentiment: {sent.get('overall_sentiment', 'unknown')}")
            print(f"   Sentiment score: {sent.get('sentiment_score', 0.0):.2f}")
            print(f"   Emotional intensity: {sent.get('emotional_intensity', 0.0):.2f}")
            print(f"   Primary emotions: {', '.join(sent.get('primary_emotions', []))}")
            
            # Show detailed indicators
            hope = sent.get('hope_indicators', {})
            distress = sent.get('distress_indicators', {})
            risk = sent.get('risk_factors', {})
            recovery = sent.get('recovery_indicators', {})
            
            print(f"   Hope score: {hope.get('hope_score', 0.0):.2f}")
            print(f"   Distress level: {distress.get('distress_level', 'unknown')}")
            print(f"   Suicide risk: {risk.get('suicide_risk', 'unknown')}")
            print(f"   Recovery stage: {recovery.get('recovery_stage', 'unknown')}")
            print(f"   Shows progress: {recovery.get('shows_progress', False)}")
            
            # Show coping strategies if found
            resilience = sent.get('resilience_factors', {})
            strategies = resilience.get('coping_strategies', [])
            if strategies:
                print(f"   Coping strategies identified: {', '.join(strategies[:3])}")
        
        # Test batch processing with Pro capabilities
        if "pro" in model.lower():
            print(f"\n📦 Testing Pro batch processing capabilities...")
            
            batch_contents = [
                "I've been dealing with severe anxiety for months. Panic attacks are ruining my life.",
                "Therapy changed everything for me. I learned that healing is possible with the right support.",
                "Lost my job due to depression. Feeling hopeless and don't know where to turn.",
                "Celebrating 6 months of sobriety today! Recovery is hard but worth it.",
                "Social anxiety makes it impossible to make friends. I feel so isolated and lonely."
            ]
            
            batch_start = time.time()
            batch_results = await analyzer.batch_analyze_content(
                batch_contents,
                analysis_types=["classification"]
            )
            batch_time = time.time() - batch_start
            
            print(f"⏱️  Batch analysis of {len(batch_contents)} items completed in {batch_time:.2f} seconds")
            print(f"📊 Batch Results Summary:")
            
            for i, result in enumerate(batch_results):
                if result["classification"]:
                    category = result["classification"].get("primary_category", "unknown")
                    confidence = result["classification"].get("confidence_score", 0.0)
                    print(f"   {i+1}. {category} (confidence: {confidence:.2f})")
                else:
                    print(f"   {i+1}. Analysis failed")
        
        # Performance summary
        print(f"\n📈 Performance Summary:")
        print(f"   Model: {provider_info['model']}")
        print(f"   Single analysis time: {analysis_time:.2f}s")
        if "pro" in model.lower():
            avg_batch_time = batch_time / len(batch_contents)
            print(f"   Average batch item time: {avg_batch_time:.2f}s")
            print(f"   Batch efficiency: {analysis_time / avg_batch_time:.1f}x faster")
        
        print(f"\n🎉 Gemini Pro test completed successfully!")
        print(f"   All advanced features working properly")
        print(f"   Ready for production use with enhanced capabilities")
        
    except Exception as e:
        print(f"❌ Error testing Gemini Pro: {e}")
        import traceback
        traceback.print_exc()

async def test_model_comparison():
    """Compare different Gemini models if available"""
    print(f"\n🔄 Model Comparison Test")
    print("=" * 40)
    
    # Test content
    test_content = "I've been struggling with depression but therapy is helping me recover slowly."
    
    models_to_test = ["gemini-1.5-flash", "gemini-1.5-pro"]
    
    for model in models_to_test:
        print(f"\n🧪 Testing {model}...")
        
        # Temporarily set model
        os.environ["GEMINI_MODEL"] = model
        
        try:
            analyzer = UnifiedAIAnalyzer()
            start_time = time.time()
            
            result = await analyzer.analyze_content(test_content, ["classification"])
            
            end_time = time.time()
            
            if result["classification"]:
                category = result["classification"].get("primary_category", "unknown")
                confidence = result["classification"].get("confidence_score", 0.0)
                print(f"   ✅ Category: {category}")
                print(f"   ✅ Confidence: {confidence:.2f}")
                print(f"   ✅ Time: {end_time - start_time:.2f}s")
            else:
                print(f"   ❌ Analysis failed")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_gemini_pro_performance())
    
    # Uncomment to run model comparison
    # asyncio.run(test_model_comparison())
