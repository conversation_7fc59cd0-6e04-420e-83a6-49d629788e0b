"""
Task management API endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.models.task import Task, TaskStatus, TaskType
from app.schemas.task import TaskCreate, TaskUpdate, TaskResponse, TaskListResponse
from app.services.task_service import TaskService

router = APIRouter()


@router.post("/", response_model=TaskResponse)
async def create_task(
    task_data: TaskCreate,
    db: AsyncSession = Depends(get_db)
):
    """Create a new collection or analysis task"""
    task_service = TaskService(db)
    task = await task_service.create_task(task_data)
    return task


@router.get("/", response_model=TaskListResponse)
async def get_tasks(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[TaskStatus] = None,
    task_type: Optional[TaskType] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get list of tasks with optional filtering"""
    task_service = TaskService(db)
    tasks, total = await task_service.get_tasks(
        skip=skip, 
        limit=limit, 
        status=status, 
        task_type=task_type
    )
    return TaskListResponse(
        tasks=tasks,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Get task details by ID"""
    task_service = TaskService(db)
    task = await task_service.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task


@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: int,
    task_data: TaskUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Update task configuration"""
    task_service = TaskService(db)
    task = await task_service.update_task(task_id, task_data)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task


@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Delete a task"""
    task_service = TaskService(db)
    success = await task_service.delete_task(task_id)
    if not success:
        raise HTTPException(status_code=404, detail="Task not found")
    return {"message": "Task deleted successfully"}


@router.post("/{task_id}/start")
async def start_task(
    task_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Start a task execution"""
    task_service = TaskService(db)
    task = await task_service.start_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return {"message": "Task started successfully", "task_id": task_id}


@router.post("/{task_id}/pause")
async def pause_task(
    task_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Pause a running task"""
    task_service = TaskService(db)
    task = await task_service.pause_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return {"message": "Task paused successfully", "task_id": task_id}


@router.post("/{task_id}/stop")
async def stop_task(
    task_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Stop a running task"""
    task_service = TaskService(db)
    task = await task_service.stop_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return {"message": "Task stopped successfully", "task_id": task_id}


@router.get("/{task_id}/status")
async def get_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_db)
):
    """Get current task status and progress"""
    task_service = TaskService(db)
    status = await task_service.get_task_status(task_id)
    if not status:
        raise HTTPException(status_code=404, detail="Task not found")
    return status


@router.get("/{task_id}/logs")
async def get_task_logs(
    task_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    level: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get task execution logs"""
    task_service = TaskService(db)
    logs = await task_service.get_task_logs(
        task_id=task_id,
        skip=skip,
        limit=limit,
        level=level
    )
    return logs
