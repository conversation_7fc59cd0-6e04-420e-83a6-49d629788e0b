"""
Main API router
"""
from fastapi import APIRouter

from app.api.v1.endpoints import auth, tasks, content, analysis, platforms, export

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(content.router, prefix="/content", tags=["content"])
api_router.include_router(analysis.router, prefix="/analysis", tags=["analysis"])
api_router.include_router(platforms.router, prefix="/platforms", tags=["platforms"])
api_router.include_router(export.router, prefix="/export", tags=["export"])
