"""
Analysis models for storing AI analysis results
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any

from app.core.database import Base


class AnalysisResult(Base):
    """Model for storing AI analysis results"""
    
    __tablename__ = "analysis_results"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Content reference
    content_id = Column(Integer, ForeignKey("contents.id"), nullable=False, index=True)
    
    # Analysis type and version
    analysis_type = Column(String(100), nullable=False)  # sentiment, story_classification, etc.
    model_version = Column(String(50), nullable=False)
    confidence_score = Column(Float, nullable=True)
    
    # Analysis results
    result_data = Column(JSON, nullable=False)  # Main analysis results
    metadata = Column(JSON, nullable=True)  # Additional metadata
    
    # Processing info
    processing_time_ms = Column(Integer, nullable=True)
    tokens_used = Column(Integer, nullable=True)  # For API-based models
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    content = relationship("Content", back_populates="analysis_results")
    
    def __repr__(self):
        return f"<AnalysisResult(id={self.id}, content_id={self.content_id}, type={self.analysis_type})>"


class StoryPattern(Base):
    """Model for storing identified story patterns"""
    
    __tablename__ = "story_patterns"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Pattern identification
    pattern_name = Column(String(255), nullable=False, index=True)
    pattern_type = Column(String(100), nullable=False)  # narrative_structure, emotional_arc, etc.
    category = Column(String(100), nullable=False, index=True)  # depression, anxiety, etc.
    
    # Pattern characteristics
    description = Column(Text, nullable=True)
    key_elements = Column(JSON, nullable=False)  # List of key elements
    emotional_progression = Column(JSON, nullable=True)  # Emotional arc data
    language_patterns = Column(JSON, nullable=True)  # Common phrases, words
    
    # Effectiveness metrics
    engagement_score = Column(Float, default=0.0)  # Average engagement
    viral_score = Column(Float, default=0.0)  # Viral potential
    authenticity_score = Column(Float, default=0.0)  # Perceived authenticity
    
    # Usage statistics
    occurrence_count = Column(Integer, default=0)
    success_rate = Column(Float, default=0.0)  # Success in engagement
    
    # Timestamps
    first_seen_at = Column(DateTime, default=func.now())
    last_seen_at = Column(DateTime, default=func.now())
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<StoryPattern(id={self.id}, name='{self.pattern_name}', category={self.category})>"


class TrendAnalysis(Base):
    """Model for storing trend analysis results"""
    
    __tablename__ = "trend_analyses"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Trend identification
    trend_name = Column(String(255), nullable=False)
    trend_type = Column(String(100), nullable=False)  # topic, narrative, engagement
    category = Column(String(100), nullable=False, index=True)
    
    # Time period
    period_start = Column(DateTime, nullable=False)
    period_end = Column(DateTime, nullable=False)
    
    # Trend data
    trend_data = Column(JSON, nullable=False)  # Time series data
    peak_value = Column(Float, nullable=True)
    peak_date = Column(DateTime, nullable=True)
    growth_rate = Column(Float, nullable=True)  # Percentage growth
    
    # Platform breakdown
    platform_distribution = Column(JSON, nullable=True)  # Distribution across platforms
    
    # Insights
    insights = Column(JSON, nullable=True)  # Key insights and patterns
    recommendations = Column(JSON, nullable=True)  # Content recommendations
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<TrendAnalysis(id={self.id}, name='{self.trend_name}', category={self.category})>"


class ContentRecommendation(Base):
    """Model for storing content creation recommendations"""
    
    __tablename__ = "content_recommendations"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Recommendation metadata
    title = Column(String(255), nullable=False)
    category = Column(String(100), nullable=False, index=True)
    target_platform = Column(String(50), nullable=False)
    
    # Recommendation details
    story_structure = Column(JSON, nullable=False)  # Recommended structure
    key_elements = Column(JSON, nullable=False)  # Must-have elements
    tone_guidelines = Column(JSON, nullable=True)  # Tone and style
    length_recommendation = Column(JSON, nullable=True)  # Optimal length
    
    # Supporting data
    success_examples = Column(JSON, nullable=True)  # Example content IDs
    trend_data = Column(JSON, nullable=True)  # Supporting trend analysis
    
    # Effectiveness prediction
    predicted_engagement = Column(Float, nullable=True)
    confidence_level = Column(Float, nullable=True)
    
    # Usage tracking
    times_used = Column(Integer, default=0)
    success_rate = Column(Float, nullable=True)
    
    # Timestamps
    valid_until = Column(DateTime, nullable=True)  # Recommendation expiry
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<ContentRecommendation(id={self.id}, title='{self.title}', category={self.category})>"
