"""
User model for authentication and authorization
"""
from sqlalchemy import <PERSON>um<PERSON>, Integer, String, DateTime, Boolean, JSON
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any

from app.core.database import Base


class User(Base):
    """Model for user authentication and management"""
    
    __tablename__ = "users"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Authentication
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(100), unique=True, nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    
    # Profile information
    full_name = Column(String(255), nullable=True)
    organization = Column(String(255), nullable=True)
    role = Column(String(50), default="user")  # user, admin, analyst
    
    # Account status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_superuser = Column(Boolean, default=False)
    
    # Preferences
    preferences = Column(JSON, nullable=True)  # User preferences
    api_quota = Column(Integer, default=1000)  # Monthly API quota
    api_usage = Column(Integer, default=0)  # Current month usage
    
    # Timestamps
    last_login_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @property
    def is_quota_exceeded(self) -> bool:
        """Check if user has exceeded API quota"""
        return self.api_usage >= self.api_quota
    
    def increment_api_usage(self, count: int = 1):
        """Increment API usage counter"""
        self.api_usage += count


class APIKey(Base):
    """Model for API key management"""
    
    __tablename__ = "api_keys"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Key information
    key_hash = Column(String(255), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(String(500), nullable=True)
    
    # User reference
    user_id = Column(Integer, nullable=False, index=True)
    
    # Permissions
    permissions = Column(JSON, nullable=True)  # List of allowed operations
    rate_limit = Column(Integer, default=100)  # Requests per hour
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime, nullable=True)
    
    # Timestamps
    expires_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, name='{self.name}', user_id={self.user_id})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if API key is expired"""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if API key is valid for use"""
        return self.is_active and not self.is_expired
