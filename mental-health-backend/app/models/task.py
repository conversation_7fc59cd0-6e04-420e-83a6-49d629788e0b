"""
Task model for managing data collection and analysis tasks
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List

from app.core.database import Base


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class TaskType(str, Enum):
    """Task type enumeration"""
    COLLECTION = "collection"
    ANALYSIS = "analysis"
    EXPORT = "export"
    MONITORING = "monitoring"


class Task(Base):
    """Model for managing collection and analysis tasks"""
    
    __tablename__ = "tasks"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Task metadata
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    task_type = Column(SQLEnum(TaskType), nullable=False, default=TaskType.COLLECTION)
    status = Column(SQLEnum(TaskStatus), nullable=False, default=TaskStatus.PENDING, index=True)
    
    # Task configuration
    platforms = Column(JSON, nullable=False)  # List of platforms to collect from
    keywords = Column(JSON, nullable=True)  # Keywords to search for
    filters = Column(JSON, nullable=True)  # Collection filters
    analysis_config = Column(JSON, nullable=True)  # Analysis configuration
    
    # Execution details
    celery_task_id = Column(String(255), nullable=True, index=True)
    progress_percentage = Column(Integer, default=0)
    items_collected = Column(Integer, default=0)
    items_analyzed = Column(Integer, default=0)
    items_failed = Column(Integer, default=0)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    
    # Scheduling
    is_recurring = Column(Boolean, default=False)
    schedule_config = Column(JSON, nullable=True)  # Cron-like schedule
    next_run_at = Column(DateTime, nullable=True)
    
    # Results
    results_summary = Column(JSON, nullable=True)
    export_urls = Column(JSON, nullable=True)  # URLs to exported files
    
    # Timestamps
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    contents = relationship("Content", back_populates="task")
    logs = relationship("TaskLog", back_populates="task")
    
    def __repr__(self):
        return f"<Task(id={self.id}, name='{self.name}', status={self.status})>"
    
    @property
    def is_active(self) -> bool:
        """Check if task is currently active"""
        return self.status in [TaskStatus.PENDING, TaskStatus.RUNNING]
    
    @property
    def is_completed(self) -> bool:
        """Check if task is completed"""
        return self.status == TaskStatus.COMPLETED
    
    @property
    def is_failed(self) -> bool:
        """Check if task has failed"""
        return self.status == TaskStatus.FAILED
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate of the task"""
        total_items = self.items_collected + self.items_failed
        if total_items == 0:
            return 0.0
        return (self.items_collected / total_items) * 100
    
    def update_progress(self, collected: int = 0, analyzed: int = 0, failed: int = 0):
        """Update task progress"""
        self.items_collected += collected
        self.items_analyzed += analyzed
        self.items_failed += failed
        
        # Calculate progress percentage
        total_expected = self.items_collected + self.items_failed
        if total_expected > 0:
            self.progress_percentage = min(100, int((self.items_collected / total_expected) * 100))


class TaskLog(Base):
    """Model for storing task execution logs"""
    
    __tablename__ = "task_logs"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Log metadata
    task_id = Column(Integer, nullable=False, index=True)
    level = Column(String(20), nullable=False)  # INFO, WARNING, ERROR, DEBUG
    message = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    task = relationship("Task", back_populates="logs")
    
    def __repr__(self):
        return f"<TaskLog(id={self.id}, task_id={self.task_id}, level={self.level})>"
