"""
Content model for storing collected social media content
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any

from app.core.database import Base


class Content(Base):
    """Model for storing collected content from various platforms"""
    
    __tablename__ = "contents"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Platform information
    platform = Column(String(50), nullable=False, index=True)  # reddit, youtube, tiktok, podcast
    platform_id = Column(String(255), nullable=False, index=True)  # unique ID on the platform
    platform_url = Column(String(1000), nullable=True)
    
    # Content metadata
    title = Column(String(500), nullable=True)
    content_text = Column(Text, nullable=True)
    content_type = Column(String(50), nullable=False)  # post, video, audio, comment
    author = Column(String(255), nullable=True)
    author_id = Column(String(255), nullable=True)
    
    # Engagement metrics
    likes_count = Column(Integer, default=0)
    comments_count = Column(Integer, default=0)
    shares_count = Column(Integer, default=0)
    views_count = Column(Integer, default=0)
    engagement_rate = Column(Float, default=0.0)
    
    # Content analysis
    sentiment_score = Column(Float, nullable=True)  # -1 to 1
    story_category = Column(String(100), nullable=True)  # depression, anxiety, etc.
    narrative_structure = Column(String(100), nullable=True)  # hero_journey, problem_solution, etc.
    emotional_arc = Column(JSON, nullable=True)  # emotional progression data
    
    # Quality metrics
    authenticity_score = Column(Float, default=0.0)  # 0 to 1
    quality_score = Column(Float, default=0.0)  # 0 to 1
    viral_potential = Column(Float, default=0.0)  # 0 to 1
    
    # Content flags
    is_verified = Column(Boolean, default=False)
    is_monetized = Column(Boolean, default=False)
    has_trigger_warning = Column(Boolean, default=False)
    is_professional_content = Column(Boolean, default=False)
    
    # Additional metadata
    language = Column(String(10), default="en")
    duration_seconds = Column(Integer, nullable=True)  # for audio/video content
    word_count = Column(Integer, nullable=True)
    hashtags = Column(JSON, nullable=True)
    mentions = Column(JSON, nullable=True)
    
    # Timestamps
    published_at = Column(DateTime, nullable=True)
    collected_at = Column(DateTime, default=func.now())
    analyzed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Foreign keys
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=True)
    
    # Relationships
    task = relationship("Task", back_populates="contents")
    analysis_results = relationship("AnalysisResult", back_populates="content")
    
    def __repr__(self):
        return f"<Content(id={self.id}, platform={self.platform}, title='{self.title[:50]}...')>"
    
    @property
    def engagement_metrics(self) -> Dict[str, Any]:
        """Get engagement metrics as dictionary"""
        return {
            "likes": self.likes_count,
            "comments": self.comments_count,
            "shares": self.shares_count,
            "views": self.views_count,
            "engagement_rate": self.engagement_rate
        }
    
    @property
    def analysis_summary(self) -> Dict[str, Any]:
        """Get analysis results summary"""
        return {
            "sentiment_score": self.sentiment_score,
            "story_category": self.story_category,
            "narrative_structure": self.narrative_structure,
            "authenticity_score": self.authenticity_score,
            "quality_score": self.quality_score,
            "viral_potential": self.viral_potential
        }
