"""
YouTube data scraper for mental health content
"""
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from loguru import logger

from app.core.config import settings
from app.services.scrapers.base_scraper import BaseScraper


class YouTubeScraper(BaseScraper):
    """YouTube content scraper using YouTube Data API v3"""
    
    def __init__(self):
        super().__init__("youtube")
        self.youtube = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize YouTube API client"""
        try:
            self.youtube = build('youtube', 'v3', developerKey=settings.YOUTUBE_API_KEY)
            logger.info("YouTube API client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize YouTube API client: {e}")
            raise
    
    async def search_videos(
        self,
        query: str,
        max_results: int = 50,
        published_after: Optional[datetime] = None,
        order: str = "relevance"
    ) -> List[Dict[str, Any]]:
        """
        Search for videos using YouTube API
        
        Args:
            query: Search query
            max_results: Maximum number of results
            published_after: Only return videos published after this date
            order: Sort order (relevance, date, rating, viewCount, title)
        
        Returns:
            List of video data dictionaries
        """
        try:
            search_params = {
                'part': 'snippet',
                'q': query,
                'type': 'video',
                'maxResults': min(max_results, 50),  # API limit
                'order': order,
                'regionCode': 'US',
                'relevanceLanguage': 'en'
            }
            
            if published_after:
                search_params['publishedAfter'] = published_after.isoformat() + 'Z'
            
            search_response = self.youtube.search().list(**search_params).execute()
            
            video_ids = [item['id']['videoId'] for item in search_response['items']]
            
            # Get detailed video information
            videos_data = await self._get_video_details(video_ids)
            
            logger.info(f"Found {len(videos_data)} videos for query: {query}")
            return videos_data
            
        except HttpError as e:
            logger.error(f"YouTube API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error searching YouTube videos: {e}")
            return []
    
    async def _get_video_details(self, video_ids: List[str]) -> List[Dict[str, Any]]:
        """Get detailed information for a list of video IDs"""
        if not video_ids:
            return []
        
        try:
            # Get video details
            videos_response = self.youtube.videos().list(
                part='snippet,statistics,contentDetails',
                id=','.join(video_ids)
            ).execute()
            
            videos_data = []
            
            for video in videos_response['items']:
                video_data = await self._extract_video_data(video)
                if video_data:
                    videos_data.append(video_data)
            
            return videos_data
            
        except Exception as e:
            logger.error(f"Error getting video details: {e}")
            return []
    
    async def _extract_video_data(self, video: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract relevant data from YouTube video response"""
        try:
            snippet = video['snippet']
            statistics = video.get('statistics', {})
            content_details = video.get('contentDetails', {})
            
            # Parse duration
            duration_seconds = self._parse_duration(content_details.get('duration', ''))
            
            # Parse published date
            published_at = datetime.fromisoformat(snippet['publishedAt'].replace('Z', '+00:00'))
            
            # Calculate engagement rate
            view_count = int(statistics.get('viewCount', 0))
            like_count = int(statistics.get('likeCount', 0))
            comment_count = int(statistics.get('commentCount', 0))
            
            engagement_rate = self.calculate_engagement_rate(
                likes=like_count,
                comments=comment_count,
                shares=0,  # YouTube doesn't provide share count
                views=view_count
            )
            
            # Extract content text (title + description)
            title = snippet.get('title', '')
            description = snippet.get('description', '')
            content_text = f"{title}\n\n{description}"
            
            video_data = {
                "platform": "youtube",
                "platform_id": video['id'],
                "platform_url": f"https://www.youtube.com/watch?v={video['id']}",
                "title": title,
                "content_text": self.clean_text(content_text),
                "content_type": "video",
                "author": snippet.get('channelTitle', ''),
                "author_id": snippet.get('channelId', ''),
                "likes_count": like_count,
                "comments_count": comment_count,
                "shares_count": 0,
                "views_count": view_count,
                "engagement_rate": engagement_rate,
                "published_at": published_at,
                "collected_at": datetime.utcnow(),
                "language": snippet.get('defaultLanguage', 'en'),
                "duration_seconds": duration_seconds,
                "word_count": len(content_text.split()) if content_text else 0,
                "hashtags": self.extract_hashtags(content_text),
                "category_id": snippet.get('categoryId'),
                "tags": snippet.get('tags', []),
                "thumbnail_url": snippet.get('thumbnails', {}).get('high', {}).get('url'),
                "live_broadcast_content": snippet.get('liveBroadcastContent'),
                "definition": content_details.get('definition'),
                "caption": content_details.get('caption')
            }
            
            return video_data
            
        except Exception as e:
            logger.error(f"Error extracting video data: {e}")
            return None
    
    def _parse_duration(self, duration_str: str) -> int:
        """Parse YouTube duration format (PT4M13S) to seconds"""
        if not duration_str:
            return 0
        
        try:
            import re
            pattern = r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?'
            match = re.match(pattern, duration_str)
            
            if not match:
                return 0
            
            hours = int(match.group(1) or 0)
            minutes = int(match.group(2) or 0)
            seconds = int(match.group(3) or 0)
            
            return hours * 3600 + minutes * 60 + seconds
            
        except Exception:
            return 0
    
    async def search_mental_health_content(
        self,
        limit_per_query: int = 25
    ) -> List[Dict[str, Any]]:
        """
        Search for mental health related content on YouTube
        
        Args:
            limit_per_query: Limit per search query
        
        Returns:
            Combined list of videos from all searches
        """
        # Mental health search queries
        search_queries = [
            "depression recovery story",
            "anxiety healing journey", 
            "mental health transformation",
            "overcoming depression",
            "anxiety management tips",
            "therapy success story",
            "mental health awareness",
            "depression motivation",
            "anxiety support",
            "mental wellness journey",
            "healing from trauma",
            "bipolar disorder story",
            "PTSD recovery",
            "suicide prevention",
            "self care mental health",
            "mindfulness meditation",
            "therapy session",
            "psychiatrist advice",
            "mental health vlog",
            "depression help"
        ]
        
        all_videos = []
        
        for query in search_queries:
            try:
                videos = await self.search_videos(
                    query=query,
                    max_results=limit_per_query,
                    published_after=datetime.now() - timedelta(days=30),  # Last 30 days
                    order="relevance"
                )
                
                # Filter for mental health relevance
                relevant_videos = [
                    video for video in videos 
                    if self.is_mental_health_related(video.get('content_text', ''))
                ]
                
                all_videos.extend(relevant_videos)
                
                # Rate limiting
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error searching for query '{query}': {e}")
                continue
        
        # Remove duplicates based on video ID
        unique_videos = {}
        for video in all_videos:
            video_id = video.get('platform_id')
            if video_id and video_id not in unique_videos:
                unique_videos[video_id] = video
        
        final_videos = list(unique_videos.values())
        logger.info(f"Total unique YouTube videos collected: {len(final_videos)}")
        
        return final_videos
    
    async def get_video_comments(
        self,
        video_id: str,
        max_results: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get comments for a specific video
        
        Args:
            video_id: YouTube video ID
            max_results: Maximum number of comments to retrieve
        
        Returns:
            List of comment data dictionaries
        """
        try:
            comments_response = self.youtube.commentThreads().list(
                part='snippet',
                videoId=video_id,
                maxResults=min(max_results, 100),  # API limit
                order='relevance'
            ).execute()
            
            comments_data = []
            
            for item in comments_response['items']:
                try:
                    comment = item['snippet']['topLevelComment']['snippet']
                    
                    comment_data = {
                        "platform": "youtube",
                        "platform_id": item['id'],
                        "platform_url": f"https://www.youtube.com/watch?v={video_id}&lc={item['id']}",
                        "content_text": self.clean_text(comment['textDisplay']),
                        "content_type": "comment",
                        "author": comment.get('authorDisplayName', ''),
                        "author_id": comment.get('authorChannelId', {}).get('value', ''),
                        "likes_count": comment.get('likeCount', 0),
                        "comments_count": item['snippet'].get('totalReplyCount', 0),
                        "published_at": datetime.fromisoformat(comment['publishedAt'].replace('Z', '+00:00')),
                        "collected_at": datetime.utcnow(),
                        "parent_video_id": video_id,
                        "word_count": len(comment['textDisplay'].split()),
                        "can_reply": comment.get('canReply', False)
                    }
                    
                    comments_data.append(comment_data)
                    
                except Exception as e:
                    logger.warning(f"Error processing comment: {e}")
                    continue
            
            logger.info(f"Extracted {len(comments_data)} comments for video {video_id}")
            return comments_data
            
        except HttpError as e:
            if e.resp.status == 403:
                logger.warning(f"Comments disabled for video {video_id}")
            else:
                logger.error(f"YouTube API error getting comments: {e}")
            return []
        except Exception as e:
            logger.error(f"Error getting video comments: {e}")
            return []
    
    async def get_channel_videos(
        self,
        channel_id: str,
        max_results: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get recent videos from a specific channel
        
        Args:
            channel_id: YouTube channel ID
            max_results: Maximum number of videos to retrieve
        
        Returns:
            List of video data dictionaries
        """
        try:
            # Get channel's uploads playlist
            channel_response = self.youtube.channels().list(
                part='contentDetails',
                id=channel_id
            ).execute()
            
            if not channel_response['items']:
                return []
            
            uploads_playlist_id = channel_response['items'][0]['contentDetails']['relatedPlaylists']['uploads']
            
            # Get videos from uploads playlist
            playlist_response = self.youtube.playlistItems().list(
                part='snippet',
                playlistId=uploads_playlist_id,
                maxResults=min(max_results, 50)
            ).execute()
            
            video_ids = [item['snippet']['resourceId']['videoId'] for item in playlist_response['items']]
            
            # Get detailed video information
            videos_data = await self._get_video_details(video_ids)
            
            logger.info(f"Retrieved {len(videos_data)} videos from channel {channel_id}")
            return videos_data
            
        except Exception as e:
            logger.error(f"Error getting channel videos: {e}")
            return []
