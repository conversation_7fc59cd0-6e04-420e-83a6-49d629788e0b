"""
Reddit data scraper for mental health content
"""
import asyncio
import praw
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from loguru import logger

from app.core.config import settings
from app.services.scrapers.base_scraper import BaseScraper
from app.models.content import Content


class RedditScraper(BaseScraper):
    """Reddit content scraper using PRAW (Python Reddit API Wrapper)"""
    
    def __init__(self):
        super().__init__("reddit")
        self.reddit = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Reddit API client"""
        try:
            self.reddit = praw.Reddit(
                client_id=settings.REDDIT_CLIENT_ID,
                client_secret=settings.REDDIT_CLIENT_SECRET,
                user_agent=settings.REDDIT_USER_AGENT,
                read_only=True
            )
            logger.info("Reddit client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Reddit client: {e}")
            raise
    
    async def scrape_subreddit(
        self, 
        subreddit_name: str, 
        keywords: List[str] = None,
        limit: int = 100,
        time_filter: str = "week"
    ) -> List[Dict[str, Any]]:
        """
        Scrape posts from a specific subreddit
        
        Args:
            subreddit_name: Name of the subreddit
            keywords: Optional keywords to filter posts
            limit: Maximum number of posts to retrieve
            time_filter: Time filter (hour, day, week, month, year, all)
        
        Returns:
            List of post data dictionaries
        """
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            posts_data = []
            
            # Get hot posts
            hot_posts = list(subreddit.hot(limit=limit//2))
            # Get top posts for the time period
            top_posts = list(subreddit.top(time_filter=time_filter, limit=limit//2))
            
            all_posts = hot_posts + top_posts
            
            for post in all_posts:
                try:
                    # Filter by keywords if provided
                    if keywords and not self._contains_keywords(post, keywords):
                        continue
                    
                    post_data = await self._extract_post_data(post)
                    if post_data:
                        posts_data.append(post_data)
                        
                except Exception as e:
                    logger.warning(f"Error processing post {post.id}: {e}")
                    continue
            
            logger.info(f"Scraped {len(posts_data)} posts from r/{subreddit_name}")
            return posts_data
            
        except Exception as e:
            logger.error(f"Error scraping subreddit r/{subreddit_name}: {e}")
            return []
    
    async def scrape_mental_health_subreddits(
        self, 
        keywords: List[str] = None,
        limit_per_subreddit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Scrape multiple mental health related subreddits
        
        Args:
            keywords: Keywords to filter content
            limit_per_subreddit: Limit per subreddit
        
        Returns:
            Combined list of posts from all subreddits
        """
        # Mental health focused subreddits
        subreddits = [
            "depression",
            "anxiety", 
            "mentalhealth",
            "getmotivated",
            "decidingtobebetter",
            "selfimprovement",
            "socialanxiety",
            "bipolar",
            "ptsd",
            "therapy",
            "mentalillness",
            "depression_help",
            "anxietyhelp",
            "getting_over_it",
            "lonely",
            "suicidewatch",
            "offmychest",
            "trueoffmychest"
        ]
        
        all_posts = []
        
        for subreddit_name in subreddits:
            try:
                posts = await self.scrape_subreddit(
                    subreddit_name=subreddit_name,
                    keywords=keywords,
                    limit=limit_per_subreddit
                )
                all_posts.extend(posts)
                
                # Rate limiting - be respectful to Reddit's API
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error scraping r/{subreddit_name}: {e}")
                continue
        
        logger.info(f"Total posts scraped from Reddit: {len(all_posts)}")
        return all_posts
    
    async def _extract_post_data(self, post) -> Optional[Dict[str, Any]]:
        """Extract relevant data from a Reddit post"""
        try:
            # Get post creation time
            created_utc = datetime.fromtimestamp(post.created_utc)
            
            # Extract post content
            content_text = post.selftext if hasattr(post, 'selftext') else ""
            if post.title:
                content_text = f"{post.title}\n\n{content_text}"
            
            # Calculate engagement rate
            total_votes = max(1, post.ups + abs(post.downs) if hasattr(post, 'downs') else post.ups)
            engagement_rate = (post.num_comments + post.ups) / total_votes if total_votes > 0 else 0
            
            post_data = {
                "platform": "reddit",
                "platform_id": post.id,
                "platform_url": f"https://reddit.com{post.permalink}",
                "title": post.title,
                "content_text": content_text,
                "content_type": "post",
                "author": str(post.author) if post.author else "[deleted]",
                "author_id": str(post.author.id) if post.author else None,
                "likes_count": post.ups,
                "comments_count": post.num_comments,
                "shares_count": 0,  # Reddit doesn't provide share count
                "views_count": 0,   # Reddit doesn't provide view count
                "engagement_rate": engagement_rate,
                "published_at": created_utc,
                "collected_at": datetime.utcnow(),
                "language": "en",  # Assuming English for now
                "word_count": len(content_text.split()) if content_text else 0,
                "subreddit": post.subreddit.display_name,
                "post_flair": post.link_flair_text,
                "is_nsfw": post.over_18,
                "is_spoiler": post.spoiler,
                "upvote_ratio": post.upvote_ratio,
                "gilded": post.gilded,
                "stickied": post.stickied
            }
            
            return post_data
            
        except Exception as e:
            logger.error(f"Error extracting post data: {e}")
            return None
    
    def _contains_keywords(self, post, keywords: List[str]) -> bool:
        """Check if post contains any of the specified keywords"""
        if not keywords:
            return True
        
        text_to_search = f"{post.title} {post.selftext}".lower()
        return any(keyword.lower() in text_to_search for keyword in keywords)
    
    async def get_post_comments(self, post_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get comments for a specific post
        
        Args:
            post_id: Reddit post ID
            limit: Maximum number of comments to retrieve
        
        Returns:
            List of comment data dictionaries
        """
        try:
            submission = self.reddit.submission(id=post_id)
            submission.comments.replace_more(limit=0)  # Remove "more comments" objects
            
            comments_data = []
            
            for comment in submission.comments.list()[:limit]:
                try:
                    if hasattr(comment, 'body') and comment.body != '[deleted]':
                        comment_data = {
                            "platform": "reddit",
                            "platform_id": comment.id,
                            "platform_url": f"https://reddit.com{comment.permalink}",
                            "content_text": comment.body,
                            "content_type": "comment",
                            "author": str(comment.author) if comment.author else "[deleted]",
                            "author_id": str(comment.author.id) if comment.author else None,
                            "likes_count": comment.ups,
                            "comments_count": 0,  # Comments don't have sub-comments in this context
                            "published_at": datetime.fromtimestamp(comment.created_utc),
                            "collected_at": datetime.utcnow(),
                            "parent_post_id": post_id,
                            "word_count": len(comment.body.split()),
                            "is_submitter": comment.is_submitter,
                            "gilded": comment.gilded
                        }
                        comments_data.append(comment_data)
                        
                except Exception as e:
                    logger.warning(f"Error processing comment {comment.id}: {e}")
                    continue
            
            logger.info(f"Extracted {len(comments_data)} comments for post {post_id}")
            return comments_data
            
        except Exception as e:
            logger.error(f"Error getting comments for post {post_id}: {e}")
            return []
