"""
Podcast data scraper for mental health content
"""
import asyncio
import httpx
import spotipy
from spotipy.oauth2 import SpotifyClientCredentials
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from loguru import logger

from app.core.config import settings
from app.services.scrapers.base_scraper import BaseScraper


class PodcastScraper(BaseScraper):
    """Podcast content scraper using Spotify and Apple Podcasts APIs"""
    
    def __init__(self):
        super().__init__("podcast")
        self.spotify = None
        self.http_client = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize Spotify and HTTP clients"""
        try:
            # Initialize Spotify client
            if settings.SPOTIFY_CLIENT_ID and settings.SPOTIFY_CLIENT_SECRET:
                client_credentials_manager = SpotifyClientCredentials(
                    client_id=settings.SPOTIFY_CLIENT_ID,
                    client_secret=settings.SPOTIFY_CLIENT_SECRET
                )
                self.spotify = spotipy.Spotify(client_credentials_manager=client_credentials_manager)
                logger.info("Spotify client initialized successfully")
            else:
                logger.warning("Spotify credentials not found")
            
            # Initialize HTTP client for other podcast platforms
            self.http_client = httpx.AsyncClient(
                timeout=30.0,
                headers={
                    'User-Agent': 'MentalHealthAnalyzer/1.0'
                }
            )
            logger.info("Podcast scraper clients initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize podcast clients: {e}")
            raise
    
    async def search_spotify_podcasts(
        self,
        query: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Search for podcasts on Spotify
        
        Args:
            query: Search query
            limit: Maximum number of results
        
        Returns:
            List of podcast episode data
        """
        if not self.spotify:
            logger.warning("Spotify client not available")
            return []
        
        try:
            # Search for shows
            results = self.spotify.search(q=query, type='show', limit=min(limit, 50))
            
            all_episodes = []
            
            for show in results['shows']['items']:
                try:
                    # Get episodes for each show
                    episodes = await self._get_spotify_show_episodes(show['id'], limit=10)
                    all_episodes.extend(episodes)
                    
                    # Rate limiting
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.warning(f"Error getting episodes for show {show['id']}: {e}")
                    continue
            
            logger.info(f"Found {len(all_episodes)} podcast episodes for query: {query}")
            return all_episodes
            
        except Exception as e:
            logger.error(f"Error searching Spotify podcasts: {e}")
            return []
    
    async def _get_spotify_show_episodes(
        self,
        show_id: str,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """Get episodes for a specific Spotify show"""
        try:
            episodes_result = self.spotify.show_episodes(show_id, limit=limit)
            episodes_data = []
            
            # Get show information
            show_info = self.spotify.show(show_id)
            
            for episode in episodes_result['items']:
                episode_data = await self._extract_spotify_episode_data(episode, show_info)
                if episode_data:
                    episodes_data.append(episode_data)
            
            return episodes_data
            
        except Exception as e:
            logger.error(f"Error getting Spotify show episodes: {e}")
            return []
    
    async def _extract_spotify_episode_data(
        self,
        episode: Dict[str, Any],
        show_info: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Extract relevant data from Spotify episode"""
        try:
            # Parse release date
            release_date = datetime.fromisoformat(episode['release_date'])
            
            # Extract content text (name + description)
            title = episode.get('name', '')
            description = episode.get('description', '')
            content_text = f"{title}\n\n{description}"
            
            episode_data = {
                "platform": "spotify",
                "platform_id": episode['id'],
                "platform_url": episode.get('external_urls', {}).get('spotify', ''),
                "title": title,
                "content_text": self.clean_text(content_text),
                "content_type": "audio",
                "author": show_info.get('publisher', ''),
                "author_id": show_info.get('id', ''),
                "likes_count": 0,  # Spotify doesn't provide like counts for episodes
                "comments_count": 0,  # Spotify doesn't provide comment counts
                "shares_count": 0,
                "views_count": 0,  # Spotify doesn't provide play counts publicly
                "engagement_rate": 0.0,
                "published_at": release_date,
                "collected_at": datetime.utcnow(),
                "language": episode.get('language', 'en'),
                "duration_seconds": episode.get('duration_ms', 0) // 1000,
                "word_count": len(content_text.split()) if content_text else 0,
                "show_name": show_info.get('name', ''),
                "show_description": show_info.get('description', ''),
                "show_total_episodes": show_info.get('total_episodes', 0),
                "episode_number": episode.get('episode_number'),
                "season_number": episode.get('season_number'),
                "is_explicit": episode.get('explicit', False),
                "audio_preview_url": episode.get('audio_preview_url'),
                "images": episode.get('images', []),
                "type": episode.get('type', 'episode')
            }
            
            return episode_data
            
        except Exception as e:
            logger.error(f"Error extracting Spotify episode data: {e}")
            return None
    
    async def search_mental_health_podcasts(
        self,
        limit_per_query: int = 20
    ) -> List[Dict[str, Any]]:
        """
        Search for mental health related podcasts
        
        Args:
            limit_per_query: Limit per search query
        
        Returns:
            Combined list of episodes from all searches
        """
        # Mental health podcast search queries
        search_queries = [
            "mental health",
            "depression anxiety",
            "therapy counseling",
            "mindfulness meditation",
            "self care wellness",
            "trauma healing",
            "bipolar disorder",
            "PTSD recovery",
            "suicide prevention",
            "emotional wellness",
            "psychology therapy",
            "mental illness",
            "stress management",
            "anxiety relief",
            "depression help"
        ]
        
        all_episodes = []
        
        for query in search_queries:
            try:
                episodes = await self.search_spotify_podcasts(
                    query=query,
                    limit=limit_per_query
                )
                
                # Filter for mental health relevance
                relevant_episodes = [
                    episode for episode in episodes 
                    if self.is_mental_health_related(episode.get('content_text', ''))
                ]
                
                all_episodes.extend(relevant_episodes)
                
                # Rate limiting
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error searching for query '{query}': {e}")
                continue
        
        # Remove duplicates based on episode ID
        unique_episodes = {}
        for episode in all_episodes:
            episode_id = episode.get('platform_id')
            if episode_id and episode_id not in unique_episodes:
                unique_episodes[episode_id] = episode
        
        final_episodes = list(unique_episodes.values())
        logger.info(f"Total unique podcast episodes collected: {len(final_episodes)}")
        
        return final_episodes
    
    async def get_featured_mental_health_shows(self) -> List[Dict[str, Any]]:
        """
        Get episodes from featured mental health podcast shows
        
        Returns:
            List of episodes from curated mental health shows
        """
        # Curated list of popular mental health podcasts
        featured_shows = [
            "The Mental Illness Happy Hour",
            "Therapy for Black Girls",
            "The Hilarious World of Depression", 
            "Ten Percent Happier",
            "On Being",
            "The Life Coach School Podcast",
            "Terrible, Thanks for Asking",
            "Mental Health Happy Hour",
            "The Anxiety Coaches Podcast",
            "Depression and Bipolar Support Alliance"
        ]
        
        all_episodes = []
        
        for show_name in featured_shows:
            try:
                # Search for the specific show
                if self.spotify:
                    results = self.spotify.search(q=show_name, type='show', limit=1)
                    
                    if results['shows']['items']:
                        show = results['shows']['items'][0]
                        episodes = await self._get_spotify_show_episodes(show['id'], limit=10)
                        all_episodes.extend(episodes)
                
                # Rate limiting
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error getting episodes for show '{show_name}': {e}")
                continue
        
        logger.info(f"Retrieved {len(all_episodes)} episodes from featured shows")
        return all_episodes
    
    async def search_apple_podcasts(
        self,
        query: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Search Apple Podcasts using iTunes Search API
        
        Args:
            query: Search query
            limit: Maximum number of results
        
        Returns:
            List of podcast data
        """
        try:
            url = "https://itunes.apple.com/search"
            params = {
                'term': query,
                'media': 'podcast',
                'entity': 'podcast',
                'limit': min(limit, 200),  # iTunes API limit
                'country': 'US',
                'lang': 'en_us'
            }
            
            response = await self.http_client.get(url, params=params)
            
            if response.status_code != 200:
                logger.warning(f"Apple Podcasts search failed: {response.status_code}")
                return []
            
            data = response.json()
            podcasts_data = []
            
            for podcast in data.get('results', []):
                podcast_data = await self._extract_apple_podcast_data(podcast)
                if podcast_data:
                    podcasts_data.append(podcast_data)
            
            logger.info(f"Found {len(podcasts_data)} Apple Podcasts for query: {query}")
            return podcasts_data
            
        except Exception as e:
            logger.error(f"Error searching Apple Podcasts: {e}")
            return []
    
    async def _extract_apple_podcast_data(self, podcast: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract relevant data from Apple Podcast result"""
        try:
            # Parse release date
            release_date = None
            if podcast.get('releaseDate'):
                release_date = datetime.fromisoformat(podcast['releaseDate'].replace('Z', '+00:00'))
            
            # Extract content text
            title = podcast.get('trackName', '')
            description = podcast.get('description', '')
            content_text = f"{title}\n\n{description}"
            
            podcast_data = {
                "platform": "apple_podcasts",
                "platform_id": str(podcast.get('trackId', '')),
                "platform_url": podcast.get('trackViewUrl', ''),
                "title": title,
                "content_text": self.clean_text(content_text),
                "content_type": "audio",
                "author": podcast.get('artistName', ''),
                "author_id": str(podcast.get('artistId', '')),
                "likes_count": 0,
                "comments_count": 0,
                "shares_count": 0,
                "views_count": 0,
                "engagement_rate": 0.0,
                "published_at": release_date,
                "collected_at": datetime.utcnow(),
                "language": "en",
                "duration_seconds": 0,  # Not provided by iTunes API
                "word_count": len(content_text.split()) if content_text else 0,
                "genre": podcast.get('primaryGenreName', ''),
                "country": podcast.get('country', 'USA'),
                "track_count": podcast.get('trackCount', 0),
                "artwork_url": podcast.get('artworkUrl600', ''),
                "feed_url": podcast.get('feedUrl', ''),
                "content_advisory_rating": podcast.get('contentAdvisoryRating', ''),
                "collection_price": podcast.get('collectionPrice', 0)
            }
            
            return podcast_data
            
        except Exception as e:
            logger.error(f"Error extracting Apple Podcast data: {e}")
            return None
    
    async def get_podcast_rss_feed(self, feed_url: str) -> List[Dict[str, Any]]:
        """
        Parse podcast RSS feed to get episode information
        
        Args:
            feed_url: RSS feed URL
        
        Returns:
            List of episode data
        """
        try:
            response = await self.http_client.get(feed_url)
            
            if response.status_code != 200:
                logger.warning(f"Failed to fetch RSS feed: {response.status_code}")
                return []
            
            # Parse RSS feed
            # This would require an XML parser like feedparser
            # For now, return empty list as placeholder
            logger.info(f"RSS feed parsing not implemented yet for: {feed_url}")
            return []
            
        except Exception as e:
            logger.error(f"Error parsing RSS feed: {e}")
            return []
    
    async def close(self):
        """Close HTTP client"""
        if self.http_client:
            await self.http_client.aclose()
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        if self.http_client:
            try:
                asyncio.create_task(self.http_client.aclose())
            except Exception:
                pass
