"""
Base scraper class for all platform scrapers
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger


class BaseScraper(ABC):
    """Abstract base class for all platform scrapers"""
    
    def __init__(self, platform_name: str):
        self.platform_name = platform_name
        self.rate_limit_delay = 1.0  # Default delay between requests
        self.max_retries = 3
        self.timeout = 30
    
    @abstractmethod
    async def scrape_content(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Main method to scrape content from the platform
        Must be implemented by each platform scraper
        """
        pass
    
    def validate_content(self, content_data: Dict[str, Any]) -> bool:
        """
        Validate scraped content data
        
        Args:
            content_data: Dictionary containing content data
            
        Returns:
            True if content is valid, False otherwise
        """
        required_fields = [
            "platform",
            "platform_id", 
            "content_text",
            "content_type"
        ]
        
        # Check required fields
        for field in required_fields:
            if field not in content_data or not content_data[field]:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Check content length
        content_text = content_data.get("content_text", "")
        if len(content_text.strip()) < 10:  # Minimum content length
            logger.warning("Content too short")
            return False
        
        # Check if content is in English (basic check)
        if not self._is_likely_english(content_text):
            logger.warning("Content not in English")
            return False
        
        return True
    
    def _is_likely_english(self, text: str) -> bool:
        """
        Basic check to determine if text is likely in English
        
        Args:
            text: Text to check
            
        Returns:
            True if text is likely English
        """
        if not text:
            return False
        
        # Check for common English words
        common_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 
            'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'is', 
            'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
            'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
            'might', 'must', 'can', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
        }
        
        words = text.lower().split()
        if len(words) < 5:
            return True  # Too short to determine, assume English
        
        english_word_count = sum(1 for word in words[:50] if word in common_words)
        return english_word_count / min(len(words), 50) > 0.1  # At least 10% common English words
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize text content
        
        Args:
            text: Raw text to clean
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = " ".join(text.split())
        
        # Remove common Reddit/social media artifacts
        text = text.replace("&amp;", "&")
        text = text.replace("&lt;", "<")
        text = text.replace("&gt;", ">")
        text = text.replace("&quot;", '"')
        
        return text.strip()
    
    def extract_hashtags(self, text: str) -> List[str]:
        """
        Extract hashtags from text
        
        Args:
            text: Text to extract hashtags from
            
        Returns:
            List of hashtags (without # symbol)
        """
        import re
        if not text:
            return []
        
        hashtag_pattern = r'#(\w+)'
        hashtags = re.findall(hashtag_pattern, text, re.IGNORECASE)
        return list(set(hashtags))  # Remove duplicates
    
    def extract_mentions(self, text: str) -> List[str]:
        """
        Extract user mentions from text
        
        Args:
            text: Text to extract mentions from
            
        Returns:
            List of mentioned usernames (without @ symbol)
        """
        import re
        if not text:
            return []
        
        mention_pattern = r'@(\w+)'
        mentions = re.findall(mention_pattern, text, re.IGNORECASE)
        return list(set(mentions))  # Remove duplicates
    
    def calculate_engagement_rate(
        self, 
        likes: int, 
        comments: int, 
        shares: int, 
        views: int = None
    ) -> float:
        """
        Calculate engagement rate for content
        
        Args:
            likes: Number of likes
            comments: Number of comments
            shares: Number of shares
            views: Number of views (optional)
            
        Returns:
            Engagement rate as a float between 0 and 1
        """
        total_engagement = likes + comments + shares
        
        if views and views > 0:
            return total_engagement / views
        else:
            # If no view count, use total engagement as a proxy
            return min(1.0, total_engagement / max(1, total_engagement + 100))
    
    def is_mental_health_related(self, text: str, keywords: List[str] = None) -> bool:
        """
        Check if content is related to mental health
        
        Args:
            text: Text content to check
            keywords: Optional list of keywords to check for
            
        Returns:
            True if content appears to be mental health related
        """
        if not text:
            return False
        
        # Default mental health keywords
        default_keywords = [
            'depression', 'anxiety', 'mental health', 'therapy', 'counseling',
            'ptsd', 'bipolar', 'panic', 'stress', 'trauma', 'healing',
            'recovery', 'suicide', 'self harm', 'medication', 'therapist',
            'psychiatrist', 'psychology', 'emotional', 'mood', 'wellbeing',
            'mindfulness', 'meditation', 'self care', 'coping', 'support',
            'lonely', 'loneliness', 'isolation', 'overwhelmed', 'burnout'
        ]
        
        keywords_to_check = keywords or default_keywords
        text_lower = text.lower()
        
        return any(keyword.lower() in text_lower for keyword in keywords_to_check)
    
    async def handle_rate_limit(self, attempt: int = 1):
        """
        Handle rate limiting with exponential backoff
        
        Args:
            attempt: Current attempt number
        """
        import asyncio
        delay = self.rate_limit_delay * (2 ** (attempt - 1))
        logger.info(f"Rate limit hit, waiting {delay} seconds...")
        await asyncio.sleep(delay)
    
    def log_scraping_stats(self, total_items: int, successful_items: int, platform: str = None):
        """
        Log scraping statistics
        
        Args:
            total_items: Total number of items processed
            successful_items: Number of successfully processed items
            platform: Platform name (optional)
        """
        platform_name = platform or self.platform_name
        success_rate = (successful_items / total_items * 100) if total_items > 0 else 0
        
        logger.info(
            f"{platform_name} scraping completed: "
            f"{successful_items}/{total_items} items successful "
            f"({success_rate:.1f}% success rate)"
        )
