"""
AI Analyzer Factory - Choose between Gemini and OpenAI
"""
from typing import Dict, Any, List
from loguru import logger

from app.core.config import settings


class AIAnalyzerFactory:
    """Factory class to create appropriate AI analyzer based on configuration"""
    
    @staticmethod
    def create_analyzer():
        """Create analyzer based on AI_PROVIDER setting"""
        provider = settings.AI_PROVIDER.lower()
        
        if provider == "gemini":
            try:
                from app.services.analyzers.gemini_analyzer import GeminiAnalyzer
                logger.info("Using Gemini AI analyzer")
                return GeminiAnalyzer()
            except ImportError as e:
                logger.error(f"Failed to import Gemini analyzer: {e}")
                logger.info("Falling back to OpenAI analyzer")
                provider = "openai"
        
        if provider == "openai":
            try:
                from app.services.analyzers.story_classifier import StoryClassifier
                from app.services.analyzers.sentiment_analyzer import SentimentAnalyzer
                logger.info("Using OpenAI analyzer")
                return OpenAIAnalyzerWrapper()
            except ImportError as e:
                logger.error(f"Failed to import OpenAI analyzer: {e}")
                raise RuntimeError("No AI analyzer available")
        
        raise ValueError(f"Unknown AI provider: {provider}")


class OpenAIAnalyzerWrapper:
    """Wrapper to make OpenAI analyzers compatible with unified interface"""
    
    def __init__(self):
        from app.services.analyzers.story_classifier import StoryClassifier
        from app.services.analyzers.sentiment_analyzer import SentimentAnalyzer
        
        self.story_classifier = StoryClassifier()
        self.sentiment_analyzer = SentimentAnalyzer()
    
    async def classify_story(self, content: str) -> Dict[str, Any]:
        """Classify story using OpenAI"""
        return await self.story_classifier.classify_story(content)
    
    async def analyze_sentiment(self, content: str) -> Dict[str, Any]:
        """Analyze sentiment using OpenAI"""
        return await self.sentiment_analyzer.analyze_sentiment(content)
    
    async def batch_analyze(self, contents: List[str], analysis_type: str = "classification") -> List[Dict[str, Any]]:
        """Batch analyze using OpenAI"""
        if analysis_type == "classification":
            return await self.story_classifier.batch_classify_stories(contents)
        else:
            return await self.sentiment_analyzer.batch_analyze_sentiment(contents)


class UnifiedAIAnalyzer:
    """Unified interface for AI analysis regardless of provider"""
    
    def __init__(self):
        self.analyzer = AIAnalyzerFactory.create_analyzer()
        self.provider = settings.AI_PROVIDER.lower()
    
    async def analyze_content(self, content: str, analysis_types: List[str] = None) -> Dict[str, Any]:
        """
        Perform comprehensive analysis on content
        
        Args:
            content: Text content to analyze
            analysis_types: List of analysis types ["classification", "sentiment"]
        
        Returns:
            Combined analysis results
        """
        if analysis_types is None:
            analysis_types = ["classification", "sentiment"]
        
        results = {
            "content_preview": content[:200] + "..." if len(content) > 200 else content,
            "analysis_provider": self.provider,
            "analysis_timestamp": None,
            "classification": None,
            "sentiment": None,
            "errors": []
        }
        
        # Perform classification analysis
        if "classification" in analysis_types:
            try:
                classification_result = await self.analyzer.classify_story(content)
                results["classification"] = classification_result
                results["analysis_timestamp"] = classification_result.get("analyzed_at")
            except Exception as e:
                logger.error(f"Classification analysis failed: {e}")
                results["errors"].append(f"Classification failed: {str(e)}")
        
        # Perform sentiment analysis
        if "sentiment" in analysis_types:
            try:
                sentiment_result = await self.analyzer.analyze_sentiment(content)
                results["sentiment"] = sentiment_result
                if not results["analysis_timestamp"]:
                    results["analysis_timestamp"] = sentiment_result.get("analyzed_at")
            except Exception as e:
                logger.error(f"Sentiment analysis failed: {e}")
                results["errors"].append(f"Sentiment failed: {str(e)}")
        
        return results
    
    async def batch_analyze_content(
        self, 
        contents: List[str], 
        analysis_types: List[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Perform batch analysis on multiple contents
        
        Args:
            contents: List of text contents to analyze
            analysis_types: List of analysis types ["classification", "sentiment"]
        
        Returns:
            List of combined analysis results
        """
        if analysis_types is None:
            analysis_types = ["classification", "sentiment"]
        
        results = []
        
        # Process each content individually for better error handling
        for i, content in enumerate(contents):
            try:
                result = await self.analyze_content(content, analysis_types)
                result["batch_index"] = i
                results.append(result)
                
                # Add delay between analyses to respect rate limits
                if i < len(contents) - 1:
                    import asyncio
                    await asyncio.sleep(0.5 if self.provider == "openai" else 1.0)
                    
            except Exception as e:
                logger.error(f"Batch analysis failed for content {i}: {e}")
                results.append({
                    "batch_index": i,
                    "content_preview": content[:200] + "..." if len(content) > 200 else content,
                    "analysis_provider": self.provider,
                    "analysis_timestamp": None,
                    "classification": None,
                    "sentiment": None,
                    "errors": [f"Analysis failed: {str(e)}"]
                })
        
        logger.info(f"Completed batch analysis of {len(contents)} contents using {self.provider}")
        return results
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current AI provider"""
        if self.provider == "gemini":
            model_name = settings.GEMINI_MODEL
            is_pro = "pro" in model_name.lower()
            return {
                "provider": self.provider,
                "model": model_name,
                "model_tier": "pro" if is_pro else "flash",
                "capabilities": ["classification", "sentiment", "batch_analysis", "advanced_reasoning"],
                "rate_limits": {
                    "requests_per_minute": 60 if is_pro else 15,
                    "batch_size": 5 if is_pro else 3,
                    "max_tokens": 2048 if is_pro else 1000
                },
                "features": {
                    "multimodal": True,
                    "function_calling": True,
                    "json_mode": True,
                    "safety_settings": True
                }
            }
        else:
            return {
                "provider": self.provider,
                "model": "gpt-4",
                "model_tier": "standard",
                "capabilities": ["classification", "sentiment", "batch_analysis"],
                "rate_limits": {
                    "requests_per_minute": 60,
                    "batch_size": 5,
                    "max_tokens": 1000
                }
            }
