"""
Gemini AI analyzer for mental health content classification and sentiment analysis
"""
import asyncio
import google.generativeai as genai
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger
import json
import re

from app.core.config import settings


class GeminiAnalyzer:
    """AI-powered content analysis using Google Gemini"""
    
    def __init__(self):
        # Configure Gemini
        genai.configure(api_key=settings.GEMINI_API_KEY)
        self.model_name = settings.GEMINI_MODEL
        self.model = genai.GenerativeModel(self.model_name)

        # Set generation config based on model
        if "pro" in self.model_name.lower():
            self.generation_config = genai.types.GenerationConfig(
                temperature=0.3,
                max_output_tokens=2048,  # Pro supports more tokens
                top_p=0.8,
                top_k=40
            )
            self.batch_size = 5  # Pro can handle larger batches
            self.rate_limit_delay = 0.5  # Pro has higher rate limits
        else:
            self.generation_config = genai.types.GenerationConfig(
                temperature=0.3,
                max_output_tokens=1000,
                top_p=0.8,
                top_k=40
            )
            self.batch_size = 3
            self.rate_limit_delay = 1.0
        
        # Story categories for mental health content
        self.story_categories = [
            "depression_recovery",
            "anxiety_management", 
            "job_loss_recovery",
            "breakup_healing",
            "social_anxiety_overcome",
            "loneliness_solutions",
            "trauma_healing",
            "bipolar_management",
            "ptsd_recovery",
            "suicide_prevention",
            "self_acceptance",
            "therapy_journey",
            "medication_experience",
            "family_support",
            "workplace_stress",
            "academic_pressure",
            "body_image_issues",
            "addiction_recovery",
            "grief_processing",
            "relationship_healing"
        ]
        
        # Narrative structures
        self.narrative_structures = [
            "hero_journey",
            "problem_solution",
            "before_after",
            "crisis_resolution",
            "gradual_improvement",
            "setback_comeback",
            "discovery_insight",
            "support_transformation"
        ]
    
    async def classify_story(self, content: str) -> Dict[str, Any]:
        """
        Classify a story into categories and analyze its structure
        
        Args:
            content: Text content to analyze
        
        Returns:
            Dictionary with classification results
        """
        try:
            prompt = self._build_classification_prompt(content)
            
            # Generate response using Gemini
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=self.generation_config
            )
            
            result = self._parse_classification_response(response.text)
            
            logger.info(f"Story classified successfully with Gemini: {result.get('primary_category', 'unknown')}")
            return result
            
        except Exception as e:
            logger.error(f"Error classifying story with Gemini: {e}")
            return self._get_default_classification()
    
    def _build_classification_prompt(self, content: str) -> str:
        """Build prompt for story classification"""
        categories_str = ", ".join(self.story_categories)
        structures_str = ", ".join(self.narrative_structures)
        
        prompt = f"""
        You are an expert in mental health content analysis and storytelling patterns. Please analyze the following mental health related content and provide a detailed classification.

        Content: "{content[:2000]}..."

        Please provide your analysis in the following JSON format:
        {{
            "primary_category": "one of: {categories_str}",
            "secondary_categories": ["list of relevant secondary categories"],
            "narrative_structure": "one of: {structures_str}",
            "emotional_arc": {{
                "starting_emotion": "emotion at the beginning",
                "lowest_point": "emotion at the lowest point", 
                "turning_point": "emotion at the turning point",
                "ending_emotion": "emotion at the end"
            }},
            "key_themes": ["list of 3-5 key themes"],
            "story_elements": {{
                "has_personal_experience": true/false,
                "has_professional_advice": true/false,
                "has_specific_strategies": true/false,
                "has_hope_message": true/false,
                "has_trigger_warning": true/false
            }},
            "authenticity_indicators": {{
                "personal_details": true/false,
                "emotional_vulnerability": true/false,
                "specific_examples": true/false,
                "realistic_timeline": true/false
            }},
            "engagement_factors": ["list of elements that make this engaging"],
            "target_audience": "who this content would resonate with most",
            "confidence_score": 0.0-1.0
        }}

        Please respond with only the JSON object, no additional text.
        """
        
        return prompt
    
    async def analyze_sentiment(self, content: str) -> Dict[str, Any]:
        """
        Analyze sentiment of mental health content
        
        Args:
            content: Text content to analyze
        
        Returns:
            Detailed sentiment analysis results
        """
        try:
            prompt = self._build_sentiment_prompt(content)
            
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=self.generation_config
            )
            
            result = self._parse_sentiment_response(response.text)
            
            logger.info(f"Sentiment analyzed with Gemini: {result.get('overall_sentiment', 'unknown')}")
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment with Gemini: {e}")
            return self._get_default_sentiment()
    
    def _build_sentiment_prompt(self, content: str) -> str:
        """Build prompt for sentiment analysis"""
        prompt = f"""
        You are an expert in psychological sentiment analysis, particularly for mental health content. Analyze the sentiment and emotional content of this mental health related text:

        Content: "{content[:2000]}..."

        Please provide detailed sentiment analysis in JSON format:
        {{
            "overall_sentiment": "positive/negative/neutral/mixed",
            "sentiment_score": -1.0 to 1.0,
            "emotional_intensity": 0.0 to 1.0,
            "primary_emotions": ["list of 3-5 primary emotions detected"],
            "emotional_progression": [
                {{"section": "beginning", "sentiment": "positive/negative/neutral", "emotions": ["emotion1", "emotion2"]}},
                {{"section": "middle", "sentiment": "positive/negative/neutral", "emotions": ["emotion1", "emotion2"]}},
                {{"section": "end", "sentiment": "positive/negative/neutral", "emotions": ["emotion1", "emotion2"]}}
            ],
            "hope_indicators": {{
                "has_hope": true/false,
                "hope_score": 0.0-1.0,
                "hope_phrases": ["specific phrases indicating hope"]
            }},
            "distress_indicators": {{
                "has_distress": true/false,
                "distress_level": "low/medium/high",
                "distress_phrases": ["specific phrases indicating distress"]
            }},
            "recovery_indicators": {{
                "shows_progress": true/false,
                "recovery_stage": "crisis/seeking_help/improving/maintaining/thriving",
                "progress_markers": ["specific indicators of progress"]
            }},
            "support_seeking": {{
                "seeks_support": true/false,
                "support_type": "professional/peer/family/online",
                "support_phrases": ["phrases indicating support seeking"]
            }},
            "risk_factors": {{
                "suicide_risk": "none/low/medium/high",
                "self_harm_mentions": true/false,
                "crisis_indicators": ["specific crisis indicators if any"]
            }},
            "resilience_factors": {{
                "shows_resilience": true/false,
                "coping_strategies": ["mentioned coping strategies"],
                "strength_indicators": ["phrases showing strength/resilience"]
            }},
            "confidence_score": 0.0-1.0
        }}

        Please respond with only the JSON object, no additional text.
        """
        
        return prompt
    
    def _parse_classification_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the Gemini response into structured data"""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                # Add metadata
                result["analyzed_at"] = datetime.utcnow().isoformat()
                result["model_version"] = self.model_name
                result["ai_provider"] = "gemini"
                
                return result
            else:
                logger.warning("Could not extract JSON from Gemini classification response")
                return self._get_default_classification()
                
        except Exception as e:
            logger.error(f"Error parsing Gemini classification response: {e}")
            return self._get_default_classification()
    
    def _parse_sentiment_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the Gemini sentiment response into structured data"""
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                # Add metadata
                result["analyzed_at"] = datetime.utcnow().isoformat()
                result["model_version"] = self.model_name
                result["ai_provider"] = "gemini"
                
                return result
            else:
                logger.warning("Could not extract JSON from Gemini sentiment response")
                return self._get_default_sentiment()
                
        except Exception as e:
            logger.error(f"Error parsing Gemini sentiment response: {e}")
            return self._get_default_sentiment()
    
    def _get_default_classification(self) -> Dict[str, Any]:
        """Return default classification when analysis fails"""
        return {
            "primary_category": "unknown",
            "secondary_categories": [],
            "narrative_structure": "unknown",
            "emotional_arc": {
                "starting_emotion": "unknown",
                "lowest_point": "unknown",
                "turning_point": "unknown", 
                "ending_emotion": "unknown"
            },
            "key_themes": [],
            "story_elements": {
                "has_personal_experience": False,
                "has_professional_advice": False,
                "has_specific_strategies": False,
                "has_hope_message": False,
                "has_trigger_warning": False
            },
            "authenticity_indicators": {
                "personal_details": False,
                "emotional_vulnerability": False,
                "specific_examples": False,
                "realistic_timeline": False
            },
            "engagement_factors": [],
            "target_audience": "unknown",
            "confidence_score": 0.0,
            "analyzed_at": datetime.utcnow().isoformat(),
            "model_version": self.model_name,
            "ai_provider": "gemini"
        }
    
    def _get_default_sentiment(self) -> Dict[str, Any]:
        """Return default sentiment when analysis fails"""
        return {
            "overall_sentiment": "neutral",
            "sentiment_score": 0.0,
            "emotional_intensity": 0.0,
            "primary_emotions": [],
            "emotional_progression": [],
            "hope_indicators": {
                "has_hope": False,
                "hope_score": 0.0,
                "hope_phrases": []
            },
            "distress_indicators": {
                "has_distress": False,
                "distress_level": "low",
                "distress_phrases": []
            },
            "recovery_indicators": {
                "shows_progress": False,
                "recovery_stage": "unknown",
                "progress_markers": []
            },
            "support_seeking": {
                "seeks_support": False,
                "support_type": "unknown",
                "support_phrases": []
            },
            "risk_factors": {
                "suicide_risk": "none",
                "self_harm_mentions": False,
                "crisis_indicators": []
            },
            "resilience_factors": {
                "shows_resilience": False,
                "coping_strategies": [],
                "strength_indicators": []
            },
            "confidence_score": 0.0,
            "analyzed_at": datetime.utcnow().isoformat(),
            "model_version": self.model_name,
            "ai_provider": "gemini"
        }
    
    async def batch_analyze(self, contents: List[str], analysis_type: str = "classification") -> List[Dict[str, Any]]:
        """
        Analyze multiple contents in batch
        
        Args:
            contents: List of text contents to analyze
            analysis_type: "classification" or "sentiment"
        
        Returns:
            List of analysis results
        """
        results = []
        
        # Process in batches based on model capabilities
        for i in range(0, len(contents), self.batch_size):
            batch = contents[i:i + self.batch_size]
            
            # Process batch with delay
            batch_results = []
            for content in batch:
                try:
                    if analysis_type == "classification":
                        result = await self.classify_story(content)
                    else:
                        result = await self.analyze_sentiment(content)
                    batch_results.append(result)
                    
                    # Rate limiting between requests
                    await asyncio.sleep(self.rate_limit_delay)
                    
                except Exception as e:
                    logger.error(f"Error in batch analysis: {e}")
                    if analysis_type == "classification":
                        batch_results.append(self._get_default_classification())
                    else:
                        batch_results.append(self._get_default_sentiment())
            
            results.extend(batch_results)
            
            # Longer delay between batches
            if i + self.batch_size < len(contents):
                await asyncio.sleep(self.rate_limit_delay * 2)
        
        logger.info(f"Batch analyzed {len(results)} contents with Gemini")
        return results
