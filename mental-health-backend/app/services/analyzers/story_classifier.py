"""
Story classification service using OpenAI API
"""
import asyncio
import openai
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from app.core.config import settings


class StoryClassifier:
    """AI-powered story classification and analysis"""
    
    def __init__(self):
        self.client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = "gpt-4"
        
        # Story categories for mental health content
        self.story_categories = [
            "depression_recovery",
            "anxiety_management", 
            "job_loss_recovery",
            "breakup_healing",
            "social_anxiety_overcome",
            "loneliness_solutions",
            "trauma_healing",
            "bipolar_management",
            "ptsd_recovery",
            "suicide_prevention",
            "self_acceptance",
            "therapy_journey",
            "medication_experience",
            "family_support",
            "workplace_stress",
            "academic_pressure",
            "body_image_issues",
            "addiction_recovery",
            "grief_processing",
            "relationship_healing"
        ]
        
        # Narrative structures
        self.narrative_structures = [
            "hero_journey",
            "problem_solution",
            "before_after",
            "crisis_resolution",
            "gradual_improvement",
            "setback_comeback",
            "discovery_insight",
            "support_transformation"
        ]
    
    async def classify_story(self, content: str) -> Dict[str, Any]:
        """
        Classify a story into categories and analyze its structure
        
        Args:
            content: Text content to analyze
        
        Returns:
            Dictionary with classification results
        """
        try:
            prompt = self._build_classification_prompt(content)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert in mental health content analysis and storytelling patterns."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            result = self._parse_classification_response(response.choices[0].message.content)
            
            logger.info(f"Story classified successfully: {result.get('primary_category', 'unknown')}")
            return result
            
        except Exception as e:
            logger.error(f"Error classifying story: {e}")
            return self._get_default_classification()
    
    def _build_classification_prompt(self, content: str) -> str:
        """Build prompt for story classification"""
        categories_str = ", ".join(self.story_categories)
        structures_str = ", ".join(self.narrative_structures)
        
        prompt = f"""
        Please analyze the following mental health related content and provide a detailed classification:

        Content: "{content[:2000]}..."

        Please provide your analysis in the following JSON format:
        {{
            "primary_category": "one of: {categories_str}",
            "secondary_categories": ["list of relevant secondary categories"],
            "narrative_structure": "one of: {structures_str}",
            "emotional_arc": {{
                "starting_emotion": "emotion at the beginning",
                "lowest_point": "emotion at the lowest point", 
                "turning_point": "emotion at the turning point",
                "ending_emotion": "emotion at the end"
            }},
            "key_themes": ["list of 3-5 key themes"],
            "story_elements": {{
                "has_personal_experience": true/false,
                "has_professional_advice": true/false,
                "has_specific_strategies": true/false,
                "has_hope_message": true/false,
                "has_trigger_warning": true/false
            }},
            "authenticity_indicators": {{
                "personal_details": true/false,
                "emotional_vulnerability": true/false,
                "specific_examples": true/false,
                "realistic_timeline": true/false
            }},
            "engagement_factors": ["list of elements that make this engaging"],
            "target_audience": "who this content would resonate with most",
            "confidence_score": 0.0-1.0
        }}
        """
        
        return prompt
    
    def _parse_classification_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the AI response into structured data"""
        try:
            import json
            import re
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                # Add metadata
                result["analyzed_at"] = datetime.utcnow().isoformat()
                result["model_version"] = self.model
                
                return result
            else:
                logger.warning("Could not extract JSON from classification response")
                return self._get_default_classification()
                
        except Exception as e:
            logger.error(f"Error parsing classification response: {e}")
            return self._get_default_classification()
    
    def _get_default_classification(self) -> Dict[str, Any]:
        """Return default classification when analysis fails"""
        return {
            "primary_category": "unknown",
            "secondary_categories": [],
            "narrative_structure": "unknown",
            "emotional_arc": {
                "starting_emotion": "unknown",
                "lowest_point": "unknown",
                "turning_point": "unknown", 
                "ending_emotion": "unknown"
            },
            "key_themes": [],
            "story_elements": {
                "has_personal_experience": False,
                "has_professional_advice": False,
                "has_specific_strategies": False,
                "has_hope_message": False,
                "has_trigger_warning": False
            },
            "authenticity_indicators": {
                "personal_details": False,
                "emotional_vulnerability": False,
                "specific_examples": False,
                "realistic_timeline": False
            },
            "engagement_factors": [],
            "target_audience": "unknown",
            "confidence_score": 0.0,
            "analyzed_at": datetime.utcnow().isoformat(),
            "model_version": self.model
        }
    
    async def analyze_narrative_structure(self, content: str) -> Dict[str, Any]:
        """
        Analyze the narrative structure of a story in detail
        
        Args:
            content: Text content to analyze
        
        Returns:
            Detailed narrative structure analysis
        """
        try:
            prompt = f"""
            Analyze the narrative structure of this mental health story in detail:

            Content: "{content[:2000]}..."

            Please provide analysis in JSON format:
            {{
                "structure_type": "hero_journey/problem_solution/before_after/etc",
                "story_beats": [
                    {{"beat": "setup", "description": "what happens in setup"}},
                    {{"beat": "inciting_incident", "description": "what triggers the story"}},
                    {{"beat": "rising_action", "description": "challenges and obstacles"}},
                    {{"beat": "climax", "description": "turning point or crisis"}},
                    {{"beat": "falling_action", "description": "resolution process"}},
                    {{"beat": "resolution", "description": "final outcome"}}
                ],
                "pacing": "slow/medium/fast",
                "perspective": "first_person/third_person/mixed",
                "timeline": "linear/non_linear/flashbacks",
                "emotional_journey": [
                    {{"stage": "beginning", "emotions": ["list of emotions"], "intensity": 1-10}},
                    {{"stage": "middle", "emotions": ["list of emotions"], "intensity": 1-10}},
                    {{"stage": "end", "emotions": ["list of emotions"], "intensity": 1-10}}
                ],
                "storytelling_techniques": ["dialogue", "metaphor", "imagery", "etc"],
                "effectiveness_score": 0.0-1.0
            }}
            """
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert in narrative analysis and storytelling structure."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1200
            )
            
            result = self._parse_classification_response(response.choices[0].message.content)
            logger.info("Narrative structure analyzed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing narrative structure: {e}")
            return {"error": str(e)}
    
    async def extract_key_insights(self, content: str) -> Dict[str, Any]:
        """
        Extract key insights and lessons from mental health content
        
        Args:
            content: Text content to analyze
        
        Returns:
            Key insights and actionable advice
        """
        try:
            prompt = f"""
            Extract key insights and actionable advice from this mental health content:

            Content: "{content[:2000]}..."

            Please provide analysis in JSON format:
            {{
                "main_message": "core message of the content",
                "key_insights": [
                    "insight 1",
                    "insight 2", 
                    "insight 3"
                ],
                "actionable_advice": [
                    "specific action 1",
                    "specific action 2",
                    "specific action 3"
                ],
                "coping_strategies": ["list of mentioned coping strategies"],
                "resources_mentioned": ["therapy", "medication", "support groups", "etc"],
                "warning_signs": ["list of warning signs mentioned"],
                "success_factors": ["what contributed to positive outcomes"],
                "barriers_overcome": ["obstacles that were addressed"],
                "support_systems": ["types of support mentioned"],
                "timeline_to_improvement": "how long recovery/improvement took",
                "applicability": "who this advice would be most helpful for"
            }}
            """
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a mental health professional analyzing content for insights and advice."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            result = self._parse_classification_response(response.choices[0].message.content)
            logger.info("Key insights extracted successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error extracting key insights: {e}")
            return {"error": str(e)}
    
    async def batch_classify_stories(self, contents: List[str]) -> List[Dict[str, Any]]:
        """
        Classify multiple stories in batch
        
        Args:
            contents: List of text contents to analyze
        
        Returns:
            List of classification results
        """
        results = []
        
        # Process in batches to avoid rate limits
        batch_size = 5
        for i in range(0, len(contents), batch_size):
            batch = contents[i:i + batch_size]
            
            # Process batch concurrently
            tasks = [self.classify_story(content) for content in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"Error in batch classification: {result}")
                    results.append(self._get_default_classification())
                else:
                    results.append(result)
            
            # Rate limiting between batches
            if i + batch_size < len(contents):
                await asyncio.sleep(1)
        
        logger.info(f"Batch classified {len(results)} stories")
        return results
