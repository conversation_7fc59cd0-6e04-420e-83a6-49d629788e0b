"""
Sentiment analysis service for mental health content
"""
import asyncio
import openai
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from app.core.config import settings


class SentimentAnalyzer:
    """AI-powered sentiment analysis for mental health content"""
    
    def __init__(self):
        self.client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = "gpt-4"
    
    async def analyze_sentiment(self, content: str) -> Dict[str, Any]:
        """
        Analyze sentiment of mental health content
        
        Args:
            content: Text content to analyze
        
        Returns:
            Detailed sentiment analysis results
        """
        try:
            prompt = self._build_sentiment_prompt(content)
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert in psychological sentiment analysis, particularly for mental health content."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=800
            )
            
            result = self._parse_sentiment_response(response.choices[0].message.content)
            
            logger.info(f"Sentiment analyzed: {result.get('overall_sentiment', 'unknown')}")
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return self._get_default_sentiment()
    
    def _build_sentiment_prompt(self, content: str) -> str:
        """Build prompt for sentiment analysis"""
        prompt = f"""
        Analyze the sentiment and emotional content of this mental health related text:

        Content: "{content[:2000]}..."

        Please provide detailed sentiment analysis in JSON format:
        {{
            "overall_sentiment": "positive/negative/neutral/mixed",
            "sentiment_score": -1.0 to 1.0,
            "emotional_intensity": 0.0 to 1.0,
            "primary_emotions": ["list of 3-5 primary emotions detected"],
            "emotional_progression": [
                {{"section": "beginning", "sentiment": "positive/negative/neutral", "emotions": ["emotion1", "emotion2"]}},
                {{"section": "middle", "sentiment": "positive/negative/neutral", "emotions": ["emotion1", "emotion2"]}},
                {{"section": "end", "sentiment": "positive/negative/neutral", "emotions": ["emotion1", "emotion2"]}}
            ],
            "hope_indicators": {{
                "has_hope": true/false,
                "hope_score": 0.0-1.0,
                "hope_phrases": ["specific phrases indicating hope"]
            }},
            "distress_indicators": {{
                "has_distress": true/false,
                "distress_level": "low/medium/high",
                "distress_phrases": ["specific phrases indicating distress"]
            }},
            "recovery_indicators": {{
                "shows_progress": true/false,
                "recovery_stage": "crisis/seeking_help/improving/maintaining/thriving",
                "progress_markers": ["specific indicators of progress"]
            }},
            "support_seeking": {{
                "seeks_support": true/false,
                "support_type": "professional/peer/family/online",
                "support_phrases": ["phrases indicating support seeking"]
            }},
            "risk_factors": {{
                "suicide_risk": "none/low/medium/high",
                "self_harm_mentions": true/false,
                "crisis_indicators": ["specific crisis indicators if any"]
            }},
            "resilience_factors": {{
                "shows_resilience": true/false,
                "coping_strategies": ["mentioned coping strategies"],
                "strength_indicators": ["phrases showing strength/resilience"]
            }},
            "confidence_score": 0.0-1.0
        }}
        """
        
        return prompt
    
    def _parse_sentiment_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the AI response into structured sentiment data"""
        try:
            import json
            import re
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                # Add metadata
                result["analyzed_at"] = datetime.utcnow().isoformat()
                result["model_version"] = self.model
                
                return result
            else:
                logger.warning("Could not extract JSON from sentiment response")
                return self._get_default_sentiment()
                
        except Exception as e:
            logger.error(f"Error parsing sentiment response: {e}")
            return self._get_default_sentiment()
    
    def _get_default_sentiment(self) -> Dict[str, Any]:
        """Return default sentiment when analysis fails"""
        return {
            "overall_sentiment": "neutral",
            "sentiment_score": 0.0,
            "emotional_intensity": 0.0,
            "primary_emotions": [],
            "emotional_progression": [],
            "hope_indicators": {
                "has_hope": False,
                "hope_score": 0.0,
                "hope_phrases": []
            },
            "distress_indicators": {
                "has_distress": False,
                "distress_level": "low",
                "distress_phrases": []
            },
            "recovery_indicators": {
                "shows_progress": False,
                "recovery_stage": "unknown",
                "progress_markers": []
            },
            "support_seeking": {
                "seeks_support": False,
                "support_type": "unknown",
                "support_phrases": []
            },
            "risk_factors": {
                "suicide_risk": "none",
                "self_harm_mentions": False,
                "crisis_indicators": []
            },
            "resilience_factors": {
                "shows_resilience": False,
                "coping_strategies": [],
                "strength_indicators": []
            },
            "confidence_score": 0.0,
            "analyzed_at": datetime.utcnow().isoformat(),
            "model_version": self.model
        }
    
    async def analyze_emotional_arc(self, content: str) -> Dict[str, Any]:
        """
        Analyze the emotional arc/journey in the content
        
        Args:
            content: Text content to analyze
        
        Returns:
            Emotional arc analysis
        """
        try:
            prompt = f"""
            Analyze the emotional journey/arc in this mental health story:

            Content: "{content[:2000]}..."

            Please provide emotional arc analysis in JSON format:
            {{
                "arc_type": "upward/downward/u_shaped/roller_coaster/flat",
                "emotional_stages": [
                    {{"stage": 1, "description": "stage description", "emotions": ["emotion1", "emotion2"], "intensity": 1-10}},
                    {{"stage": 2, "description": "stage description", "emotions": ["emotion1", "emotion2"], "intensity": 1-10}},
                    {{"stage": 3, "description": "stage description", "emotions": ["emotion1", "emotion2"], "intensity": 1-10}}
                ],
                "turning_points": [
                    {{"point": "description of turning point", "emotional_shift": "from X to Y"}}
                ],
                "emotional_range": {{
                    "lowest_point": {{"emotions": ["emotion1"], "intensity": 1-10}},
                    "highest_point": {{"emotions": ["emotion1"], "intensity": 1-10}}
                }},
                "resolution_quality": "positive/negative/ambiguous/unresolved",
                "emotional_complexity": "simple/moderate/complex",
                "authenticity_markers": ["specific emotional details that seem authentic"]
            }}
            """
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert in emotional analysis and psychological narrative patterns."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            result = self._parse_sentiment_response(response.choices[0].message.content)
            logger.info("Emotional arc analyzed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing emotional arc: {e}")
            return {"error": str(e)}
    
    async def detect_crisis_indicators(self, content: str) -> Dict[str, Any]:
        """
        Detect crisis indicators and risk factors in content
        
        Args:
            content: Text content to analyze
        
        Returns:
            Crisis detection results
        """
        try:
            prompt = f"""
            Analyze this mental health content for crisis indicators and risk factors:

            Content: "{content[:2000]}..."

            Please provide crisis analysis in JSON format:
            {{
                "crisis_level": "none/low/medium/high/severe",
                "immediate_risk": true/false,
                "risk_factors": {{
                    "suicide_ideation": true/false,
                    "self_harm": true/false,
                    "substance_abuse": true/false,
                    "social_isolation": true/false,
                    "hopelessness": true/false,
                    "psychosis": true/false
                }},
                "protective_factors": {{
                    "support_system": true/false,
                    "professional_help": true/false,
                    "coping_skills": true/false,
                    "future_plans": true/false,
                    "reasons_to_live": true/false
                }},
                "warning_phrases": ["specific phrases that indicate risk"],
                "help_seeking": {{
                    "mentions_therapy": true/false,
                    "mentions_medication": true/false,
                    "mentions_hotline": true/false,
                    "asks_for_help": true/false
                }},
                "recommendations": [
                    "immediate actions recommended",
                    "resources to suggest"
                ],
                "urgency_score": 0.0-1.0
            }}
            """
            
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a mental health crisis assessment expert. Focus on identifying risk factors and protective factors."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Lower temperature for more consistent crisis detection
                max_tokens=800
            )
            
            result = self._parse_sentiment_response(response.choices[0].message.content)
            logger.info(f"Crisis indicators analyzed: {result.get('crisis_level', 'unknown')}")
            return result
            
        except Exception as e:
            logger.error(f"Error detecting crisis indicators: {e}")
            return {"error": str(e)}
    
    async def batch_analyze_sentiment(self, contents: List[str]) -> List[Dict[str, Any]]:
        """
        Analyze sentiment for multiple contents in batch
        
        Args:
            contents: List of text contents to analyze
        
        Returns:
            List of sentiment analysis results
        """
        results = []
        
        # Process in batches to avoid rate limits
        batch_size = 5
        for i in range(0, len(contents), batch_size):
            batch = contents[i:i + batch_size]
            
            # Process batch concurrently
            tasks = [self.analyze_sentiment(content) for content in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"Error in batch sentiment analysis: {result}")
                    results.append(self._get_default_sentiment())
                else:
                    results.append(result)
            
            # Rate limiting between batches
            if i + batch_size < len(contents):
                await asyncio.sleep(1)
        
        logger.info(f"Batch analyzed sentiment for {len(results)} contents")
        return results
