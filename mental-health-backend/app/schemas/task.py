"""
Pydantic schemas for task-related API endpoints
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from app.models.task import TaskStatus, TaskType


class TaskBase(BaseModel):
    """Base task schema"""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    task_type: TaskType = TaskType.COLLECTION
    platforms: List[str] = Field(..., min_items=1)
    keywords: Optional[List[str]] = None
    filters: Optional[Dict[str, Any]] = None
    analysis_config: Optional[Dict[str, Any]] = None


class TaskCreate(TaskBase):
    """Schema for creating a new task"""
    is_recurring: bool = False
    schedule_config: Optional[Dict[str, Any]] = None
    max_retries: int = Field(default=3, ge=0, le=10)


class TaskUpdate(BaseModel):
    """Schema for updating an existing task"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    platforms: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    filters: Optional[Dict[str, Any]] = None
    analysis_config: Optional[Dict[str, Any]] = None
    is_recurring: Optional[bool] = None
    schedule_config: Optional[Dict[str, Any]] = None
    max_retries: Optional[int] = Field(None, ge=0, le=10)


class TaskResponse(TaskBase):
    """Schema for task response"""
    id: int
    status: TaskStatus
    celery_task_id: Optional[str] = None
    progress_percentage: int = 0
    items_collected: int = 0
    items_analyzed: int = 0
    items_failed: int = 0
    error_message: Optional[str] = None
    retry_count: int = 0
    results_summary: Optional[Dict[str, Any]] = None
    export_urls: Optional[List[str]] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class TaskListResponse(BaseModel):
    """Schema for paginated task list response"""
    tasks: List[TaskResponse]
    total: int
    skip: int
    limit: int


class TaskStatusResponse(BaseModel):
    """Schema for task status response"""
    id: int
    status: TaskStatus
    progress_percentage: int
    items_collected: int
    items_analyzed: int
    items_failed: int
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None


class TaskLogResponse(BaseModel):
    """Schema for task log response"""
    id: int
    level: str
    message: str
    details: Optional[Dict[str, Any]] = None
    created_at: datetime
    
    class Config:
        from_attributes = True
