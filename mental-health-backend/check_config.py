"""
检查当前API配置的脚本
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def check_api_config():
    """检查API配置状态"""
    print("🔍 检查API配置状态...")
    print("=" * 50)
    
    # 检查Gemini配置
    gemini_key = os.getenv("GEMINI_API_KEY")
    gemini_model = os.getenv("GEMINI_MODEL", "gemini-1.5-flash")
    ai_provider = os.getenv("AI_PROVIDER", "gemini")
    
    print(f"🤖 AI Provider: {ai_provider}")
    print(f"📱 Gemini Model: {gemini_model}")
    
    if gemini_key:
        # 只显示密钥的前8位和后4位，中间用*代替
        masked_key = f"{gemini_key[:8]}...{gemini_key[-4:]}" if len(gemini_key) > 12 else "***"
        print(f"🔑 Gemini API Key: {masked_key}")
        print(f"✅ Gemini API密钥已配置")
    else:
        print(f"❌ Gemini API密钥未配置")
        print(f"请在.env文件中设置 GEMINI_API_KEY")
    
    # 检查其他API密钥
    print(f"\n📋 其他API配置状态:")
    
    apis = {
        "Reddit Client ID": os.getenv("REDDIT_CLIENT_ID"),
        "Reddit Client Secret": os.getenv("REDDIT_CLIENT_SECRET"),
        "YouTube API Key": os.getenv("YOUTUBE_API_KEY"),
        "OpenAI API Key": os.getenv("OPENAI_API_KEY"),
        "Spotify Client ID": os.getenv("SPOTIFY_CLIENT_ID"),
    }
    
    for name, key in apis.items():
        if key:
            masked = f"{key[:6]}...{key[-4:]}" if len(key) > 10 else "***"
            print(f"   ✅ {name}: {masked}")
        else:
            print(f"   ❌ {name}: 未配置")
    
    # 检查数据库配置
    db_url = os.getenv("DATABASE_URL")
    redis_url = os.getenv("REDIS_URL")
    
    print(f"\n🗄️  数据库配置:")
    print(f"   PostgreSQL: {'✅ 已配置' if db_url else '❌ 未配置'}")
    print(f"   Redis: {'✅ 已配置' if redis_url else '❌ 未配置'}")
    
    # 给出建议
    print(f"\n💡 配置建议:")
    if not gemini_key:
        print(f"   1. 获取Gemini API密钥: https://makersuite.google.com/app/apikey")
        print(f"   2. 在.env文件中设置: GEMINI_API_KEY=your-api-key")
    
    if ai_provider == "gemini" and "pro" in gemini_model.lower():
        print(f"   ✨ 你正在使用Gemini Pro - 享受高质量AI分析!")
    elif ai_provider == "gemini":
        print(f"   💡 考虑升级到Gemini Pro以获得更好的分析质量")
    
    print(f"\n🧪 测试命令:")
    print(f"   python test_gemini_pro.py")
    print(f"   python test_reddit_scraper.py")

if __name__ == "__main__":
    check_config()
