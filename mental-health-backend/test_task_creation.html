<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务创建测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #1890ff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <h1>任务创建测试</h1>
    
    <form id="taskForm">
        <div class="form-group">
            <label for="taskName">任务名称:</label>
            <input type="text" id="taskName" value="测试任务" required>
        </div>
        
        <div class="form-group">
            <label for="platforms">平台 (多选):</label>
            <select id="platforms" multiple required>
                <option value="reddit">Reddit</option>
                <option value="youtube">YouTube</option>
                <option value="tiktok">TikTok</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="keywords">关键词 (逗号分隔):</label>
            <input type="text" id="keywords" value="mental health, therapy, wellness" required>
        </div>
        
        <div class="form-group">
            <label for="maxResults">采集数量:</label>
            <select id="maxResults" required>
                <option value="5">5条</option>
                <option value="10" selected>10条</option>
                <option value="20">20条</option>
                <option value="50">50条</option>
            </select>
        </div>
        
        <button type="submit" id="submitBtn">创建任务</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('taskForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // 禁用按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '创建中...';
            
            // 清除之前的结果
            resultDiv.innerHTML = '';
            
            try {
                // 收集表单数据
                const taskName = document.getElementById('taskName').value;
                const platformsSelect = document.getElementById('platforms');
                const platforms = Array.from(platformsSelect.selectedOptions).map(option => option.value);
                const keywordsInput = document.getElementById('keywords').value;
                const keywords = keywordsInput.split(',').map(k => k.trim()).filter(k => k);
                const maxResults = parseInt(document.getElementById('maxResults').value);
                
                const taskData = {
                    name: taskName,
                    platforms: platforms,
                    keywords: keywords,
                    max_results: maxResults
                };
                
                console.log('发送任务数据:', taskData);
                
                // 显示请求信息
                resultDiv.innerHTML = `
                    <div class="result info">
                        <h3>正在发送请求...</h3>
                        <p>URL: http://localhost:8000/tasks</p>
                        <p>数据: ${JSON.stringify(taskData, null, 2)}</p>
                    </div>
                `;
                
                // 发送请求
                const response = await fetch('http://localhost:8000/tasks', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(taskData)
                });
                
                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('响应数据:', result);
                
                // 显示成功结果
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ 任务创建成功!</h3>
                        <p><strong>任务ID:</strong> ${result.id}</p>
                        <p><strong>任务名称:</strong> ${result.name}</p>
                        <p><strong>状态:</strong> ${result.status}</p>
                        <p><strong>创建时间:</strong> ${result.created_at}</p>
                        <button onclick="executeTask('${result.id}')">立即执行任务</button>
                    </div>
                `;
                
            } catch (error) {
                console.error('创建任务失败:', error);
                
                // 显示错误结果
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 任务创建失败</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <p>请检查:</p>
                        <ul>
                            <li>后端服务器是否在运行 (http://localhost:8000)</li>
                            <li>网络连接是否正常</li>
                            <li>浏览器控制台是否有其他错误</li>
                        </ul>
                    </div>
                `;
            } finally {
                // 恢复按钮
                submitBtn.disabled = false;
                submitBtn.textContent = '创建任务';
            }
        });
        
        async function executeTask(taskId) {
            try {
                const response = await fetch(`http://localhost:8000/tasks/${taskId}/execute`, {
                    method: 'POST'
                });
                
                if (response.ok) {
                    alert('任务执行已启动!');
                } else {
                    alert('任务执行失败: ' + response.statusText);
                }
            } catch (error) {
                alert('任务执行失败: ' + error.message);
            }
        }
        
        // 页面加载时测试连接
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('http://localhost:8000/health');
                if (response.ok) {
                    const health = await response.json();
                    console.log('后端服务器健康状态:', health);
                } else {
                    console.warn('后端服务器连接失败');
                }
            } catch (error) {
                console.error('无法连接到后端服务器:', error);
            }
        });
    </script>
</body>
</html>
