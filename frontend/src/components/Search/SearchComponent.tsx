import React, { useState } from 'react';
import { Card, Input, Select, Button, Form, Row, Col, message, Table, Tag } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { Platform } from '../../types';
import axios from 'axios';

const { Search } = Input;
const { Option } = Select;

interface SearchComponentProps {
  onSearch?: (query: string, filters: any) => void;
}

const SearchComponent: React.FC<SearchComponentProps> = ({ onSearch }) => {
  const [form] = Form.useForm();
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);

  const handleSearch = async () => {
    setLoading(true);
    try {
      const filters = form.getFieldsValue();

      const response = await axios.get('http://localhost:8000/content', {
        params: {
          query: searchQuery,
          platform: filters.platform,
          sort_by: filters.sortBy,
          sort_order: filters.sortOrder,
          min_likes: filters.minLikes || 0,
          min_comments: filters.minComments || 0,
          limit: 20
        }
      });

      setSearchResults(response.data.content || []);
      message.success(`找到 ${response.data.filtered_total} 条内容`);

      if (onSearch) {
        onSearch(searchQuery, filters);
      }
    } catch (error: any) {
      console.error('Search failed:', error);
      message.error('搜索失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (title: string) => title?.substring(0, 50) + (title?.length > 50 ? '...' : ''),
    },
    {
      title: '平台',
      dataIndex: 'platform',
      key: 'platform',
      render: (platform: string) => (
        <Tag color={platform === 'reddit' ? 'orange' : 'red'}>
          {platform === 'reddit' ? 'Reddit' : 'YouTube'}
        </Tag>
      ),
    },
    {
      title: '作者/频道',
      dataIndex: 'author',
      key: 'author',
      render: (author: string, record: any) => author || record.channel || 'Unknown',
    },
    {
      title: '互动数据',
      key: 'engagement',
      render: (_, record: any) => (
        <div>
          <div>👍 {record.upvotes || record.likes || 0}</div>
          <div>💬 {record.comments || 0}</div>
          {record.views && <div>👁️ {record.views}</div>}
        </div>
      ),
    },
    {
      title: '采集时间',
      dataIndex: 'collected_at',
      key: 'collected_at',
      render: (time: string) => {
        if (!time) return '-';
        const date = new Date(time);
        return date.toLocaleString('zh-CN');
      },
    },
  ];

  return (
    <div style={{ margin: '16px' }}>
      <Card
        title={
          <span>
            <SearchOutlined style={{ marginRight: 8 }} />
            内容搜索
          </span>
        }
        style={{ marginBottom: '16px' }}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label="搜索关键词">
                <Search
                  placeholder="输入关键词搜索内容"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onSearch={handleSearch}
                  enterButton="搜索"
                  loading={loading}
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} sm={8}>
              <Form.Item name="platform" label="平台筛选">
                <Select placeholder="选择平台" allowClear>
                  <Option value="reddit">Reddit</Option>
                  <Option value="youtube">YouTube</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={8}>
              <Form.Item name="sortBy" label="排序方式">
                <Select placeholder="选择排序方式" defaultValue="collected_at">
                  <Option value="likes">按点赞数</Option>
                  <Option value="comments">按评论数</Option>
                  <Option value="views">按观看数</Option>
                  <Option value="collected_at">按采集时间</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col xs={24} sm={8}>
              <Form.Item name="sortOrder" label="排序顺序">
                <Select placeholder="选择排序顺序" defaultValue="desc">
                  <Option value="desc">降序</Option>
                  <Option value="asc">升序</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} sm={12}>
              <Form.Item name="minLikes" label="最低点赞数">
                <Input type="number" placeholder="输入最低点赞数" />
              </Form.Item>
            </Col>

            <Col xs={24} sm={12}>
              <Form.Item name="minComments" label="最低评论数">
                <Input type="number" placeholder="输入最低评论数" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Button type="primary" onClick={handleSearch} loading={loading} block>
              搜索内容
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {searchResults.length > 0 && (
        <Card title={`搜索结果 (${searchResults.length} 条)`}>
          <Table
            columns={columns}
            dataSource={searchResults}
            rowKey="id"
            pagination={{ pageSize: 10 }}
            scroll={{ x: 800 }}
          />
        </Card>
      )}
    </div>
  );
};

export default SearchComponent;