import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Tag,
  Progress,
  Select,
  DatePicker,
  Button,
  Space,
  Tooltip,
  Alert
} from 'antd';
import {
  HeartOutlined,
  BrainOutlined,
  TrophyOutlined,
  WarningOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { Line, Pie, Bar } from '@ant-design/plots';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface AnalyticsData {
  totalContent: number;
  analyzedContent: number;
  storyCategories: { category: string; count: number; percentage: number }[];
  sentimentDistribution: { sentiment: string; count: number; percentage: number }[];
  engagementTrends: { date: string; engagement: number; sentiment: number }[];
  topStoryPatterns: { pattern: string; effectiveness: number; count: number }[];
  riskIndicators: { level: string; count: number; percentage: number }[];
}

const MentalHealthAnalytics: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState<string>('7d');
  const [platform, setPlatform] = useState<string>('all');
  const [data, setData] = useState<AnalyticsData>({
    totalContent: 0,
    analyzedContent: 0,
    storyCategories: [],
    sentimentDistribution: [],
    engagementTrends: [],
    topStoryPatterns: [],
    riskIndicators: []
  });

  // Mock data for demonstration
  useEffect(() => {
    setData({
      totalContent: 15420,
      analyzedContent: 12336,
      storyCategories: [
        { category: '抑郁康复', count: 3245, percentage: 26.3 },
        { category: '焦虑管理', count: 2891, percentage: 23.4 },
        { category: '失业恢复', count: 1876, percentage: 15.2 },
        { category: '失恋治愈', count: 1654, percentage: 13.4 },
        { category: '社交焦虑', count: 1432, percentage: 11.6 },
        { category: '孤独解决', count: 1238, percentage: 10.1 }
      ],
      sentimentDistribution: [
        { sentiment: '积极', count: 7402, percentage: 60.0 },
        { sentiment: '中性', count: 3084, percentage: 25.0 },
        { sentiment: '消极', count: 1850, percentage: 15.0 }
      ],
      engagementTrends: [
        { date: '2024-01-01', engagement: 85, sentiment: 0.6 },
        { date: '2024-01-02', engagement: 92, sentiment: 0.65 },
        { date: '2024-01-03', engagement: 78, sentiment: 0.58 },
        { date: '2024-01-04', engagement: 95, sentiment: 0.72 },
        { date: '2024-01-05', engagement: 88, sentiment: 0.68 },
        { date: '2024-01-06', engagement: 91, sentiment: 0.71 },
        { date: '2024-01-07', engagement: 87, sentiment: 0.66 }
      ],
      topStoryPatterns: [
        { pattern: '英雄之旅', effectiveness: 92, count: 2341 },
        { pattern: '问题解决', effectiveness: 88, count: 1987 },
        { pattern: '前后对比', effectiveness: 85, count: 1654 },
        { pattern: '危机转机', effectiveness: 83, count: 1432 },
        { pattern: '渐进改善', effectiveness: 80, count: 1298 }
      ],
      riskIndicators: [
        { level: '无风险', count: 9869, percentage: 80.0 },
        { level: '低风险', count: 1850, percentage: 15.0 },
        { level: '中风险', count: 493, percentage: 4.0 },
        { level: '高风险', count: 124, percentage: 1.0 }
      ]
    });
  }, [timeRange, platform]);

  const handleRefresh = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  const handleExport = () => {
    console.log('Exporting analytics data...');
  };

  // Chart configurations
  const sentimentPieConfig = {
    data: data.sentimentDistribution,
    angleField: 'count',
    colorField: 'sentiment',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name} {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };

  const engagementLineConfig = {
    data: data.engagementTrends,
    xField: 'date',
    yField: 'engagement',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 5,
      shape: 'diamond',
    },
  };

  const categoryBarConfig = {
    data: data.storyCategories,
    xField: 'count',
    yField: 'category',
    color: '#52c41a',
    label: {
      position: 'middle',
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
  };

  const patternColumns = [
    {
      title: '故事模式',
      dataIndex: 'pattern',
      key: 'pattern',
    },
    {
      title: '有效性',
      dataIndex: 'effectiveness',
      key: 'effectiveness',
      render: (value: number) => (
        <Progress percent={value} size="small" status="active" />
      ),
    },
    {
      title: '出现次数',
      dataIndex: 'count',
      key: 'count',
      render: (value: number) => value.toLocaleString(),
    },
  ];

  return (
    <div>
      {/* Controls */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col>
            <Space>
              <span>时间范围:</span>
              <Select value={timeRange} onChange={setTimeRange} style={{ width: 120 }}>
                <Option value="1d">今天</Option>
                <Option value="7d">最近7天</Option>
                <Option value="30d">最近30天</Option>
                <Option value="90d">最近90天</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <span>平台:</span>
              <Select value={platform} onChange={setPlatform} style={{ width: 120 }}>
                <Option value="all">全部</Option>
                <Option value="reddit">Reddit</Option>
                <Option value="youtube">YouTube</Option>
                <Option value="tiktok">TikTok</Option>
                <Option value="podcast">播客</Option>
              </Select>
            </Space>
          </Col>
          <Col flex="auto" />
          <Col>
            <Space>
              <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
                刷新
              </Button>
              <Button icon={<DownloadOutlined />} onClick={handleExport}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Key Metrics */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总内容数"
              value={data.totalContent}
              prefix={<BrainOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已分析内容"
              value={data.analyzedContent}
              prefix={<HeartOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix={`/ ${data.totalContent}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="分析完成率"
              value={((data.analyzedContent / data.totalContent) * 100).toFixed(1)}
              prefix={<TrophyOutlined />}
              suffix="%"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="高风险内容"
              value={data.riskIndicators.find(r => r.level === '高风险')?.count || 0}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Risk Alert */}
      {data.riskIndicators.find(r => r.level === '高风险')?.count > 0 && (
        <Alert
          message="风险提醒"
          description={`检测到 ${data.riskIndicators.find(r => r.level === '高风险')?.count} 条高风险内容，建议及时关注和处理。`}
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      {/* Charts */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="情感分布" extra={<Tooltip title="内容情感倾向分析"><HeartOutlined /></Tooltip>}>
            <Pie {...sentimentPieConfig} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="参与度趋势" extra={<Tooltip title="用户参与度变化趋势"><TrophyOutlined /></Tooltip>}>
            <Line {...engagementLineConfig} />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title="故事类别分布" extra={<Tooltip title="不同心理健康话题的内容分布"><BrainOutlined /></Tooltip>}>
            <Bar {...categoryBarConfig} />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="有效故事模式" extra={<Tooltip title="最有效的故事讲述模式"><TrophyOutlined /></Tooltip>}>
            <Table
              dataSource={data.topStoryPatterns}
              columns={patternColumns}
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* Risk Distribution */}
      <Card title="风险等级分布" extra={<Tooltip title="内容风险等级统计"><WarningOutlined /></Tooltip>}>
        <Row gutter={16}>
          {data.riskIndicators.map((risk, index) => (
            <Col span={6} key={index}>
              <Card size="small">
                <Statistic
                  title={risk.level}
                  value={risk.count}
                  suffix={`(${risk.percentage}%)`}
                  valueStyle={{
                    color: risk.level === '高风险' ? '#ff4d4f' :
                           risk.level === '中风险' ? '#faad14' :
                           risk.level === '低风险' ? '#1890ff' : '#52c41a'
                  }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    </div>
  );
};

export default MentalHealthAnalytics;
