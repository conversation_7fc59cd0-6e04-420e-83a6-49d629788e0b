import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { StoryTheme, ExpandableCard } from '../../types';

interface ExpandableCardsProps {
  storyId: string;
  theme: StoryTheme;
}

export const ExpandableCards: React.FC<ExpandableCardsProps> = ({ storyId, theme }) => {
  const [expandedCards, setExpandedCards] = useState<string[]>([]);

  // 模拟卡片数据 - 将来会从API获取
  const cards: ExpandableCard[] = [
    {
      id: 'first_step',
      title: '改变的第一步是什么？',
      content: '第二天在会议上我能够进行对话了吗？并不能。但改变的是，我对此感到没关系了。我允许自己保持安静而不因此恨自己。第一步不是成为一个新的人，而是接受当时的自己。',
      type: 'next_step',
      isExpanded: false
    },
    {
      id: 'method',
      title: '一个真正有效的方法',
      content: 'Ali博士的视频提到将脸浸入冷水中来冷静下来。听起来很傻，但这是一个改变游戏规则的方法。那种冷的冲击可以让你的神经系统从恐慌螺旋中跳出来。它给了我一个我真正可以按下的物理"暂停按钮"。',
      type: 'method',
      isExpanded: false
    },
    {
      id: 'author_message',
      title: '来自故事作者的信息',
      content: '如果我能对在那个酒店房间里的自己说一句话，那就是："这不是性格缺陷。它不叫'可悲'或'失败'。它是一个有名字的状况。你的首要任务不是修复自己，而是理解自己。"',
      type: 'author_message',
      isExpanded: false
    }
  ];

  const toggleCard = (cardId: string) => {
    setExpandedCards(prev =>
      prev.includes(cardId)
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  const getCardIcon = (type: ExpandableCard['type']): string => {
    switch (type) {
      case 'next_step':
        return '🚀';
      case 'method':
        return '🛠️';
      case 'author_message':
        return '💌';
      case 'community_echo':
        return '🌊';
      default:
        return '✨';
    }
  };

  return (
    <div className="expandable-cards">
      <motion.div
        className="cards-header"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h3 className="cards-title">故事的延续</h3>
        <p className="cards-subtitle">点击卡片了解更多...</p>
      </motion.div>

      <div className="cards-container">
        {cards.map((card, index) => (
          <motion.div
            key={card.id}
            className={`expandable-card ${expandedCards.includes(card.id) ? 'expanded' : ''}`}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <motion.div
              className="card-header"
              onClick={() => toggleCard(card.id)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="card-icon">{getCardIcon(card.type)}</div>
              <h4 className="card-title">{card.title}</h4>
              <motion.div
                className="card-toggle"
                animate={{ rotate: expandedCards.includes(card.id) ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                ▼
              </motion.div>
            </motion.div>

            <AnimatePresence>
              {expandedCards.includes(card.id) && (
                <motion.div
                  className="card-content"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <p className="card-text">{card.content}</p>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>
    </div>
  );
};