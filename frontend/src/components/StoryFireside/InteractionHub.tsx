import React from 'react';
import { motion } from 'framer-motion';
import { InteractionPath, StoryTheme } from '../../types';

interface InteractionHubProps {
  paths: InteractionPath[];
  theme: StoryTheme;
}

export const InteractionHub: React.FC<InteractionHubProps> = ({ paths, theme }) => {
  return (
    <div className="interaction-hub">
      <motion.div
        className="interaction-header"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h3 className="interaction-title">你可以在这里选择你的探索路径：</h3>
      </motion.div>

      <div className="interaction-paths">
        {paths.map((path, index) => (
          <motion.div
            key={path.id}
            className="interaction-path"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              duration: 0.5,
              delay: index * 0.2
            }}
            whileHover={{
              scale: 1.02,
              boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
            }}
            whileTap={{ scale: 0.98 }}
            onClick={path.action}
          >
            <div className="path-content">
              <div className="path-icon">
                {getPathIcon(path.type)}
              </div>
              <div className="path-text">
                <h4 className="path-label">{path.label}</h4>
                <p className="path-description">{path.description}</p>
              </div>
              <div className="path-arrow">→</div>
            </div>
          </motion.div>
        ))}
      </div>

      <motion.div
        className="interaction-footer"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1, duration: 0.6 }}
      >
        <p className="interaction-footer-text">
          每个选择都会带你进入不同的体验层次...
        </p>
      </motion.div>
    </div>
  );
};

const getPathIcon = (type: InteractionPath['type']): string => {
  switch (type) {
    case 'dive_deeper':
      return '🔍';
    case 'personal_reflection':
      return '💭';
    case 'continue_story':
      return '📖';
    case 'explore_theme':
      return '🌟';
    default:
      return '✨';
  }
};