import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { StoryTheme } from '../../types';

interface UserReflectionSpaceProps {
  theme: StoryTheme;
  onClose: () => void;
}

export const UserReflectionSpace: React.FC<UserReflectionSpaceProps> = ({
  theme,
  onClose
}) => {
  const [userInput, setUserInput] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = () => {
    if (userInput.trim()) {
      setIsSubmitted(true);
      // 这里可以添加提交到后端的逻辑
      setTimeout(() => {
        onClose();
      }, 3000);
    }
  };

  return (
    <motion.div
      className="user-reflection-overlay"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="user-reflection-space"
        initial={{ opacity: 0, scale: 0.9, y: 50 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 50 }}
        transition={{ duration: 0.3 }}
        onClick={(e) => e.stopPropagation()}
      >
        {!isSubmitted ? (
          <>
            <div className="reflection-header">
              <button className="close-button" onClick={onClose}>×</button>
              <h3 className="reflection-title">谢谢你。这个故事也触动了你。</h3>
              <p className="reflection-subtitle">
                下面的空间是为你准备的——一个绝对安全的分享地方。
              </p>
            </div>

            <div className="reflection-content">
              <p className="reflection-prompt">
                如果你愿意，你可以在这里留下你的回响。可以是一个词、一句话，或者一个完整的时刻。
              </p>
              <p className="reflection-promise">
                它将被小心地保存。
              </p>

              <textarea
                className="reflection-input"
                placeholder="在这里分享你的想法..."
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                rows={6}
              />

              <div className="reflection-actions">
                <motion.button
                  className="submit-button"
                  onClick={handleSubmit}
                  disabled={!userInput.trim()}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  分享我的回响
                </motion.button>
                <button className="cancel-button" onClick={onClose}>
                  暂时不分享
                </button>
              </div>
            </div>
          </>
        ) : (
          <motion.div
            className="reflection-success"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="success-icon">💝</div>
            <h3 className="success-title">感谢你信任这个空间</h3>
            <p className="success-message">
              在你分享的内容中，我能听到我们社区中其他故事的回响。
              比如，其他人也提到过"想要隐形"或"感觉像个骗子"的感受。
            </p>
            <p className="success-message">
              你看，在这个炉边，我们经常分享相似的感受。
            </p>
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  );
};