/* Story Fireside - 温暖故事化的样式 */

/* 全局变量 */
:root {
  --fireside-warm-orange: #ff8a65;
  --fireside-soft-yellow: #fff3e0;
  --fireside-deep-brown: #5d4037;
  --fireside-cream: #faf7f2;
  --fireside-shadow: rgba(93, 64, 55, 0.1);
  --fireside-text-primary: #3e2723;
  --fireside-text-secondary: #6d4c41;
  --fireside-accent: #ff7043;
  --fireside-gradient-warm: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
}

/* 主容器 */
.story-fireside {
  min-height: 100vh;
  background: var(--theme-gradient, var(--fireside-gradient-warm));
  position: relative;
  overflow-x: hidden;
}

.story-fireside::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 138, 101, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 204, 128, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.story-fireside-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

/* 加载状态 */
.story-fireside-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: var(--fireside-text-primary);
}

.loading-flame {
  font-size: 3rem;
  animation: flicker 2s ease-in-out infinite alternate;
}

@keyframes flicker {
  0% { transform: scale(1) rotate(-2deg); opacity: 0.8; }
  100% { transform: scale(1.1) rotate(2deg); opacity: 1; }
}

/* 故事头部 */
.story-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px var(--fireside-shadow);
}

.story-theme-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: inline-block;
  animation: gentle-float 3s ease-in-out infinite;
}

@keyframes gentle-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.story-title {
  font-size: 2.5rem;
  color: var(--fireside-text-primary);
  margin: 1rem 0;
  font-weight: 600;
  line-height: 1.2;
  font-family: 'Georgia', serif;
}

.story-meta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  color: var(--fireside-text-secondary);
  font-size: 0.9rem;
}

.story-author, .story-date {
  padding: 0.5rem 1rem;
  background: rgba(255, 138, 101, 0.1);
  border-radius: 20px;
}

/* 故事层级 */
.story-layer {
  margin-bottom: 2rem;
}

.back-button {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid var(--fireside-accent);
  color: var(--fireside-accent);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: var(--fireside-accent);
  color: white;
  transform: translateX(-5px);
}

.story-content {
  background: rgba(255, 255, 255, 0.8);
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px var(--fireside-shadow);
  backdrop-filter: blur(10px);
  margin-bottom: 2rem;
}

.story-intro {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 138, 101, 0.05);
  border-radius: 15px;
  border-left: 4px solid var(--fireside-accent);
}

.story-intro-text {
  color: var(--fireside-text-secondary);
  font-style: italic;
  margin: 0;
  line-height: 1.6;
}

.story-main-content {
  margin: 2rem 0;
}

.story-content-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--fireside-text-primary);
  margin: 0;
  font-family: 'Georgia', serif;
}

.story-conclusion {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 204, 128, 0.1);
  border-radius: 15px;
  text-align: center;
}

.story-conclusion-text {
  color: var(--fireside-text-secondary);
  font-style: italic;
  margin: 0;
  font-size: 0.95rem;
}

/* 热点样式 */
.story-hotspot {
  background: linear-gradient(120deg, rgba(255, 138, 101, 0.2) 0%, rgba(255, 204, 128, 0.2) 100%);
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid var(--fireside-accent);
  position: relative;
}

.story-hotspot:hover {
  background: linear-gradient(120deg, rgba(255, 138, 101, 0.3) 0%, rgba(255, 204, 128, 0.3) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 138, 101, 0.3);
}

.story-hotspot.active {
  background: linear-gradient(120deg, var(--fireside-accent) 0%, #ffab40 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(255, 138, 101, 0.4);
}

/* 热点展开内容 */
.hotspot-expansion {
  margin-top: 2rem;
  padding: 2rem;
  background: rgba(255, 138, 101, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 138, 101, 0.2);
}

.hotspot-title {
  color: var(--fireside-accent);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.hotspot-text {
  color: var(--fireside-text-primary);
  line-height: 1.7;
  font-style: italic;
  margin: 0;
}

/* 交互中心 */
.interaction-hub {
  margin: 3rem 0;
}

.interaction-header {
  text-align: center;
  margin-bottom: 2rem;
}

.interaction-title {
  color: var(--fireside-text-primary);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-family: 'Georgia', serif;
}

.interaction-paths {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.interaction-path {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
}

.interaction-path:hover {
  border-color: var(--fireside-accent);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 138, 101, 0.2);
}

.path-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.path-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.path-text {
  flex: 1;
}

.path-label {
  color: var(--fireside-text-primary);
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.path-description {
  color: var(--fireside-text-secondary);
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.path-arrow {
  font-size: 1.5rem;
  color: var(--fireside-accent);
  transition: transform 0.3s ease;
}

.interaction-path:hover .path-arrow {
  transform: translateX(5px);
}

.interaction-footer {
  text-align: center;
  margin-top: 2rem;
}

.interaction-footer-text {
  color: var(--fireside-text-secondary);
  font-style: italic;
  margin: 0;
  font-size: 0.9rem;
}

/* 用户反思空间 */
.user-reflection-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.user-reflection-space {
  background: var(--fireside-cream);
  border-radius: 20px;
  padding: 2rem;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  position: relative;
}

.reflection-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.close-button {
  position: absolute;
  top: -1rem;
  right: -1rem;
  background: var(--fireside-accent);
  color: white;
  border: none;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: var(--fireside-deep-brown);
  transform: scale(1.1);
}

.reflection-title {
  color: var(--fireside-text-primary);
  margin-bottom: 1rem;
  font-family: 'Georgia', serif;
}

.reflection-subtitle {
  color: var(--fireside-text-secondary);
  margin: 0;
  line-height: 1.6;
}

.reflection-content {
  text-align: left;
}

.reflection-prompt, .reflection-promise {
  color: var(--fireside-text-primary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.reflection-promise {
  font-style: italic;
  color: var(--fireside-text-secondary);
}

.reflection-input {
  width: 100%;
  padding: 1rem;
  border: 2px solid rgba(255, 138, 101, 0.3);
  border-radius: 10px;
  font-size: 1rem;
  line-height: 1.5;
  resize: vertical;
  font-family: inherit;
  background: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.5rem;
}

.reflection-input:focus {
  outline: none;
  border-color: var(--fireside-accent);
  box-shadow: 0 0 0 3px rgba(255, 138, 101, 0.1);
}

.reflection-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.submit-button {
  background: var(--fireside-accent);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submit-button:hover:not(:disabled) {
  background: var(--fireside-deep-brown);
  transform: translateY(-1px);
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.cancel-button {
  background: transparent;
  color: var(--fireside-text-secondary);
  border: 2px solid rgba(255, 138, 101, 0.3);
  padding: 0.75rem 2rem;
  border-radius: 25px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-button:hover {
  border-color: var(--fireside-accent);
  color: var(--fireside-accent);
}

.reflection-success {
  text-align: center;
}

.success-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.success-title {
  color: var(--fireside-text-primary);
  margin-bottom: 1rem;
  font-family: 'Georgia', serif;
}

.success-message {
  color: var(--fireside-text-primary);
  line-height: 1.6;
  margin-bottom: 1rem;
}

/* 可展开卡片 */
.expandable-cards {
  margin-top: 3rem;
}

.cards-header {
  text-align: center;
  margin-bottom: 2rem;
}

.cards-title {
  color: var(--fireside-text-primary);
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-family: 'Georgia', serif;
}

.cards-subtitle {
  color: var(--fireside-text-secondary);
  margin: 0;
  font-style: italic;
}

.cards-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.expandable-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px var(--fireside-shadow);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.expandable-card:hover {
  box-shadow: 0 8px 25px rgba(255, 138, 101, 0.2);
  transform: translateY(-2px);
}

.expandable-card.expanded {
  box-shadow: 0 8px 30px rgba(255, 138, 101, 0.3);
}

.card-header {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  cursor: pointer;
  transition: background 0.3s ease;
}

.card-header:hover {
  background: rgba(255, 138, 101, 0.05);
}

.card-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.card-title {
  flex: 1;
  color: var(--fireside-text-primary);
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.card-toggle {
  color: var(--fireside-accent);
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.card-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid rgba(255, 138, 101, 0.1);
}

.card-text {
  color: var(--fireside-text-primary);
  line-height: 1.7;
  margin: 1rem 0 0 0;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .story-fireside-container {
    padding: 1rem;
  }

  .story-header {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .story-title {
    font-size: 2rem;
  }

  .story-content {
    padding: 2rem;
  }

  .story-meta {
    flex-direction: column;
    gap: 1rem;
  }

  .interaction-paths {
    gap: 1rem;
  }

  .path-content {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .path-arrow {
    transform: rotate(90deg);
  }

  .user-reflection-space {
    margin: 1rem;
    padding: 1.5rem;
  }

  .reflection-actions {
    flex-direction: column;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --fireside-text-primary: #f5f5f5;
    --fireside-text-secondary: #cccccc;
    --fireside-cream: #2d2d2d;
    --fireside-shadow: rgba(0, 0, 0, 0.3);
  }

  .story-fireside {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }

  .story-header,
  .story-content,
  .interaction-path,
  .expandable-card {
    background: rgba(45, 45, 45, 0.9);
    border: 1px solid rgba(255, 138, 101, 0.2);
  }

  .user-reflection-space {
    background: #2d2d2d;
    border: 1px solid rgba(255, 138, 101, 0.3);
  }

  .reflection-input {
    background: rgba(45, 45, 45, 0.8);
    color: var(--fireside-text-primary);
    border-color: rgba(255, 138, 101, 0.4);
  }
}