/* Story Fireside - 温暖故事化的样式 */

/* 全局变量 */
:root {
  --fireside-warm-orange: #ff8a65;
  --fireside-soft-yellow: #fff3e0;
  --fireside-deep-brown: #5d4037;
  --fireside-cream: #faf7f2;
  --fireside-shadow: rgba(93, 64, 55, 0.1);
  --fireside-text-primary: #3e2723;
  --fireside-text-secondary: #6d4c41;
  --fireside-accent: #ff7043;
  --fireside-gradient-warm: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
}

/* 主容器 */
.story-fireside {
  min-height: 100vh;
  background: var(--theme-gradient, var(--fireside-gradient-warm));
  position: relative;
  overflow-x: hidden;
}

.story-fireside::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 138, 101, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 204, 128, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.story-fireside-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

/* 加载状态 */
.story-fireside-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: var(--fireside-text-primary);
}

.loading-flame {
  font-size: 3rem;
  animation: flicker 2s ease-in-out infinite alternate;
}

@keyframes flicker {
  0% { transform: scale(1) rotate(-2deg); opacity: 0.8; }
  100% { transform: scale(1.1) rotate(2deg); opacity: 1; }
}

/* 故事头部 */
.story-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px var(--fireside-shadow);
}

.story-theme-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: inline-block;
  animation: gentle-float 3s ease-in-out infinite;
}

@keyframes gentle-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.story-title {
  font-size: 2.5rem;
  color: var(--fireside-text-primary);
  margin: 1rem 0;
  font-weight: 600;
  line-height: 1.2;
  font-family: 'Georgia', serif;
}

.story-meta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  color: var(--fireside-text-secondary);
  font-size: 0.9rem;
}

.story-author, .story-date {
  padding: 0.5rem 1rem;
  background: rgba(255, 138, 101, 0.1);
  border-radius: 20px;
}

/* 故事层级 */
.story-layer {
  margin-bottom: 2rem;
}

.back-button {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid var(--fireside-accent);
  color: var(--fireside-accent);
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  cursor: pointer;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: var(--fireside-accent);
  color: white;
  transform: translateX(-5px);
}

.story-content {
  background: rgba(255, 255, 255, 0.8);
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px var(--fireside-shadow);
  backdrop-filter: blur(10px);
  margin-bottom: 2rem;
}

.story-intro {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(255, 138, 101, 0.05);
  border-radius: 15px;
  border-left: 4px solid var(--fireside-accent);
}

.story-intro-text {
  color: var(--fireside-text-secondary);
  font-style: italic;
  margin: 0;
  line-height: 1.6;
}

.story-main-content {
  margin: 2rem 0;
}

.story-content-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--fireside-text-primary);
  margin: 0;
  font-family: 'Georgia', serif;
}

.story-conclusion {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(255, 204, 128, 0.1);
  border-radius: 15px;
  text-align: center;
}

.story-conclusion-text {
  color: var(--fireside-text-secondary);
  font-style: italic;
  margin: 0;
  font-size: 0.95rem;
}

/* 热点样式 */
.story-hotspot {
  background: linear-gradient(120deg, rgba(255, 138, 101, 0.2) 0%, rgba(255, 204, 128, 0.2) 100%);
  padding: 0.2rem 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid var(--fireside-accent);
  position: relative;
}

.story-hotspot:hover {
  background: linear-gradient(120deg, rgba(255, 138, 101, 0.3) 0%, rgba(255, 204, 128, 0.3) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 138, 101, 0.3);
}

.story-hotspot.active {
  background: linear-gradient(120deg, var(--fireside-accent) 0%, #ffab40 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(255, 138, 101, 0.4);
}

/* 热点展开内容 */
.hotspot-expansion {
  margin-top: 2rem;
  padding: 2rem;
  background: rgba(255, 138, 101, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 138, 101, 0.2);
}

.hotspot-title {
  color: var(--fireside-accent);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.hotspot-text {
  color: var(--fireside-text-primary);
  line-height: 1.7;
  font-style: italic;
  margin: 0;
}