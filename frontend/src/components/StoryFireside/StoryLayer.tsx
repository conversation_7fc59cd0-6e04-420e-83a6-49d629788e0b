import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { StoryTheme, StoryHotspot } from '../../types';

interface StoryLayerProps {
  content: string;
  layer: 'summary' | 'details';
  theme: StoryTheme;
  selectedPath?: string;
  onBackToSummary?: () => void;
}

export const StoryLayer: React.FC<StoryLayerProps> = ({
  content,
  layer,
  theme,
  selectedPath,
  onBackToSummary
}) => {
  const [activeHotspot, setActiveHotspot] = useState<string | null>(null);

  // 模拟热点数据 - 将来会从API获取
  const hotspots: StoryHotspot[] = [
    {
      id: 'lie',
      text: '一个简单的谎',
      startIndex: content.indexOf('[一个简单的谎]'),
      endIndex: content.indexOf('[一个简单的谎]') + '[一个简单的谎]'.length,
      expandedContent: '事实是，我一周前就决定不喝酒了，我为此感到自豪。但我不敢说出"嘿，我现在不喝酒"这样简单的话。这个小小的事实感觉像是一个巨大的秘密，所以我选择了更容易、更羞耻的借口："我身体不舒服"。',
      type: 'emotion'
    },
    {
      id: 'disgust',
      text: '我对自己如此厌恶',
      startIndex: content.indexOf('[我对自己如此厌恶]'),
      endIndex: content.indexOf('[我对自己如此厌恶]') + '[我对自己如此厌恶]'.length,
      expandedContent: '回到房间后，我看着镜子中的自己，眼泪开始流下。自我厌恶如此强烈，以至于自青少年时期以来第一次，我真的有了"我死了会更好"的想法。痛苦感觉无法克服。',
      type: 'emotion'
    },
    {
      id: 'girlfriend',
      text: '女朋友',
      startIndex: content.indexOf('[女朋友]'),
      endIndex: content.indexOf('[女朋友]') + '[女朋友]'.length,
      expandedContent: '当她的脸出现在屏幕上时，我无法掩饰我湿润的眼睛。我试图假装，微笑着说"我很好"，但我的声音在第一个字就破了。我知道我再也无法隐藏了。',
      type: 'turning_point'
    },
    {
      id: 'social_anxiety',
      text: '"社交焦虑"',
      startIndex: content.indexOf('["社交焦虑"]'),
      endIndex: content.indexOf('["社交焦虑"]') + '["社交焦虑"]'.length,
      expandedContent: '当她说出这个词时，我震惊了。这些话在我脑海中回响。我立即去YouTube找到了Ali博士的视频。就像第一次看到披头士乐队一样。我不仅仅是被安慰；我是被理解了。看完后我真的笑了。',
      type: 'insight'
    }
  ];

  const renderContentWithHotspots = (text: string) => {
    if (layer === 'summary') {
      return <p className="story-content-text">{text}</p>;
    }

    // 在详细层级显示可点击的热点
    let lastIndex = 0;
    const elements: React.ReactNode[] = [];

    hotspots.forEach((hotspot, index) => {
      // 添加热点前的文本
      if (hotspot.startIndex > lastIndex) {
        elements.push(
          <span key={`text-${index}`}>
            {text.slice(lastIndex, hotspot.startIndex)}
          </span>
        );
      }

      // 添加可点击的热点
      elements.push(
        <motion.span
          key={hotspot.id}
          className={`story-hotspot ${activeHotspot === hotspot.id ? 'active' : ''}`}
          onClick={() => setActiveHotspot(activeHotspot === hotspot.id ? null : hotspot.id)}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {hotspot.text}
        </motion.span>
      );

      lastIndex = hotspot.endIndex;
    });

    // 添加最后的文本
    if (lastIndex < text.length) {
      elements.push(
        <span key="text-end">
          {text.slice(lastIndex)}
        </span>
      );
    }

    return <p className="story-content-text">{elements}</p>;
  };

  return (
    <div className="story-layer">
      {layer === 'details' && onBackToSummary && (
        <motion.button
          className="back-button"
          onClick={onBackToSummary}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          whileHover={{ x: -5 }}
        >
          ← 返回故事概览
        </motion.button>
      )}

      <motion.div
        className="story-content"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {layer === 'summary' && (
          <div className="story-intro">
            <p className="story-intro-text">
              想象一下，你刚刚坐在我们的"故事炉边"。故事代理人为你呈现以下故事的开始...
            </p>
          </div>
        )}

        <div className="story-main-content">
          {renderContentWithHotspots(content)}
        </div>

        {layer === 'summary' && (
          <motion.div
            className="story-conclusion"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.6 }}
          >
            <p className="story-conclusion-text">
              （故事摘要结束。片刻沉默后，下方的交互中心轻柔地淡入。）
            </p>
          </div>
        )}
      </motion.div>

      {/* 热点展开内容 */}
      {layer === 'details' && activeHotspot && (
        <motion.div
          className="hotspot-expansion"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
        >
          {hotspots.map(hotspot => (
            activeHotspot === hotspot.id && (
              <div key={hotspot.id} className="hotspot-content">
                <h4 className="hotspot-title">点击 "{hotspot.text}" 展开：</h4>
                <p className="hotspot-text">"{hotspot.expandedContent}"</p>
              </div>
            )
          ))}
        </motion.div>
      )}
    </div>
  );
};