import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { StoryLayer } from './StoryLayer';
import { InteractionHub } from './InteractionHub';
import { UserReflectionSpace } from './UserReflectionSpace';
import { ExpandableCards } from './ExpandableCards';
import { StoryService } from '../../services/storyService';
import {
  StoryContent,
  StoryFiresideState,
  InteractionPath,
  StoryTheme,
  STORY_THEMES
} from '../../types';
import './StoryFireside.css';

interface StoryFiresideProps {
  storyId?: string;
  onStoryChange?: (storyId: string) => void;
}

export const StoryFireside: React.FC<StoryFiresideProps> = ({
  storyId,
  onStoryChange
}) => {
  const [state, setState] = useState<StoryFiresideState>({
    currentLayer: 'summary',
    selectedPath: undefined,
    activeHotspot: undefined,
    userInput: '',
    expandedCards: [],
    showReflectionSpace: false
  });

  const [currentStory, setCurrentStory] = useState<StoryContent | null>(null);
  const [theme, setTheme] = useState<StoryTheme>(STORY_THEMES.anxiety);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadStory = async () => {
      setLoading(true);
      try {
        let story: StoryContent | null = null;

        if (storyId) {
          // 获取指定的故事
          story = await StoryService.getStoryById(storyId);
        } else {
          // 获取推荐的故事
          const stories = await StoryService.getStories({ limit: 1 });
          story = stories[0] || null;
        }

        if (story) {
          setCurrentStory(story);
          setTheme(STORY_THEMES[story.category] || STORY_THEMES.anxiety);
        }
      } catch (error) {
        console.error('加载故事失败:', error);
      } finally {
        setLoading(false);
      }
    };

    loadStory();
  }, [storyId]);

  const interactionPaths: InteractionPath[] = [
    {
      id: 'dive_deeper',
      label: '我想深入了解这个故事',
      description: '探索故事中的关键时刻和情感转折点',
      type: 'dive_deeper',
      action: () => setState(prev => ({ ...prev, currentLayer: 'details', selectedPath: 'dive_deeper' }))
    },
    {
      id: 'personal_reflection',
      label: '这个故事让我想到了自己...',
      description: '分享你的共鸣和个人经历',
      type: 'personal_reflection',
      action: () => setState(prev => ({ ...prev, showReflectionSpace: true, selectedPath: 'personal_reflection' }))
    },
    {
      id: 'continue_story',
      label: '我想知道后来发生了什么',
      description: '了解故事的后续发展和成长历程',
      type: 'continue_story',
      action: () => setState(prev => ({ ...prev, currentLayer: 'details', selectedPath: 'continue_story' }))
    }
  ];

  const handleBackToSummary = () => {
    setState(prev => ({
      ...prev,
      currentLayer: 'summary',
      selectedPath: undefined,
      activeHotspot: undefined,
      showReflectionSpace: false
    }));
  };

  if (loading || !currentStory) {
    return (
      <div className="story-fireside-loading">
        <div className="loading-flame">🔥</div>
        <p>{loading ? '故事正在燃起...' : '没有找到故事'}</p>
      </div>
    );
  }

  return (
    <div
      className="story-fireside"
      style={{ '--theme-gradient': theme.gradient } as React.CSSProperties}
    >
      <div className="story-fireside-container">
        {/* 故事标题和主题 */}
        <motion.div
          className="story-header"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="story-theme-icon">{theme.icon}</div>
          <h1 className="story-title">{currentStory.title}</h1>
          <div className="story-meta">
            <span className="story-author">{currentStory.author}</span>
            <span className="story-date">{currentStory.createdAt}</span>
          </div>
        </motion.div>

        {/* 主要内容区域 */}
        <AnimatePresence mode="wait">
          {state.currentLayer === 'summary' && (
            <motion.div
              key="summary"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <StoryLayer
                content={currentStory.summary}
                layer="summary"
                theme={theme}
              />

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1, duration: 0.8 }}
              >
                <InteractionHub
                  paths={interactionPaths}
                  theme={theme}
                />
              </motion.div>
            </motion.div>
          )}

          {state.currentLayer === 'details' && (
            <motion.div
              key="details"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <StoryLayer
                content={currentStory.summary}
                layer="details"
                theme={theme}
                selectedPath={state.selectedPath}
                onBackToSummary={handleBackToSummary}
              />

              {state.selectedPath === 'continue_story' && (
                <ExpandableCards
                  storyId={currentStory.id}
                  theme={theme}
                />
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* 用户反思空间 */}
        <AnimatePresence>
          {state.showReflectionSpace && (
            <UserReflectionSpace
              theme={theme}
              onClose={() => setState(prev => ({ ...prev, showReflectionSpace: false }))}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
