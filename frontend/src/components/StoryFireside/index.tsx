import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { StoryLayer } from './StoryLayer';
import { InteractionHub } from './InteractionHub';
import { UserReflectionSpace } from './UserReflectionSpace';
import { ExpandableCards } from './ExpandableCards';
import {
  StoryContent,
  StoryFiresideState,
  InteractionPath,
  StoryTheme,
  STORY_THEMES
} from '../../types';
import './StoryFireside.css';

interface StoryFiresideProps {
  storyId?: string;
  onStoryChange?: (storyId: string) => void;
}

export const StoryFireside: React.FC<StoryFiresideProps> = ({
  storyId,
  onStoryChange
}) => {
  const [state, setState] = useState<StoryFiresideState>({
    currentLayer: 'summary',
    selectedPath: undefined,
    activeHotspot: undefined,
    userInput: '',
    expandedCards: [],
    showReflectionSpace: false
  });

  const [currentStory, setCurrentStory] = useState<StoryContent | null>(null);
  const [theme, setTheme] = useState<StoryTheme>(STORY_THEMES.anxiety);

  // Mock story data - 将来会从API获取
  const mockStory: StoryContent = {
    id: 'story-1',
    title: '社交焦虑的突破',
    summary: `在一次重要的公司聚餐中，我坐在熟悉的面孔中间，却像一座沉默的孤岛。我已经50岁了，却无法对年轻的同事说出一句话。脑海中有个声音不断攻击我，称我为"可悲"。为了逃避聚会后的活动，我撒了[一个简单的谎]，说自己身体不舒服。

独自在酒店房间里，[我对自己如此厌恶]，以至于产生了自青少年时期以来最黑暗的想法。

就在这时，[女朋友]的FaceTime通话打了进来。我无法掩饰自己的痛苦，在我坦白一切后，她温柔地说出了两个词：["社交焦虑"]。

就像一把钥匙，这些话解开了多年的困惑。我没有被治愈，但在那一刻，我终于感到自己并不孤单。`,
    author: '匿名用户',
    category: 'anxiety',
    emotionalTags: ['突破', '理解', '希望'],
    createdAt: '2024-01-15',
    engagement: {
      likes: 1247,
      comments: 89,
      shares: 156
    }
  };

  useEffect(() => {
    // 模拟从API获取故事数据
    setCurrentStory(mockStory);
    setTheme(STORY_THEMES[mockStory.category] || STORY_THEMES.anxiety);
  }, [storyId]);

  const interactionPaths: InteractionPath[] = [
    {
      id: 'dive_deeper',
      label: '我想深入了解这个故事',
      description: '探索故事中的关键时刻和情感转折点',
      type: 'dive_deeper',
      action: () => setState(prev => ({ ...prev, currentLayer: 'details', selectedPath: 'dive_deeper' }))
    },
    {
      id: 'personal_reflection',
      label: '这个故事让我想到了自己...',
      description: '分享你的共鸣和个人经历',
      type: 'personal_reflection',
      action: () => setState(prev => ({ ...prev, showReflectionSpace: true, selectedPath: 'personal_reflection' }))
    },
    {
      id: 'continue_story',
      label: '我想知道后来发生了什么',
      description: '了解故事的后续发展和成长历程',
      type: 'continue_story',
      action: () => setState(prev => ({ ...prev, currentLayer: 'details', selectedPath: 'continue_story' }))
    }
  ];

  const handleBackToSummary = () => {
    setState(prev => ({
      ...prev,
      currentLayer: 'summary',
      selectedPath: undefined,
      activeHotspot: undefined,
      showReflectionSpace: false
    }));
  };

  if (!currentStory) {
    return (
      <div className="story-fireside-loading">
        <div className="loading-flame">🔥</div>
        <p>故事正在燃起...</p>
      </div>
    );
  }

  return (
    <div
      className="story-fireside"
      style={{ '--theme-gradient': theme.gradient } as React.CSSProperties}
    >
      <div className="story-fireside-container">
        {/* 故事标题和主题 */}
        <motion.div
          className="story-header"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="story-theme-icon">{theme.icon}</div>
          <h1 className="story-title">{currentStory.title}</h1>
          <div className="story-meta">
            <span className="story-author">{currentStory.author}</span>
            <span className="story-date">{currentStory.createdAt}</span>
          </div>
        </motion.div>

        {/* 主要内容区域 */}
        <AnimatePresence mode="wait">
          {state.currentLayer === 'summary' && (
            <motion.div
              key="summary"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <StoryLayer
                content={currentStory.summary}
                layer="summary"
                theme={theme}
              />

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1, duration: 0.8 }}
              >
                <InteractionHub
                  paths={interactionPaths}
                  theme={theme}
                />
              </motion.div>
            </motion.div>
          )}

          {state.currentLayer === 'details' && (
            <motion.div
              key="details"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
            >
              <StoryLayer
                content={currentStory.summary}
                layer="details"
                theme={theme}
                selectedPath={state.selectedPath}
                onBackToSummary={handleBackToSummary}
              />

              {state.selectedPath === 'continue_story' && (
                <ExpandableCards
                  storyId={currentStory.id}
                  theme={theme}
                />
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* 用户反思空间 */}
        <AnimatePresence>
          {state.showReflectionSpace && (
            <UserReflectionSpace
              theme={theme}
              onClose={() => setState(prev => ({ ...prev, showReflectionSpace: false }))}
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
