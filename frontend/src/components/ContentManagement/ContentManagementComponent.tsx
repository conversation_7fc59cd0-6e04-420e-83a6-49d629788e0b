import React, { useState, useEffect } from 'react';
import { Card, Table, Tag, Button, Modal, Typography, Divider, message, Input, Select, Space, Tooltip, Badge, Alert, Dropdown, Menu } from 'antd';
import { EyeOutlined, DatabaseOutlined, LinkOutlined, DownloadOutlined, FilterOutlined, ReloadOutlined, ExclamationCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Text, Paragraph } = Typography;
const { Search } = Input;

interface ContentManagementComponentProps {
  onRefresh?: () => void;
}

const ContentManagementComponent: React.FC<ContentManagementComponentProps> = ({ onRefresh }) => {
  const [content, setContent] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedContent, setSelectedContent] = useState<any>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [platformFilter, setPlatformFilter] = useState<string>('');
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [contentStats, setContentStats] = useState({
    total: 0,
    validLinks: 0,
    invalidLinks: 0,
    platforms: {} as Record<string, number>
  });

  // 验证链接是否有效
  const validateLink = (url: string): boolean => {
    if (!url) return false;
    // 检查是否是真实的YouTube或Reddit链接
    const youtubePattern = /^https:\/\/(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[a-zA-Z0-9_-]+/;
    const redditPattern = /^https:\/\/(www\.)?reddit\.com\/r\/[a-zA-Z0-9_]+\/comments\/[a-zA-Z0-9_]+/;
    return youtubePattern.test(url) || redditPattern.test(url);
  };

  const fetchContent = async () => {
    setLoading(true);
    try {
      const response = await axios.get('http://localhost:8000/content', {
        params: {
          query: searchQuery,
          platform: platformFilter || undefined,
          limit: 50 // 减少一次性加载的数量
        }
      });

      const contentData = response.data.content || [];
      setContent(contentData);

      // 计算内容统计
      const stats = {
        total: contentData.length,
        validLinks: contentData.filter((item: any) => validateLink(item.url)).length,
        invalidLinks: contentData.filter((item: any) => !validateLink(item.url)).length,
        platforms: contentData.reduce((acc: Record<string, number>, item: any) => {
          acc[item.platform] = (acc[item.platform] || 0) + 1;
          return acc;
        }, {})
      };
      setContentStats(stats);

      console.log('Content loaded:', response.data);
      console.log('Content stats:', stats);
    } catch (error) {
      console.error('Failed to fetch content:', error);
      message.error('获取内容失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContent();
  }, []);

  const handleSearch = () => {
    fetchContent();
  };

  const handleRefresh = () => {
    fetchContent();
    if (onRefresh) {
      onRefresh();
    }
  };

  const showContentDetail = (record: any) => {
    setSelectedContent(record);
    setModalVisible(true);
  };

  const columns = [
    {
      title: '内容信息',
      key: 'content_info',
      width: 400,
      render: (_, record: any) => {
        const isValidLink = validateLink(record.url);
        return (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '4px' }}>
              <Tag color={record.platform === 'reddit' ? 'orange' : 'red'} style={{ marginRight: '8px' }}>
                {record.platform === 'reddit' ? 'Reddit' : 'YouTube'}
              </Tag>
              {isValidLink ? (
                <CheckCircleOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
              ) : (
                <ExclamationCircleOutlined style={{ color: '#ff4d4f', marginRight: '4px' }} />
              )}
              <Text strong style={{ fontSize: '14px' }}>
                {record.title || '无标题'}
              </Text>
            </div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              作者: {record.author || record.channel || 'Unknown'}
            </div>
            <div style={{ marginTop: '4px' }}>
              <Space size="small">
                <Button
                  type="link"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => showContentDetail(record)}
                  style={{ padding: '0 4px', height: 'auto' }}
                >
                  详情
                </Button>
                {isValidLink ? (
                  <Button
                    type="link"
                    size="small"
                    icon={<LinkOutlined />}
                    onClick={() => window.open(record.url, '_blank')}
                    style={{ padding: '0 4px', height: 'auto' }}
                  >
                    访问
                  </Button>
                ) : (
                  <Tooltip title="链接无效或为模拟数据">
                    <Button
                      type="link"
                      size="small"
                      icon={<LinkOutlined />}
                      disabled
                      style={{ padding: '0 4px', height: 'auto' }}
                    >
                      访问
                    </Button>
                  </Tooltip>
                )}
              </Space>
            </div>
          </div>
        );
      },
    },
    {
      title: '互动数据',
      key: 'engagement',
      width: 150,
      render: (_, record: any) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '2px' }}>
            <span style={{ marginRight: '8px' }}>👍</span>
            <Badge count={record.upvotes || record.likes || 0} style={{ backgroundColor: '#52c41a' }} />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '2px' }}>
            <span style={{ marginRight: '8px' }}>💬</span>
            <Badge count={record.comments || 0} style={{ backgroundColor: '#1890ff' }} />
          </div>
          {record.views && (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ marginRight: '8px' }}>👁️</span>
              <Badge count={record.views} style={{ backgroundColor: '#722ed1' }} />
            </div>
          )}
        </div>
      ),
    },
    {
      title: '采集时间',
      dataIndex: 'collected_at',
      key: 'collected_at',
      width: 150,
      render: (time: string) => {
        if (!time) return '-';
        const date = new Date(time);
        const now = new Date();
        const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

        return (
          <div>
            <div style={{ fontSize: '12px' }}>{date.toLocaleString('zh-CN')}</div>
            <div style={{ fontSize: '11px', color: '#999' }}>
              {diffHours < 1 ? '刚刚' : diffHours < 24 ? `${diffHours}小时前` : `${Math.floor(diffHours / 24)}天前`}
            </div>
          </div>
        );
      },
    },
  ];

  // 批量操作菜单
  const batchMenu = (
    <Menu>
      <Menu.Item key="export" icon={<DownloadOutlined />}>
        导出选中内容
      </Menu.Item>
      <Menu.Item key="analyze" icon={<EyeOutlined />}>
        批量分析
      </Menu.Item>
    </Menu>
  );

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <div style={{ margin: '16px' }}>
      {/* 数据质量警告 */}
      {contentStats.invalidLinks > 0 && (
        <Alert
          message="数据质量提醒"
          description={`检测到 ${contentStats.invalidLinks} 条内容的链接无效，可能为模拟数据。建议重新采集真实数据。`}
          type="warning"
          showIcon
          closable
          style={{ marginBottom: '16px' }}
        />
      )}

      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <span>
              <DatabaseOutlined style={{ marginRight: 8 }} />
              内容管理
            </span>
            <div style={{ display: 'flex', gap: '16px', fontSize: '14px', fontWeight: 'normal' }}>
              <span>总计: <strong>{contentStats.total}</strong></span>
              <span>有效链接: <strong style={{ color: '#52c41a' }}>{contentStats.validLinks}</strong></span>
              <span>无效链接: <strong style={{ color: '#ff4d4f' }}>{contentStats.invalidLinks}</strong></span>
              {Object.entries(contentStats.platforms).map(([platform, count]) => (
                <span key={platform}>
                  {platform === 'reddit' ? 'Reddit' : 'YouTube'}: <strong>{count}</strong>
                </span>
              ))}
            </div>
          </div>
        }
        extra={
          <Space>
            {selectedRowKeys.length > 0 && (
              <Dropdown overlay={batchMenu} placement="bottomRight">
                <Button>
                  批量操作 ({selectedRowKeys.length})
                </Button>
              </Dropdown>
            )}
            <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>
              刷新
            </Button>
          </Space>
        }
        style={{ marginBottom: '16px' }}
      >
        <div style={{ marginBottom: '16px', display: 'flex', gap: '16px' }}>
          <Search
            placeholder="搜索内容标题或内容"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onSearch={handleSearch}
            style={{ flex: 1 }}
            enterButton="搜索"
          />
          <Select
            placeholder="筛选平台"
            value={platformFilter}
            onChange={setPlatformFilter}
            style={{ width: 120 }}
            allowClear
          >
            <Select.Option value="reddit">Reddit</Select.Option>
            <Select.Option value="youtube">YouTube</Select.Option>
          </Select>
        </div>

        <Table
          columns={columns}
          dataSource={content}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            pageSize: 15,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条内容`,
            pageSizeOptions: ['10', '15', '20', '50']
          }}
          scroll={{ x: 800 }}
          locale={{
            emptyText: content.length === 0 ? (
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <DatabaseOutlined style={{ fontSize: '48px', color: '#ccc', marginBottom: '16px' }} />
                <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无内容数据</div>
                <div style={{ color: '#999' }}>请先创建并执行采集任务来获取内容</div>
              </div>
            ) : '暂无数据'
          }}
          size="middle"
        />
      </Card>

      <Modal
        title="内容详情"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedContent && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <Tag color={selectedContent.platform === 'reddit' ? 'orange' : 'red'}>
                {selectedContent.platform === 'reddit' ? 'Reddit' : 'YouTube'}
              </Tag>
              <Text strong>{selectedContent.title}</Text>
            </div>
            
            <Divider orientation="left">基本信息</Divider>
            <p><strong>作者/频道:</strong> {selectedContent.author || selectedContent.channel}</p>
            {selectedContent.subreddit && <p><strong>子版块:</strong> {selectedContent.subreddit}</p>}
            <p><strong>链接:</strong> <a href={selectedContent.url} target="_blank" rel="noopener noreferrer">{selectedContent.url}</a></p>
            <p><strong>采集时间:</strong> {new Date(selectedContent.collected_at).toLocaleString('zh-CN')}</p>
            
            <Divider orientation="left">互动数据</Divider>
            <div style={{ display: 'flex', gap: '20px' }}>
              <span>👍 点赞: {selectedContent.upvotes || selectedContent.likes || 0}</span>
              <span>💬 评论: {selectedContent.comments || 0}</span>
              {selectedContent.views && <span>👁️ 观看: {selectedContent.views}</span>}
            </div>
            
            <Divider orientation="left">内容</Divider>
            <Paragraph>
              {selectedContent.content}
            </Paragraph>
            
            {selectedContent.ai_analysis && (
              <>
                <Divider orientation="left">AI 分析结果</Divider>
                <div style={{ background: '#f5f5f5', padding: '16px', borderRadius: '6px' }}>
                  <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'inherit' }}>
                    {selectedContent.ai_analysis}
                  </pre>
                </div>
              </>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ContentManagementComponent;
