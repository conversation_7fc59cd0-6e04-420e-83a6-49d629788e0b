import React, { useState } from 'react';
import { Form, Input, Select, But<PERSON>, Card, message } from 'antd';
import { TaskConfig, Platform } from '../../types';
import axios from 'axios';

interface TaskConfigComponentProps {
  onSubmit: (config: TaskConfig) => void;
  loading?: boolean;
}

const TaskConfigComponent: React.FC<TaskConfigComponentProps> = ({ onSubmit, loading = false }) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      console.log('Task config submitted:', values);
      console.log('Form values:', JSON.stringify(values, null, 2));

      // 验证必填字段
      if (!values.name || !values.platforms || !values.keywords || !values.max_results) {
        message.error('请填写所有必填字段');
        return;
      }

      const taskData = {
        name: values.name,
        platforms: values.platforms,
        keywords: values.keywords,
        max_results: values.max_results || 10
      };

      console.log('Sending task data:', JSON.stringify(taskData, null, 2));

      // Call backend API
      const response = await axios.post('http://localhost:8000/tasks', taskData);

      message.success(`任务 "${values.name}" 创建成功！任务ID: ${response.data.id}`);
      console.log('Task created:', response.data);

      // Reset form
      form.resetFields();

      // Call parent callback
      onSubmit(values as TaskConfig);

      // 提示用户可以在数据概览页面查看任务状态
      setTimeout(() => {
        message.info('您可以在"数据概览"页面查看任务执行状态');
      }, 2000);

    } catch (error: any) {
      console.error('Failed to create task:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        config: error.config
      });

      let errorMessage = '创建任务失败';

      if (error.response?.data?.detail) {
        errorMessage = `创建任务失败: ${error.response.data.detail}`;
      } else if (error.response?.status) {
        errorMessage = `创建任务失败: HTTP ${error.response.status} ${error.response.statusText}`;
      } else if (error.message) {
        errorMessage = `创建任务失败: ${error.message}`;
      } else {
        errorMessage = '创建任务失败，请检查网络连接和后端服务';
      }

      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card title="任务配置" style={{ margin: '16px' }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          max_results: 10,
          platforms: [Platform.REDDIT, Platform.YOUTUBE],
          keywords: ['mental health', 'therapy', 'wellness']
        }}
      >
        <Form.Item
          name="name"
          label="任务名称"
          rules={[{ required: true, message: '请输入任务名称' }]}
        >
          <Input placeholder="请输入任务名称" />
        </Form.Item>

        <Form.Item
          name="platforms"
          label="选择平台"
          rules={[{ required: true, message: '请选择至少一个平台' }]}
        >
          <Select
            mode="multiple"
            placeholder="选择要采集的平台"
            options={[
              // Phase 1 - Priority Platforms (已配置API)
              { label: '🔴 Reddit', value: Platform.REDDIT },
              { label: '📺 YouTube', value: Platform.YOUTUBE },
              { label: '🎵 TikTok', value: Platform.TIKTOK },
              { label: '🎧 Spotify Podcasts', value: Platform.SPOTIFY },
              { label: '🍎 Apple Podcasts', value: Platform.APPLE_PODCASTS },
              { label: '🎙️ Google Podcasts', value: Platform.GOOGLE_PODCASTS },

              // Phase 2 - Extended Platforms (待配置)
              { label: '📝 Medium', value: Platform.MEDIUM, disabled: true },
              { label: '📷 Instagram', value: Platform.INSTAGRAM, disabled: true },
              { label: '🐦 X (Twitter)', value: Platform.TWITTER, disabled: true },
              { label: '🤖 Character.AI', value: Platform.CHARACTER_AI, disabled: true },

              // Legacy platforms
              { label: '📱 小红书', value: Platform.XIAOHONGSHU, disabled: true },
              { label: '🎬 抖音', value: Platform.DOUYIN, disabled: true },
              { label: '💬 微信公众号', value: Platform.WECHAT_ARTICLE, disabled: true },
              { label: '📹 微信视频号', value: Platform.WECHAT_VIDEO, disabled: true }
            ]}
          />
        </Form.Item>

        <Form.Item
          name="keywords"
          label="关键词"
          rules={[{ required: true, message: '请输入关键词' }]}
        >
          <Select
            mode="tags"
            placeholder="输入关键词，按回车添加（如：depression, anxiety, mental health）"
            style={{ width: '100%' }}
            options={[
              // 心理健康核心关键词
              { label: 'depression', value: 'depression' },
              { label: 'anxiety', value: 'anxiety' },
              { label: 'mental health', value: 'mental health' },
              { label: 'therapy', value: 'therapy' },
              { label: 'recovery', value: 'recovery' },
              { label: 'healing', value: 'healing' },
              { label: 'self care', value: 'self care' },
              { label: 'mindfulness', value: 'mindfulness' },
              { label: 'meditation', value: 'meditation' },
              { label: 'bipolar', value: 'bipolar' },
              { label: 'ptsd', value: 'ptsd' },
              { label: 'trauma', value: 'trauma' },
              { label: 'suicide prevention', value: 'suicide prevention' },
              { label: 'self improvement', value: 'self improvement' },
              { label: 'motivation', value: 'motivation' },
              { label: 'personal growth', value: 'personal growth' }
            ]}
          />
        </Form.Item>

        <Form.Item
          name="max_results"
          label="采集数量"
          rules={[{ required: true, message: '请输入采集数量' }]}
        >
          <Select placeholder="选择采集数量" defaultValue={10}>
            <Select.Option value={5}>5条内容</Select.Option>
            <Select.Option value={10}>10条内容</Select.Option>
            <Select.Option value={20}>20条内容</Select.Option>
            <Select.Option value={50}>50条内容</Select.Option>
            <Select.Option value={100}>100条内容</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading || submitting}
            disabled={submitting}
          >
            {submitting ? '创建中...' : '创建任务'}
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default TaskConfigComponent;