import React, { useState, useEffect } from 'react';
import { Card, List, Tag, Button, Statistic, Row, Col, Typography, message, Select, Collapse, Progress, Tooltip } from 'antd';
import { FireOutlined, RiseOutlined, ReloadOutlined, InfoCircleOutlined, TrophyOutlined, ThunderboltOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Text, Title } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

interface HotInsightsComponentProps {
  onRefresh?: () => void;
}

const HotInsightsComponent: React.FC<HotInsightsComponentProps> = ({ onRefresh }) => {
  const [hotContent, setHotContent] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [platformFilter, setPlatformFilter] = useState<string | undefined>(undefined);
  const [timeWindow, setTimeWindow] = useState<number>(24);
  const [stats, setStats] = useState({
    totalHotContent: 0,
    avgEngagement: 0,
    topPlatform: '',
    lastUpdate: '',
    hotTotal: 0,
    avgHotScore: 0
  });

  const fetchHotContent = async () => {
    setLoading(true);
    try {
      // 获取基于智能算法的热点内容
      const response = await axios.get('http://localhost:8000/content/hot', {
        params: {
          limit: 20,
          hours: timeWindow,
          ...(platformFilter && { platform: platformFilter })
        }
      });

      const content = response.data.content || [];
      const criteria = response.data.criteria || {};
      setHotContent(content);

      // 计算统计数据
      if (content.length > 0) {
        const totalEngagement = content.reduce((sum: number, item: any) =>
          sum + (item.upvotes || item.likes || 0) + (item.comments || 0), 0
        );
        const avgEngagement = Math.round(totalEngagement / content.length);

        const totalHotScore = content.reduce((sum: number, item: any) =>
          sum + (item.hot_score || 0), 0
        );
        const avgHotScore = Math.round(totalHotScore / content.length);

        const platformCounts = content.reduce((acc: any, item: any) => {
          acc[item.platform] = (acc[item.platform] || 0) + 1;
          return acc;
        }, {});

        const topPlatform = Object.keys(platformCounts).reduce((a, b) =>
          platformCounts[a] > platformCounts[b] ? a : b, ''
        );

        setStats({
          totalHotContent: response.data.total || 0,
          hotTotal: response.data.hot_total || content.length,
          avgEngagement,
          avgHotScore,
          topPlatform: topPlatform === 'reddit' ? 'Reddit' : 'YouTube',
          lastUpdate: new Date().toLocaleString('zh-CN')
        });
      } else {
        setStats({
          totalHotContent: response.data.total || 0,
          hotTotal: 0,
          avgEngagement: 0,
          avgHotScore: 0,
          topPlatform: '',
          lastUpdate: new Date().toLocaleString('zh-CN')
        });
      }

    } catch (error) {
      console.error('Failed to fetch hot content:', error);
      message.error('获取热点内容失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHotContent();
  }, [platformFilter, timeWindow]);

  useEffect(() => {
    // 设置定时刷新（每5分钟）
    const interval = setInterval(fetchHotContent, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const handleRefresh = () => {
    fetchHotContent();
    if (onRefresh) {
      onRefresh();
    }
  };

  const generateHotContent = async () => {
    setLoading(true);
    try {
      // 创建一个自动热点采集任务
      const response = await axios.post('http://localhost:8000/tasks', {
        name: `热点内容自动采集_${new Date().toLocaleString('zh-CN')}`,
        platforms: ['reddit', 'youtube'],
        keywords: ['mental health', 'depression', 'anxiety', 'wellness', 'therapy'],
        max_results: 20
      });
      
      if (response.data.id) {
        // 立即执行任务
        await axios.post(`http://localhost:8000/tasks/${response.data.id}/execute`);
        message.success('热点内容采集任务已启动');
        
        // 等待一段时间后刷新
        setTimeout(() => {
          fetchHotContent();
        }, 3000);
      }
    } catch (error) {
      console.error('Failed to generate hot content:', error);
      message.error('启动热点采集失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ margin: '16px' }}>
      <Card 
        title={
          <span>
            <FireOutlined style={{ marginRight: 8 }} />
            热点洞察
          </span>
        }
        extra={
          <div>
            <Button 
              onClick={generateHotContent} 
              loading={loading}
              style={{ marginRight: 8 }}
            >
              生成热点内容
            </Button>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleRefresh} 
              loading={loading}
            >
              刷新
            </Button>
          </div>
        }
        style={{ marginBottom: '16px' }}
      >
        {/* 算法说明 */}
        <Collapse ghost style={{ marginBottom: '16px' }}>
          <Panel
            header={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <InfoCircleOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                <Text strong>智能热点算法说明</Text>
              </div>
            }
            key="algorithm"
          >
            <div style={{ background: '#f6ffed', padding: '16px', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
              <Title level={5}>🎯 热点判断规则</Title>
              <ul style={{ marginBottom: '16px' }}>
                <li><strong>互动权重</strong>：点赞数 × 1.0 + 评论数 × 2.0 + 浏览数 × 0.001</li>
                <li><strong>参与率</strong>：(点赞 + 评论) / 浏览数</li>
                <li><strong>时效性</strong>：{timeWindow}小时内的内容获得加权，越新越高</li>
                <li><strong>综合评分</strong>：互动权重 × (1 + 参与率) × (1 + 时效性因子)</li>
              </ul>
              <Text type="secondary">
                💡 评论比点赞权重更高，因为评论代表更深度的参与。时效性确保展示最新的热点话题。
              </Text>
            </div>
          </Panel>
        </Collapse>

        {/* 筛选控件 */}
        <Row gutter={16} style={{ marginBottom: '24px', padding: '16px', background: '#fafafa', borderRadius: '6px' }}>
          <Col span={6}>
            <Text strong>平台筛选：</Text>
            <Select
              style={{ width: '100%', marginTop: '8px' }}
              placeholder="选择平台"
              allowClear
              value={platformFilter}
              onChange={setPlatformFilter}
            >
              <Option value="reddit">Reddit</Option>
              <Option value="youtube">YouTube</Option>
            </Select>
          </Col>
          <Col span={6}>
            <Text strong>时间窗口：</Text>
            <Select
              style={{ width: '100%', marginTop: '8px' }}
              value={timeWindow}
              onChange={setTimeWindow}
            >
              <Option value={6}>6小时</Option>
              <Option value={12}>12小时</Option>
              <Option value={24}>24小时</Option>
              <Option value={48}>48小时</Option>
              <Option value={168}>7天</Option>
            </Select>
          </Col>
          <Col span={12}>
            <Text strong>当前筛选：</Text>
            <div style={{ marginTop: '8px' }}>
              <Tag color="blue">{platformFilter || '全部平台'}</Tag>
              <Tag color="green">{timeWindow}小时内</Tag>
            </div>
          </Col>
        </Row>

        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Statistic
              title="总内容数"
              value={stats.totalHotContent}
              prefix={<FireOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="热点内容数"
              value={stats.hotTotal}
              prefix={<TrophyOutlined style={{ color: '#ff4d4f' }} />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="平均热度分"
              value={stats.avgHotScore}
              prefix={<RiseOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="热门平台"
              value={stats.topPlatform || '暂无'}
              prefix={<ThunderboltOutlined />}
            />
          </Col>
        </Row>

        {hotContent.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <FireOutlined style={{ fontSize: '48px', color: '#ccc', marginBottom: '16px' }} />
            <Title level={4} type="secondary">暂无热点内容</Title>
            <Text type="secondary">点击"生成热点内容"开始自动采集热门内容</Text>
          </div>
        ) : (
          <List
            itemLayout="vertical"
            dataSource={hotContent}
            pagination={{
              pageSize: 5,
              showSizeChanger: false,
              showQuickJumper: false,
            }}
            renderItem={(item: any) => (
              <List.Item
                key={item.id}
                actions={[
                  <span key="likes">👍 {item.upvotes || item.likes || 0}</span>,
                  <span key="comments">💬 {item.comments || 0}</span>,
                  ...(item.views ? [<span key="views">👁️ {item.views}</span>] : []),
                  <Tooltip title="参与率：(点赞+评论)/浏览数" key="engagement">
                    <span>📈 {((item.engagement_rate || 0) * 100).toFixed(2)}%</span>
                  </Tooltip>,
                  <Tooltip title="时效性因子：基于发布时间的权重" key="recency">
                    <span>⏰ {((item.recency_factor || 0) * 100).toFixed(1)}%</span>
                  </Tooltip>
                ]}
                extra={
                  <div style={{ textAlign: 'right' }}>
                    <div style={{ marginBottom: '8px' }}>
                      <Tag color={item.platform === 'reddit' ? 'orange' : 'red'}>
                        {item.platform === 'reddit' ? 'Reddit' : 'YouTube'}
                      </Tag>
                    </div>
                    <div>
                      <Tooltip title="综合热度分数">
                        <Tag color="red" style={{ fontSize: '12px', fontWeight: 'bold' }}>
                          🔥 {Math.round(item.hot_score || 0)}
                        </Tag>
                      </Tooltip>
                    </div>
                  </div>
                }
              >
                <List.Item.Meta
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <a href={item.url} target="_blank" rel="noopener noreferrer" style={{ flex: 1 }}>
                        {item.title}
                      </a>
                      {item.hot_score && (
                        <Progress
                          type="circle"
                          size={40}
                          percent={Math.min(100, (item.hot_score / 1000) * 100)}
                          format={() => Math.round(item.hot_score)}
                          strokeColor={{
                            '0%': '#108ee9',
                            '100%': '#87d068',
                          }}
                          style={{ marginLeft: '16px' }}
                        />
                      )}
                    </div>
                  }
                  description={
                    <div>
                      <Text type="secondary">
                        {item.author || item.channel} • {new Date(item.collected_at).toLocaleString('zh-CN')}
                      </Text>
                    </div>
                  }
                />
                <div style={{ marginTop: '8px' }}>
                  <Text>
                    {item.content && item.content.length > 100
                      ? `${item.content.substring(0, 100)}...`
                      : item.content}
                  </Text>
                </div>
              </List.Item>
            )}
          />
        )}
      </Card>
    </div>
  );
};

export default HotInsightsComponent;
