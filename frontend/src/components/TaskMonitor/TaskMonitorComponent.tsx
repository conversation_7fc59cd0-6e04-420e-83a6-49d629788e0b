import React, { useState, useEffect } from 'react';
import { Card, Progress, Table, Tag, Button, message } from 'antd';
import { Task } from '../../types';
import axios from 'axios';

interface TaskMonitorComponentProps {
  onRefresh?: () => void;
}

const TaskMonitorComponent: React.FC<TaskMonitorComponentProps> = ({ onRefresh }) => {
  const [tasks, setTasks] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchTasks = async () => {
    setLoading(true);
    try {
      const response = await axios.get('http://localhost:8000/tasks');
      setTasks(response.data.tasks || []);
      console.log('Tasks loaded:', response.data);
    } catch (error) {
      console.error('Failed to fetch tasks:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, []);

  const handleRefresh = () => {
    fetchTasks();
    if (onRefresh) {
      onRefresh();
    }
  };

  const executeTask = async (taskId: string) => {
    try {
      message.info('开始执行任务...');
      const response = await axios.post(`http://localhost:8000/tasks/${taskId}/execute`);
      message.success('任务执行完成！');
      console.log('Task execution result:', response.data);

      // Refresh task list to show updated status
      fetchTasks();
    } catch (error: any) {
      console.error('Failed to execute task:', error);
      if (error.response?.data?.detail) {
        message.error(`任务执行失败: ${error.response.data.detail}`);
      } else {
        message.error('任务执行失败');
      }
    }
  };
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'processing';
      case 'completed': return 'success';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'created': return '已创建';
      case 'pending': return '等待中';
      case 'running': return '运行中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      default: return status;
    }
  };

  const columns = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '已采集',
      dataIndex: 'collected_count',
      key: 'collected_count',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => {
        if (!time) return '-';
        const date = new Date(time);
        return date.toLocaleString('zh-CN');
      },
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: any) => (
        <Button
          type="primary"
          size="small"
          disabled={record.status === 'running' || record.status === 'completed'}
          onClick={() => executeTask(record.id)}
        >
          {record.status === 'completed' ? '已完成' :
           record.status === 'running' ? '执行中' : '执行任务'}
        </Button>
      ),
    },
  ];

  return (
    <Card
      title="任务监控"
      extra={<Button onClick={handleRefresh} loading={loading}>刷新</Button>}
      style={{ margin: '16px' }}
    >
      <Table
        columns={columns}
        dataSource={tasks}
        rowKey="id"
        pagination={false}
        loading={loading}
        locale={{ emptyText: tasks.length === 0 ? '暂无任务，请先创建任务' : '暂无数据' }}
      />
    </Card>
  );
};

export default TaskMonitorComponent;