// Re-export all types from backend for consistency
export enum Platform {
  // Phase 1 - Priority Platforms
  REDDIT = 'reddit',
  YOUTUBE = 'youtube',
  TIKTOK = 'tiktok',
  SPOTIFY = 'spotify',
  APPLE_PODCASTS = 'apple_podcasts',
  GOOGLE_PODCASTS = 'google_podcasts',

  // Phase 2 - Extended Platforms
  MEDIUM = 'medium',
  INSTAGRAM = 'instagram',
  TWITTER = 'twitter',
  CHARACTER_AI = 'character_ai',

  // Legacy platforms (keeping for compatibility)
  XIAOHONGSHU = 'xiaohongshu',
  DOUYIN = 'douyin',
  WECHAT_ARTICLE = 'wechat_article',
  WECHAT_VIDEO = 'wechat_video'
}

export enum ContentType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  MIXED = 'mixed'
}

export enum ContentCategory {
  PSYCHOLOGY = 'psychology',
  GROWTH = 'growth',
  RELATIONSHIP = 'relationship',
  OTHER = 'other'
}

export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Author information interface
export interface AuthorInfo {
  id: string;
  name: string;
  followers: number;
  isVerified: boolean;
  avatarUrl?: string;
  description?: string;
  platform: Platform;
}

// Content information interface
export interface ContentInfo {
  id: string;
  title?: string;
  description?: string;
  contentText?: string;
  mediaUrls: string[];
  tags: string[];
  publishedAt: Date;
  contentType: ContentType;
  category?: ContentCategory;
}

// Metrics information interface
export interface MetricsInfo {
  likesCount: number;
  commentsCount: number;
  sharesCount: number;
  viewsCount: number;
  favoritesCount: number;
  engagementRate?: number;
}

// Complete content item interface
export interface ContentItem {
  id: string;
  taskId: string;
  platform: Platform;
  author: AuthorInfo;
  content: ContentInfo;
  metrics: MetricsInfo;
  originalUrl: string;
  collectedAt: Date;
}

// Task configuration interfaces
export interface FilterConfig {
  minFollowers?: number;
  minLikes?: number;
  minComments?: number;
  minViews?: number;
  contentTypes?: ContentType[];
  categories?: ContentCategory[];
  timeRange?: {
    startDate: Date;
    endDate: Date;
  };
}

export interface TaskConfig {
  name: string;
  platforms: Platform[];
  keywords: string[];
  filters: FilterConfig;
  maxResults?: number;
  schedule?: {
    enabled: boolean;
    cron?: string;
    interval?: number;
  };
}

// Task interface
export interface Task {
  id: string;
  userId: string;
  name: string;
  config: TaskConfig;
  status: TaskStatus;
  progress: number;
  collectedCount: number;
  estimatedTime?: number;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

// API response interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Search and filter interfaces
export interface SearchParams {
  keywords?: string;
  platforms?: Platform[];
  categories?: ContentCategory[];
  contentTypes?: ContentType[];
  sortBy?: 'publishedAt' | 'likesCount' | 'commentsCount' | 'viewsCount' | 'engagementRate';
  sortOrder?: 'asc' | 'desc';
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  minMetrics?: {
    likes?: number;
    comments?: number;
    views?: number;
    followers?: number;
  };
  page?: number;
  limit?: number;
}

// Analysis and reporting interfaces
export interface AnalysisReport {
  taskId: string;
  totalContent: number;
  platformDistribution: Record<Platform, number>;
  categoryDistribution: Record<ContentCategory, number>;
  contentTypeDistribution: Record<ContentType, number>;
  topAuthors: <AUTHORS>
    author: AuthorInfo;
    contentCount: number;
    totalEngagement: number;
  }>;
  engagementTrends: Array<{
    date: Date;
    avgLikes: number;
    avgComments: number;
    avgShares: number;
    avgViews: number;
  }>;
  keywordAnalysis: Array<{
    keyword: string;
    frequency: number;
    avgEngagement: number;
  }>;
  generatedAt: Date;
}

// Export configuration
export interface ExportConfig {
  format: 'csv' | 'json' | 'excel';
  fields: string[];
  filters?: SearchParams;
  includeMetrics: boolean;
  includeAuthorInfo: boolean;
  anonymize?: boolean;
}

export interface ExportJob {
  id: string;
  userId: string;
  config: ExportConfig;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  downloadUrl?: string;
  createdAt: Date;
  completedAt?: Date;
  error?: string;
}

// Frontend-specific interfaces
export interface ComponentProps {
  className?: string;
  style?: React.CSSProperties;
}

// Form interfaces for Ant Design
export interface TaskFormData {
  name: string;
  platforms: Platform[];
  keywords: string;
  maxResults: number;
  minFollowers?: number;
  minLikes?: number;
  minComments?: number;
  minViews?: number;
  contentTypes?: ContentType[];
  categories?: ContentCategory[];
  timeRange?: [Date, Date];
}

export interface SearchFormData {
  keywords?: string;
  platforms?: Platform[];
  categories?: ContentCategory[];
  contentTypes?: ContentType[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  dateRange?: [Date, Date];
  minLikes?: number;
  minComments?: number;
  minViews?: number;
  minFollowers?: number;
}

// UI state interfaces
export interface LoadingState {
  loading: boolean;
  error?: string;
}

export interface TableColumn {
  key: string;
  title: string;
  dataIndex?: string;
  width?: number;
  sorter?: boolean;
  render?: (value: any, record: any) => React.ReactNode;
}

// Chart data interfaces for ECharts
export interface ChartData {
  name: string;
  value: number;
}

export interface TrendData {
  date: string;
  value: number;
  category?: string;
}

// Platform display configurations
export const PLATFORM_LABELS: Record<Platform, string> = {
  // Phase 1 - Priority Platforms
  [Platform.REDDIT]: 'Reddit',
  [Platform.YOUTUBE]: 'YouTube',
  [Platform.TIKTOK]: 'TikTok',
  [Platform.SPOTIFY]: 'Spotify Podcasts',
  [Platform.APPLE_PODCASTS]: 'Apple Podcasts',
  [Platform.GOOGLE_PODCASTS]: 'Google Podcasts',

  // Phase 2 - Extended Platforms
  [Platform.MEDIUM]: 'Medium',
  [Platform.INSTAGRAM]: 'Instagram',
  [Platform.TWITTER]: 'X (Twitter)',
  [Platform.CHARACTER_AI]: 'Character.AI',

  // Legacy platforms
  [Platform.XIAOHONGSHU]: '小红书',
  [Platform.DOUYIN]: '抖音',
  [Platform.WECHAT_ARTICLE]: '微信公众号',
  [Platform.WECHAT_VIDEO]: '微信视频号'
};

export const CONTENT_TYPE_LABELS: Record<ContentType, string> = {
  [ContentType.TEXT]: '文本',
  [ContentType.IMAGE]: '图片',
  [ContentType.VIDEO]: '视频',
  [ContentType.MIXED]: '混合'
};

export const CONTENT_CATEGORY_LABELS: Record<ContentCategory, string> = {
  [ContentCategory.PSYCHOLOGY]: '心理',
  [ContentCategory.GROWTH]: '成长',
  [ContentCategory.RELATIONSHIP]: '关系',
  [ContentCategory.OTHER]: '其他'
};

export const TASK_STATUS_LABELS: Record<TaskStatus, string> = {
  [TaskStatus.PENDING]: '待执行',
  [TaskStatus.RUNNING]: '执行中',
  [TaskStatus.COMPLETED]: '已完成',
  [TaskStatus.FAILED]: '失败'
};

// Story Fireside 相关类型定义
export interface StoryContent {
  id: string;
  title: string;
  summary: string;
  author?: string;
  category: string;
  emotionalTags: string[];
  createdAt: string;
  engagement: {
    likes: number;
    comments: number;
    shares: number;
  };
}

export interface StoryHotspot {
  id: string;
  text: string;
  startIndex: number;
  endIndex: number;
  expandedContent: string;
  type: 'emotion' | 'action' | 'insight' | 'turning_point';
}

export interface StoryLayer {
  id: string;
  content: string;
  hotspots: StoryHotspot[];
  interactionPaths?: InteractionPath[];
}

export interface InteractionPath {
  id: string;
  label: string;
  description: string;
  type: 'dive_deeper' | 'personal_reflection' | 'continue_story' | 'explore_theme';
  targetLayer?: string;
  action: () => void;
}

export interface UserReflection {
  id: string;
  content: string;
  timestamp: string;
  isAnonymous: boolean;
  emotionalResonance?: string[];
}

export interface ExpandableCard {
  id: string;
  title: string;
  content: string;
  type: 'next_step' | 'method' | 'author_message' | 'community_echo';
  isExpanded: boolean;
}

export interface StoryFiresideState {
  currentLayer: 'summary' | 'interaction' | 'details';
  selectedPath?: string;
  activeHotspot?: string;
  userInput: string;
  expandedCards: string[];
  showReflectionSpace: boolean;
}

export interface StoryTheme {
  id: string;
  name: string;
  color: string;
  gradient: string;
  icon: string;
}

// 预定义的故事主题
export const STORY_THEMES: Record<string, StoryTheme> = {
  anxiety: {
    id: 'anxiety',
    name: '社交焦虑',
    color: '#6366f1',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    icon: '🌊'
  },
  depression: {
    id: 'depression',
    name: '抑郁康复',
    color: '#8b5cf6',
    gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    icon: '🌅'
  },
  growth: {
    id: 'growth',
    name: '个人成长',
    color: '#10b981',
    gradient: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)',
    icon: '🌱'
  },
  healing: {
    id: 'healing',
    name: '情感治愈',
    color: '#f59e0b',
    gradient: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    icon: '🕯️'
  }
};