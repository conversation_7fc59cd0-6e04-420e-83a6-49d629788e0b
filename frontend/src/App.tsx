import React, { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import { Layout, Menu, Typography } from 'antd'
import {
  DashboardOutlined,
  FireOutlined,
  SearchOutlined,
  SettingOutlined,
  BarChartOutlined,
  DatabaseOutlined
} from '@ant-design/icons'
import { TaskConfigComponent } from './components/TaskConfig'
import { TaskMonitorComponent } from './components/TaskMonitor'
import { SearchComponent } from './components/Search'
import { ContentManagementComponent } from './components/ContentManagement'
import { HotInsightsComponent } from './components/HotInsights'
import { StoryFireside } from './components/StoryFireside'
import { Task, ContentItem, TaskConfig } from './types'

const { Header, Sider, Content } = Layout
const { Title } = Typography

const App: React.FC = () => {
  // Mock data and handlers for development
  const [tasks] = useState<Task[]>([]);
  const [hotContent] = useState<ContentItem[]>([]);
  const [contentData] = useState<ContentItem[]>([]);

  const handleRefresh = () => {
    console.log('Refreshing data...');
  };

  const handleSearch = (query: string, filters: any) => {
    console.log('Searching:', query, filters);
  };

  const handleTaskSubmit = (config: TaskConfig) => {
    console.log('Creating task:', config);
  };

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '数据概览',
    },
    {
      key: '/story-fireside',
      icon: <FireOutlined />,
      label: '故事炉边',
    },
    {
      key: '/tasks',
      icon: <SettingOutlined />,
      label: '内容采集',
    },
    {
      key: '/content',
      icon: <DatabaseOutlined />,
      label: '内容管理',
    },
    {
      key: '/hot-insights',
      icon: <FireOutlined />,
      label: '热点洞察',
    },
    {
      key: '/analytics',
      icon: <BarChartOutlined />,
      label: 'AI分析',
    },
    {
      key: '/search',
      icon: <SearchOutlined />,
      label: '智能搜索',
    },
  ]

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider width={200} theme="dark">
        <div style={{ padding: '16px', textAlign: 'center' }}>
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            心理健康分析
          </Title>
        </div>
        <Menu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={['/']}
          items={menuItems}
          onClick={({ key }) => {
            window.location.pathname = key
          }}
        />
      </Sider>
      <Layout>
        <Header style={{ background: '#fff', padding: '0 24px' }}>
          <Title level={3} style={{ margin: 0, lineHeight: '64px' }}>
            心理健康内容分析系统
          </Title>
        </Header>
        <Content style={{ margin: '24px', background: '#fff', padding: '24px' }}>
          <Routes>
            <Route path="/" element={<TaskMonitorComponent onRefresh={handleRefresh} />} />
            <Route path="/story-fireside" element={
              <div style={{ margin: '-24px', background: 'transparent' }}>
                <StoryFireside />
              </div>
            } />
            <Route path="/tasks" element={<TaskConfigComponent onSubmit={handleTaskSubmit} />} />
            <Route path="/content" element={<ContentManagementComponent onRefresh={handleRefresh} />} />
            <Route path="/hot-insights" element={<HotInsightsComponent onRefresh={handleRefresh} />} />
            <Route path="/analytics" element={<div>AI分析功能开发中...</div>} />
            <Route path="/search" element={<SearchComponent onSearch={handleSearch} />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  )
}

export default App