import { StoryContent, StoryHotspot, ExpandableCard } from '../types';
import api from './api';

// 故事服务 - 连接现有系统API获取故事数据
export class StoryService {

  // 获取故事列表
  static async getStories(filters?: {
    category?: string;
    platform?: string;
    limit?: number;
  }): Promise<StoryContent[]> {
    try {
      // 这里会调用现有的内容API
      const response = await api.get('/content', {
        params: {
          ...filters,
          contentTypes: ['text'],
          categories: ['psychology', 'growth'],
          sortBy: 'engagementRate',
          sortOrder: 'desc'
        }
      });

      // 将现有的ContentItem转换为StoryContent格式
      return response.data.map(this.transformContentToStory);
    } catch (error) {
      console.error('获取故事数据失败:', error);
      // 返回模拟数据作为后备
      return this.getMockStories();
    }
  }

  // 获取单个故事详情
  static async getStoryById(id: string): Promise<StoryContent | null> {
    try {
      const response = await api.get(`/content/${id}`);
      return this.transformContentToStory(response.data);
    } catch (error) {
      console.error('获取故事详情失败:', error);
      return this.getMockStories().find(story => story.id === id) || null;
    }
  }

  // 获取故事的热点数据
  static async getStoryHotspots(storyId: string): Promise<StoryHotspot[]> {
    try {
      // 这里可以调用AI分析API来获取热点
      const response = await api.get(`/analysis/story-hotspots/${storyId}`);
      return response.data;
    } catch (error) {
      console.error('获取故事热点失败:', error);
      return this.getMockHotspots();
    }
  }

  // 获取故事的扩展卡片
  static async getStoryCards(storyId: string): Promise<ExpandableCard[]> {
    try {
      const response = await api.get(`/analysis/story-cards/${storyId}`);
      return response.data;
    } catch (error) {
      console.error('获取故事卡片失败:', error);
      return this.getMockCards();
    }
  }

  // 提交用户反思
  static async submitReflection(storyId: string, content: string, isAnonymous: boolean = true) {
    try {
      const response = await api.post('/user-reflections', {
        storyId,
        content,
        isAnonymous,
        timestamp: new Date().toISOString()
      });
      return response.data;
    } catch (error) {
      console.error('提交用户反思失败:', error);
      // 模拟成功提交
      return { success: true, id: Date.now().toString() };
    }
  }

  // 将ContentItem转换为StoryContent
  private static transformContentToStory(contentItem: any): StoryContent {
    return {
      id: contentItem.id,
      title: this.extractTitle(contentItem.content.contentText) || '无标题故事',
      summary: this.extractSummary(contentItem.content.contentText),
      author: contentItem.author.name || '匿名用户',
      category: this.mapCategoryToTheme(contentItem.content.category),
      emotionalTags: this.extractEmotionalTags(contentItem.content.tags),
      createdAt: new Date(contentItem.content.publishedAt).toLocaleDateString(),
      engagement: {
        likes: contentItem.metrics.likesCount,
        comments: contentItem.metrics.commentsCount,
        shares: contentItem.metrics.sharesCount
      }
    };
  }

  // 提取标题
  private static extractTitle(text: string): string {
    const lines = text.split('\n');
    const firstLine = lines[0]?.trim();
    return firstLine && firstLine.length < 100 ? firstLine : '';
  }

  // 提取摘要
  private static extractSummary(text: string): string {
    // 简单的摘要提取逻辑，实际应该使用AI
    const sentences = text.split(/[.!?。！？]/);
    return sentences.slice(0, 5).join('。') + '。';
  }

  // 映射分类到主题
  private static mapCategoryToTheme(category: string): string {
    const mapping: Record<string, string> = {
      'psychology': 'anxiety',
      'growth': 'growth',
      'relationship': 'healing'
    };
    return mapping[category] || 'anxiety';
  }

  // 提取情感标签
  private static extractEmotionalTags(tags: string[]): string[] {
    const emotionalKeywords = ['突破', '理解', '希望', '成长', '治愈', '勇气', '接纳', '转变'];
    return tags.filter(tag =>
      emotionalKeywords.some(keyword => tag.includes(keyword))
    ).slice(0, 3);
  }

  // 模拟故事数据
  private static getMockStories(): StoryContent[] {
    return [
      {
        id: 'story-1',
        title: '社交焦虑的突破',
        summary: `在一次重要的公司聚餐中，我坐在熟悉的面孔中间，却像一座沉默的孤岛。我已经50岁了，却无法对年轻的同事说出一句话。脑海中有个声音不断攻击我，称我为"可悲"。为了逃避聚会后的活动，我撒了[一个简单的谎]，说自己身体不舒服。

独自在酒店房间里，[我对自己如此厌恶]，以至于产生了自青少年时期以来最黑暗的想法。

就在这时，[女朋友]的FaceTime通话打了进来。我无法掩饰自己的痛苦，在我坦白一切后，她温柔地说出了两个词：["社交焦虑"]。

就像一把钥匙，这些话解开了多年的困惑。我没有被治愈，但在那一刻，我终于感到自己并不孤单。`,
        author: '匿名用户',
        category: 'anxiety',
        emotionalTags: ['突破', '理解', '希望'],
        createdAt: '2024-01-15',
        engagement: {
          likes: 1247,
          comments: 89,
          shares: 156
        }
      },
      {
        id: 'story-2',
        title: '从抑郁中重新站起',
        summary: `三年前，我失去了工作、房子和几乎所有的朋友。抑郁症像一张黑网将我包围，每天早上醒来都是一种折磨。我以为我永远不会再快乐了。

但是今天，我想分享我是如何一步步走出来的。这不是一个奇迹故事，而是关于小小的日常胜利，关于寻求帮助的勇气，关于接受自己的不完美。

最重要的是，我学会了[自我同情]。我开始像对待好朋友一样对待自己。这改变了一切。`,
        author: '康复者小李',
        category: 'depression',
        emotionalTags: ['勇气', '自我同情', '康复'],
        createdAt: '2024-01-10',
        engagement: {
          likes: 892,
          comments: 67,
          shares: 134
        }
      }
    ];
  }

  // 模拟热点数据
  private static getMockHotspots(): StoryHotspot[] {
    return [
      {
        id: 'lie',
        text: '一个简单的谎',
        startIndex: 0,
        endIndex: 0,
        expandedContent: '事实是，我一周前就决定不喝酒了，我为此感到自豪。但我不敢说出"嘿，我现在不喝酒"这样简单的话。这个小小的事实感觉像是一个巨大的秘密，所以我选择了更容易、更羞耻的借口："我身体不舒服"。',
        type: 'emotion'
      }
    ];
  }

  // 模拟卡片数据
  private static getMockCards(): ExpandableCard[] {
    return [
      {
        id: 'first_step',
        title: '改变的第一步是什么？',
        content: '第二天在会议上我能够进行对话了吗？并不能。但改变的是，我对此感到没关系了。我允许自己保持安静而不因此恨自己。第一步不是成为一个新的人，而是接受当时的自己。',
        type: 'next_step',
        isExpanded: false
      }
    ];
  }
}