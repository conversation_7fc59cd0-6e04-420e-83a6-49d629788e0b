{"name": "mental-health-analyzer-frontend", "version": "1.0.0", "description": "Frontend for Mental Health Content Analyzer", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest --run", "test:watch": "vitest"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "antd": "^5.8.4", "axios": "^1.4.0", "react-router-dom": "^6.14.2", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "@ant-design/icons": "^5.2.5"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "typescript": "^5.1.6", "vite": "^4.4.5", "vitest": "^0.34.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/user-event": "^14.4.3"}}