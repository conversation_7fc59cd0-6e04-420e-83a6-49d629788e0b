import { RowDataPacket } from 'mysql2/promise';
import { QueryHelper } from '../config/queryHelper';

export interface TaskLog {
  id: string;
  taskId: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: any;
  createdAt: Date;
}

interface TaskLogRow extends RowDataPacket {
  id: string;
  task_id: string;
  level: 'info' | 'warning' | 'error';
  message: string;
  details?: string;
  created_at: Date;
}

export class TaskLogModel {
  private static mapRowToTaskLog(row: TaskLogRow): TaskLog {
    return {
      id: row.id,
      taskId: row.task_id,
      level: row.level,
      message: row.message,
      details: row.details ? JSON.parse(row.details) : undefined,
      createdAt: row.created_at
    };
  }

  static async create(log: Omit<TaskLog, 'id' | 'createdAt'>): Promise<TaskLog> {
    const id = QueryHelper.generateUUID();
    const now = new Date();
    
    const sql = `
      INSERT INTO task_logs (id, task_id, level, message, details, created_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      id,
      log.taskId,
      log.level,
      log.message,
      log.details ? JSON.stringify(log.details) : null,
      now
    ];

    await QueryHelper.query(sql, params);

    return {
      ...log,
      id,
      createdAt: now
    };
  }

  static async findByTaskId(
    taskId: string, 
    level?: 'info' | 'warning' | 'error',
    limit?: number,
    offset?: number
  ): Promise<TaskLog[]> {
    let sql = 'SELECT * FROM task_logs WHERE task_id = ?';
    const params: any[] = [taskId];

    if (level) {
      sql += ' AND level = ?';
      params.push(level);
    }

    sql += ' ORDER BY created_at DESC';

    if (limit) {
      sql += ' LIMIT ?';
      params.push(limit);
      
      if (offset) {
        sql += ' OFFSET ?';
        params.push(offset);
      }
    }

    const rows = await QueryHelper.query<TaskLogRow[]>(sql, params);
    return rows.map(row => this.mapRowToTaskLog(row));
  }

  static async findById(id: string): Promise<TaskLog | null> {
    const sql = 'SELECT * FROM task_logs WHERE id = ?';
    const rows = await QueryHelper.query<TaskLogRow[]>(sql, [id]);
    
    if (rows.length === 0) {
      return null;
    }

    return this.mapRowToTaskLog(rows[0]);
  }

  static async deleteByTaskId(taskId: string): Promise<number> {
    const sql = 'DELETE FROM task_logs WHERE task_id = ?';
    return await QueryHelper.delete(sql, [taskId]);
  }

  static async deleteOldLogs(daysOld: number = 30): Promise<number> {
    const sql = 'DELETE FROM task_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)';
    return await QueryHelper.delete(sql, [daysOld]);
  }

  static async getLogStats(taskId?: string): Promise<{
    total: number;
    info: number;
    warning: number;
    error: number;
  }> {
    let sql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN level = 'info' THEN 1 ELSE 0 END) as info,
        SUM(CASE WHEN level = 'warning' THEN 1 ELSE 0 END) as warning,
        SUM(CASE WHEN level = 'error' THEN 1 ELSE 0 END) as error
      FROM task_logs
    `;
    
    const params: any[] = [];
    if (taskId) {
      sql += ' WHERE task_id = ?';
      params.push(taskId);
    }

    const rows = await QueryHelper.query<any[]>(sql, params);
    const stats = rows[0];

    return {
      total: parseInt(stats.total) || 0,
      info: parseInt(stats.info) || 0,
      warning: parseInt(stats.warning) || 0,
      error: parseInt(stats.error) || 0
    };
  }

  // Helper methods for logging
  static async logInfo(taskId: string, message: string, details?: any): Promise<TaskLog> {
    return this.create({
      taskId,
      level: 'info',
      message,
      details
    });
  }

  static async logWarning(taskId: string, message: string, details?: any): Promise<TaskLog> {
    return this.create({
      taskId,
      level: 'warning',
      message,
      details
    });
  }

  static async logError(taskId: string, message: string, details?: any): Promise<TaskLog> {
    return this.create({
      taskId,
      level: 'error',
      message,
      details
    });
  }
}