import { RowDataPacket } from 'mysql2/promise';
import { Task, TaskConfig, TaskStatus } from '../types';
import { QueryHelper } from '../config/queryHelper';

interface TaskRow extends RowDataPacket {
  id: string;
  user_id: string;
  name: string;
  config: string;
  status: TaskStatus;
  progress: number;
  collected_count: number;
  created_at: Date;
  updated_at: Date;
}

export class TaskModel {
  private static mapRowToTask(row: TaskRow): Task {
    return {
      id: row.id,
      userId: row.user_id,
      name: row.name,
      config: JSON.parse(row.config),
      status: row.status,
      progress: row.progress,
      collectedCount: row.collected_count,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  static async create(task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> {
    const id = QueryHelper.generateUUID();
    const now = new Date();
    
    const sql = `
      INSERT INTO tasks (id, user_id, name, config, status, progress, collected_count, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      id,
      task.userId,
      task.name,
      JSON.stringify(task.config),
      task.status,
      task.progress,
      task.collectedCount,
      now,
      now
    ];

    await QueryHelper.query(sql, params);

    return {
      ...task,
      id,
      createdAt: now,
      updatedAt: now
    };
  }

  static async findById(id: string): Promise<Task | null> {
    const sql = 'SELECT * FROM tasks WHERE id = ?';
    const rows = await QueryHelper.query<TaskRow[]>(sql, [id]);
    
    if (rows.length === 0) {
      return null;
    }

    return this.mapRowToTask(rows[0]);
  }

  static async findByUserId(userId: string, limit?: number, offset?: number): Promise<Task[]> {
    let sql = 'SELECT * FROM tasks WHERE user_id = ? ORDER BY created_at DESC';
    const params: any[] = [userId];

    if (limit) {
      sql += ' LIMIT ?';
      params.push(limit);
      
      if (offset) {
        sql += ' OFFSET ?';
        params.push(offset);
      }
    }

    const rows = await QueryHelper.query<TaskRow[]>(sql, params);
    return rows.map(row => this.mapRowToTask(row));
  }

  static async findByStatus(status: TaskStatus): Promise<Task[]> {
    const sql = 'SELECT * FROM tasks WHERE status = ? ORDER BY created_at DESC';
    const rows = await QueryHelper.query<TaskRow[]>(sql, [status]);
    return rows.map(row => this.mapRowToTask(row));
  }

  static async update(id: string, updates: Partial<Task>): Promise<Task> {
    const updateFields: string[] = [];
    const params: any[] = [];

    // Build dynamic update query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id' && key !== 'createdAt') {
        const dbKey = key === 'userId' ? 'user_id' : 
                     key === 'collectedCount' ? 'collected_count' :
                     key === 'updatedAt' ? 'updated_at' : key;
        
        updateFields.push(`${dbKey} = ?`);
        
        if (key === 'config') {
          params.push(JSON.stringify(value));
        } else {
          params.push(value);
        }
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    // Always update the updated_at timestamp
    updateFields.push('updated_at = ?');
    params.push(new Date());
    params.push(id);

    const sql = `UPDATE tasks SET ${updateFields.join(', ')} WHERE id = ?`;
    const affectedRows = await QueryHelper.update(sql, params);

    if (affectedRows === 0) {
      throw new Error('Task not found');
    }

    // Return the updated task
    const updatedTask = await this.findById(id);
    if (!updatedTask) {
      throw new Error('Failed to retrieve updated task');
    }

    return updatedTask;
  }

  static async delete(id: string): Promise<boolean> {
    const sql = 'DELETE FROM tasks WHERE id = ?';
    const affectedRows = await QueryHelper.delete(sql, [id]);
    return affectedRows > 0;
  }

  static async updateProgress(id: string, progress: number, collectedCount?: number): Promise<void> {
    const updates: any = { progress };
    if (collectedCount !== undefined) {
      updates.collectedCount = collectedCount;
    }
    
    await this.update(id, updates);
  }

  static async updateStatus(id: string, status: TaskStatus, error?: string): Promise<void> {
    const updates: any = { status };
    if (error) {
      updates.error = error;
    }
    
    await this.update(id, updates);
  }

  static async getTaskStats(userId?: string): Promise<{
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
  }> {
    let sql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'running' THEN 1 ELSE 0 END) as running,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
      FROM tasks
    `;
    
    const params: any[] = [];
    if (userId) {
      sql += ' WHERE user_id = ?';
      params.push(userId);
    }

    const rows = await QueryHelper.query<any[]>(sql, params);
    const stats = rows[0];

    return {
      total: parseInt(stats.total) || 0,
      pending: parseInt(stats.pending) || 0,
      running: parseInt(stats.running) || 0,
      completed: parseInt(stats.completed) || 0,
      failed: parseInt(stats.failed) || 0
    };
  }

  static async search(
    userId: string,
    searchTerm?: string,
    status?: TaskStatus,
    page: number = 1,
    limit: number = 20
  ): Promise<{ tasks: Task[]; total: number }> {
    let sql = 'SELECT * FROM tasks WHERE user_id = ?';
    const params: any[] = [userId];

    if (searchTerm) {
      sql += ' AND name LIKE ?';
      params.push(`%${searchTerm}%`);
    }

    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }

    // Get total count
    const countSql = sql.replace('SELECT *', 'SELECT COUNT(*) as total');
    const countRows = await QueryHelper.query<any[]>(countSql, params);
    const total = parseInt(countRows[0].total) || 0;

    // Add pagination
    sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, (page - 1) * limit);

    const rows = await QueryHelper.query<TaskRow[]>(sql, params);
    const tasks = rows.map(row => this.mapRowToTask(row));

    return { tasks, total };
  }
}