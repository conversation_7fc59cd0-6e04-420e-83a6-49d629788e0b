import { RowDataPacket } from 'mysql2/promise';
import { Platform } from '../types';
import { QueryHelper } from '../config/queryHelper';

export interface PlatformConfig {
  id: string;
  platform: Platform;
  configKey: string;
  configValue?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface PlatformConfigRow extends RowDataPacket {
  id: string;
  platform: Platform;
  config_key: string;
  config_value?: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export class PlatformConfigModel {
  private static mapRowToPlatformConfig(row: PlatformConfigRow): PlatformConfig {
    return {
      id: row.id,
      platform: row.platform,
      configKey: row.config_key,
      configValue: row.config_value,
      isActive: row.is_active,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  static async create(config: Omit<PlatformConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<PlatformConfig> {
    const id = QueryHelper.generateUUID();
    const now = new Date();
    
    const sql = `
      INSERT INTO platform_configs (id, platform, config_key, config_value, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      id,
      config.platform,
      config.configKey,
      config.configValue,
      config.isActive,
      now,
      now
    ];

    await QueryHelper.query(sql, params);

    return {
      ...config,
      id,
      createdAt: now,
      updatedAt: now
    };
  }

  static async findById(id: string): Promise<PlatformConfig | null> {
    const sql = 'SELECT * FROM platform_configs WHERE id = ?';
    const rows = await QueryHelper.query<PlatformConfigRow[]>(sql, [id]);
    
    if (rows.length === 0) {
      return null;
    }

    return this.mapRowToPlatformConfig(rows[0]);
  }

  static async findByPlatform(platform: Platform, activeOnly: boolean = true): Promise<PlatformConfig[]> {
    let sql = 'SELECT * FROM platform_configs WHERE platform = ?';
    const params: any[] = [platform];

    if (activeOnly) {
      sql += ' AND is_active = true';
    }

    sql += ' ORDER BY config_key';

    const rows = await QueryHelper.query<PlatformConfigRow[]>(sql, params);
    return rows.map(row => this.mapRowToPlatformConfig(row));
  }

  static async findByPlatformAndKey(platform: Platform, configKey: string): Promise<PlatformConfig | null> {
    const sql = 'SELECT * FROM platform_configs WHERE platform = ? AND config_key = ?';
    const rows = await QueryHelper.query<PlatformConfigRow[]>(sql, [platform, configKey]);
    
    if (rows.length === 0) {
      return null;
    }

    return this.mapRowToPlatformConfig(rows[0]);
  }

  static async getConfigValue(platform: Platform, configKey: string, defaultValue?: string): Promise<string | null> {
    const config = await this.findByPlatformAndKey(platform, configKey);
    
    if (!config || !config.isActive) {
      return defaultValue || null;
    }

    return config.configValue || defaultValue || null;
  }

  static async setConfigValue(
    platform: Platform, 
    configKey: string, 
    configValue: string,
    isActive: boolean = true
  ): Promise<PlatformConfig> {
    const existing = await this.findByPlatformAndKey(platform, configKey);
    
    if (existing) {
      return this.update(existing.id, { configValue, isActive });
    } else {
      return this.create({
        platform,
        configKey,
        configValue,
        isActive
      });
    }
  }

  static async update(id: string, updates: Partial<PlatformConfig>): Promise<PlatformConfig> {
    const updateFields: string[] = [];
    const params: any[] = [];

    // Build dynamic update query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id' && key !== 'createdAt') {
        const dbKey = key === 'configKey' ? 'config_key' :
                     key === 'configValue' ? 'config_value' :
                     key === 'isActive' ? 'is_active' :
                     key === 'updatedAt' ? 'updated_at' : key;
        
        updateFields.push(`${dbKey} = ?`);
        params.push(value);
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    // Always update the updated_at timestamp
    updateFields.push('updated_at = ?');
    params.push(new Date());
    params.push(id);

    const sql = `UPDATE platform_configs SET ${updateFields.join(', ')} WHERE id = ?`;
    const affectedRows = await QueryHelper.update(sql, params);

    if (affectedRows === 0) {
      throw new Error('Platform config not found');
    }

    // Return the updated config
    const updatedConfig = await this.findById(id);
    if (!updatedConfig) {
      throw new Error('Failed to retrieve updated platform config');
    }

    return updatedConfig;
  }

  static async delete(id: string): Promise<boolean> {
    const sql = 'DELETE FROM platform_configs WHERE id = ?';
    const affectedRows = await QueryHelper.delete(sql, [id]);
    return affectedRows > 0;
  }

  static async deleteByPlatformAndKey(platform: Platform, configKey: string): Promise<boolean> {
    const sql = 'DELETE FROM platform_configs WHERE platform = ? AND config_key = ?';
    const affectedRows = await QueryHelper.delete(sql, [platform, configKey]);
    return affectedRows > 0;
  }

  static async toggleActive(id: string): Promise<PlatformConfig> {
    const config = await this.findById(id);
    if (!config) {
      throw new Error('Platform config not found');
    }

    return this.update(id, { isActive: !config.isActive });
  }

  static async getAllConfigs(): Promise<Record<Platform, Record<string, string>>> {
    const sql = 'SELECT * FROM platform_configs WHERE is_active = true ORDER BY platform, config_key';
    const rows = await QueryHelper.query<PlatformConfigRow[]>(sql);
    
    const configs: Record<Platform, Record<string, string>> = {} as any;
    
    rows.forEach(row => {
      if (!configs[row.platform]) {
        configs[row.platform] = {};
      }
      if (row.config_value) {
        configs[row.platform][row.config_key] = row.config_value;
      }
    });

    return configs;
  }

  static async getPlatformConfigs(platform: Platform): Promise<Record<string, string>> {
    const configs = await this.findByPlatform(platform, true);
    const result: Record<string, string> = {};
    
    configs.forEach(config => {
      if (config.configValue) {
        result[config.configKey] = config.configValue;
      }
    });

    return result;
  }

  static async initializeDefaultConfigs(): Promise<void> {
    const defaultConfigs = [
      { platform: Platform.XIAOHONGSHU, configKey: 'api_endpoint', configValue: 'https://api.xiaohongshu.com' },
      { platform: Platform.XIAOHONGSHU, configKey: 'rate_limit_per_minute', configValue: '60' },
      { platform: Platform.DOUYIN, configKey: 'api_endpoint', configValue: 'https://api.douyin.com' },
      { platform: Platform.DOUYIN, configKey: 'rate_limit_per_minute', configValue: '100' },
      { platform: Platform.WECHAT_ARTICLE, configKey: 'api_endpoint', configValue: 'https://api.weixin.qq.com' },
      { platform: Platform.WECHAT_ARTICLE, configKey: 'rate_limit_per_minute', configValue: '30' },
      { platform: Platform.WECHAT_VIDEO, configKey: 'api_endpoint', configValue: 'https://api.weixin.qq.com' },
      { platform: Platform.WECHAT_VIDEO, configKey: 'rate_limit_per_minute', configValue: '30' }
    ];

    for (const config of defaultConfigs) {
      const existing = await this.findByPlatformAndKey(config.platform, config.configKey);
      if (!existing) {
        await this.create({
          platform: config.platform,
          configKey: config.configKey,
          configValue: config.configValue,
          isActive: true
        });
      }
    }
  }
}