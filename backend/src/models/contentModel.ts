import { RowDataPacket } from 'mysql2/promise';
import { 
  ContentItem, 
  Platform, 
  ContentType, 
  ContentCategory, 
  AuthorInfo, 
  ContentInfo, 
  MetricsInfo,
  SearchParams 
} from '../types';
import { QueryHelper } from '../config/queryHelper';

interface ContentRow extends RowDataPacket {
  id: string;
  task_id: string;
  platform: Platform;
  author_id: string;
  author_name: string;
  author_followers: number;
  author_is_verified: boolean;
  author_avatar_url?: string;
  content_id: string;
  content_type: ContentType;
  title?: string;
  description?: string;
  content_text?: string;
  media_urls: string;
  tags: string;
  original_url: string;
  published_at?: Date;
  collected_at: Date;
  category: ContentCategory;
  // Metrics from joined table
  likes_count: number;
  comments_count: number;
  shares_count: number;
  views_count: number;
  favorites_count: number;
  engagement_rate: number;
}

export class ContentModel {
  private static mapRowToContentItem(row: ContentRow): ContentItem {
    const author: AuthorInfo = {
      id: row.author_id,
      name: row.author_name,
      followers: row.author_followers || 0,
      isVerified: row.author_is_verified || false,
      avatarUrl: row.author_avatar_url,
      platform: row.platform
    };

    const content: ContentInfo = {
      id: row.content_id,
      title: row.title,
      description: row.description,
      contentText: row.content_text,
      mediaUrls: row.media_urls ? JSON.parse(row.media_urls) : [],
      tags: row.tags ? JSON.parse(row.tags) : [],
      publishedAt: row.published_at || new Date(),
      contentType: row.content_type,
      category: row.category
    };

    const metrics: MetricsInfo = {
      likesCount: row.likes_count || 0,
      commentsCount: row.comments_count || 0,
      sharesCount: row.shares_count || 0,
      viewsCount: row.views_count || 0,
      favoritesCount: row.favorites_count || 0,
      engagementRate: row.engagement_rate || 0
    };

    return {
      id: row.id,
      taskId: row.task_id,
      platform: row.platform,
      author,
      content,
      metrics,
      originalUrl: row.original_url,
      collectedAt: row.collected_at
    };
  }

  static async create(contentItem: Omit<ContentItem, 'id' | 'collectedAt'>): Promise<ContentItem> {
    const contentId = QueryHelper.generateUUID();
    const now = new Date();

    return await QueryHelper.transaction(async (connection) => {
      // Insert content record
      const contentSql = `
        INSERT INTO contents (
          id, task_id, platform, author_id, author_name, author_followers, 
          author_is_verified, author_avatar_url, content_id, content_type, 
          title, description, content_text, media_urls, tags, original_url, 
          published_at, collected_at, category
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const contentParams = [
        contentId,
        contentItem.taskId,
        contentItem.platform,
        contentItem.author.id,
        contentItem.author.name,
        contentItem.author.followers,
        contentItem.author.isVerified,
        contentItem.author.avatarUrl,
        contentItem.content.id,
        contentItem.content.contentType,
        contentItem.content.title,
        contentItem.content.description,
        contentItem.content.contentText,
        JSON.stringify(contentItem.content.mediaUrls),
        JSON.stringify(contentItem.content.tags),
        contentItem.originalUrl,
        contentItem.content.publishedAt,
        now,
        contentItem.content.category || ContentCategory.OTHER
      ];

      await connection.execute(contentSql, contentParams);

      // Insert metrics record
      const metricsSql = `
        INSERT INTO content_metrics (
          content_id, likes_count, comments_count, shares_count, 
          views_count, favorites_count, engagement_rate
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      const metricsParams = [
        contentId,
        contentItem.metrics.likesCount,
        contentItem.metrics.commentsCount,
        contentItem.metrics.sharesCount,
        contentItem.metrics.viewsCount,
        contentItem.metrics.favoritesCount,
        contentItem.metrics.engagementRate || 0
      ];

      await connection.execute(metricsSql, metricsParams);

      return {
        ...contentItem,
        id: contentId,
        collectedAt: now
      };
    });
  }

  static async findById(id: string): Promise<ContentItem | null> {
    const sql = `
      SELECT c.*, m.likes_count, m.comments_count, m.shares_count, 
             m.views_count, m.favorites_count, m.engagement_rate
      FROM contents c
      LEFT JOIN content_metrics m ON c.id = m.content_id
      WHERE c.id = ?
    `;

    const rows = await QueryHelper.query<ContentRow[]>(sql, [id]);
    
    if (rows.length === 0) {
      return null;
    }

    return this.mapRowToContentItem(rows[0]);
  }

  static async findByTaskId(taskId: string, limit?: number, offset?: number): Promise<ContentItem[]> {
    let sql = `
      SELECT c.*, m.likes_count, m.comments_count, m.shares_count, 
             m.views_count, m.favorites_count, m.engagement_rate
      FROM contents c
      LEFT JOIN content_metrics m ON c.id = m.content_id
      WHERE c.task_id = ?
      ORDER BY c.collected_at DESC
    `;

    const params: any[] = [taskId];

    if (limit) {
      sql += ' LIMIT ?';
      params.push(limit);
      
      if (offset) {
        sql += ' OFFSET ?';
        params.push(offset);
      }
    }

    const rows = await QueryHelper.query<ContentRow[]>(sql, params);
    return rows.map(row => this.mapRowToContentItem(row));
  }

  static async search(searchParams: SearchParams): Promise<{ content: ContentItem[]; total: number }> {
    let sql = `
      SELECT c.*, m.likes_count, m.comments_count, m.shares_count, 
             m.views_count, m.favorites_count, m.engagement_rate
      FROM contents c
      LEFT JOIN content_metrics m ON c.id = m.content_id
      WHERE 1=1
    `;

    const params: any[] = [];

    // Add search conditions
    if (searchParams.keywords) {
      sql += ' AND (c.title LIKE ? OR c.description LIKE ? OR c.content_text LIKE ?)';
      const keyword = `%${searchParams.keywords}%`;
      params.push(keyword, keyword, keyword);
    }

    if (searchParams.platforms && searchParams.platforms.length > 0) {
      sql += ` AND c.platform IN (${searchParams.platforms.map(() => '?').join(', ')})`;
      params.push(...searchParams.platforms);
    }

    if (searchParams.categories && searchParams.categories.length > 0) {
      sql += ` AND c.category IN (${searchParams.categories.map(() => '?').join(', ')})`;
      params.push(...searchParams.categories);
    }

    if (searchParams.contentTypes && searchParams.contentTypes.length > 0) {
      sql += ` AND c.content_type IN (${searchParams.contentTypes.map(() => '?').join(', ')})`;
      params.push(...searchParams.contentTypes);
    }

    if (searchParams.dateRange) {
      if (searchParams.dateRange.startDate) {
        sql += ' AND c.published_at >= ?';
        params.push(searchParams.dateRange.startDate);
      }
      if (searchParams.dateRange.endDate) {
        sql += ' AND c.published_at <= ?';
        params.push(searchParams.dateRange.endDate);
      }
    }

    if (searchParams.minMetrics) {
      if (searchParams.minMetrics.likes) {
        sql += ' AND m.likes_count >= ?';
        params.push(searchParams.minMetrics.likes);
      }
      if (searchParams.minMetrics.comments) {
        sql += ' AND m.comments_count >= ?';
        params.push(searchParams.minMetrics.comments);
      }
      if (searchParams.minMetrics.views) {
        sql += ' AND m.views_count >= ?';
        params.push(searchParams.minMetrics.views);
      }
      if (searchParams.minMetrics.followers) {
        sql += ' AND c.author_followers >= ?';
        params.push(searchParams.minMetrics.followers);
      }
    }

    // Get total count
    const countSql = sql.replace(
      'SELECT c.*, m.likes_count, m.comments_count, m.shares_count, m.views_count, m.favorites_count, m.engagement_rate',
      'SELECT COUNT(*) as total'
    );
    const countRows = await QueryHelper.query<any[]>(countSql, params);
    const total = parseInt(countRows[0].total) || 0;

    // Add sorting
    if (searchParams.sortBy) {
      const sortColumn = searchParams.sortBy === 'publishedAt' ? 'c.published_at' :
                        searchParams.sortBy === 'likesCount' ? 'm.likes_count' :
                        searchParams.sortBy === 'commentsCount' ? 'm.comments_count' :
                        searchParams.sortBy === 'viewsCount' ? 'm.views_count' :
                        searchParams.sortBy === 'engagementRate' ? 'm.engagement_rate' :
                        'c.collected_at';
      
      sql += ` ORDER BY ${sortColumn} ${searchParams.sortOrder || 'DESC'}`;
    } else {
      sql += ' ORDER BY c.collected_at DESC';
    }

    // Add pagination
    const page = searchParams.page || 1;
    const limit = searchParams.limit || 20;
    sql += ' LIMIT ? OFFSET ?';
    params.push(limit, (page - 1) * limit);

    const rows = await QueryHelper.query<ContentRow[]>(sql, params);
    const content = rows.map(row => this.mapRowToContentItem(row));

    return { content, total };
  }

  static async update(id: string, updates: Partial<ContentItem>): Promise<ContentItem> {
    return await QueryHelper.transaction(async (connection) => {
      // Update content table if needed
      const contentUpdates: string[] = [];
      const contentParams: any[] = [];

      if (updates.content) {
        if (updates.content.title !== undefined) {
          contentUpdates.push('title = ?');
          contentParams.push(updates.content.title);
        }
        if (updates.content.description !== undefined) {
          contentUpdates.push('description = ?');
          contentParams.push(updates.content.description);
        }
        if (updates.content.category !== undefined) {
          contentUpdates.push('category = ?');
          contentParams.push(updates.content.category);
        }
      }

      if (contentUpdates.length > 0) {
        contentParams.push(id);
        const contentSql = `UPDATE contents SET ${contentUpdates.join(', ')} WHERE id = ?`;
        await connection.execute(contentSql, contentParams);
      }

      // Update metrics table if needed
      if (updates.metrics) {
        const metricsUpdates: string[] = [];
        const metricsParams: any[] = [];

        Object.entries(updates.metrics).forEach(([key, value]) => {
          if (value !== undefined) {
            const dbKey = key === 'likesCount' ? 'likes_count' :
                         key === 'commentsCount' ? 'comments_count' :
                         key === 'sharesCount' ? 'shares_count' :
                         key === 'viewsCount' ? 'views_count' :
                         key === 'favoritesCount' ? 'favorites_count' :
                         key === 'engagementRate' ? 'engagement_rate' : key;
            
            metricsUpdates.push(`${dbKey} = ?`);
            metricsParams.push(value);
          }
        });

        if (metricsUpdates.length > 0) {
          metricsUpdates.push('updated_at = ?');
          metricsParams.push(new Date());
          metricsParams.push(id);
          
          const metricsSql = `UPDATE content_metrics SET ${metricsUpdates.join(', ')} WHERE content_id = ?`;
          await connection.execute(metricsSql, metricsParams);
        }
      }

      // Return updated content
      const updatedContent = await this.findById(id);
      if (!updatedContent) {
        throw new Error('Failed to retrieve updated content');
      }

      return updatedContent;
    });
  }

  static async delete(id: string): Promise<boolean> {
    // The foreign key constraints will handle cascading deletes
    const sql = 'DELETE FROM contents WHERE id = ?';
    const affectedRows = await QueryHelper.delete(sql, [id]);
    return affectedRows > 0;
  }

  static async findDuplicates(platform: Platform, contentId: string): Promise<ContentItem[]> {
    const sql = `
      SELECT c.*, m.likes_count, m.comments_count, m.shares_count, 
             m.views_count, m.favorites_count, m.engagement_rate
      FROM contents c
      LEFT JOIN content_metrics m ON c.id = m.content_id
      WHERE c.platform = ? AND c.content_id = ?
      ORDER BY c.collected_at DESC
    `;

    const rows = await QueryHelper.query<ContentRow[]>(sql, [platform, contentId]);
    return rows.map(row => this.mapRowToContentItem(row));
  }

  static async getHotContent(
    platform?: Platform, 
    category?: ContentCategory, 
    limit: number = 50
  ): Promise<ContentItem[]> {
    let sql = `
      SELECT c.*, m.likes_count, m.comments_count, m.shares_count, 
             m.views_count, m.favorites_count, m.engagement_rate,
             (m.likes_count + m.comments_count * 2 + m.shares_count * 3 + m.views_count * 0.1) as hot_score
      FROM contents c
      LEFT JOIN content_metrics m ON c.id = m.content_id
      WHERE c.published_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    `;

    const params: any[] = [];

    if (platform) {
      sql += ' AND c.platform = ?';
      params.push(platform);
    }

    if (category) {
      sql += ' AND c.category = ?';
      params.push(category);
    }

    sql += ' ORDER BY hot_score DESC, m.engagement_rate DESC LIMIT ?';
    params.push(limit);

    const rows = await QueryHelper.query<ContentRow[]>(sql, params);
    return rows.map(row => this.mapRowToContentItem(row));
  }

  static async getContentStats(taskId?: string): Promise<{
    total: number;
    byPlatform: Record<Platform, number>;
    byCategory: Record<ContentCategory, number>;
    byContentType: Record<ContentType, number>;
  }> {
    let sql = `
      SELECT 
        COUNT(*) as total,
        platform,
        category,
        content_type
      FROM contents
    `;

    const params: any[] = [];
    if (taskId) {
      sql += ' WHERE task_id = ?';
      params.push(taskId);
    }

    sql += ' GROUP BY platform, category, content_type';

    const rows = await QueryHelper.query<any[]>(sql, params);

    const stats = {
      total: 0,
      byPlatform: {} as Record<Platform, number>,
      byCategory: {} as Record<ContentCategory, number>,
      byContentType: {} as Record<ContentType, number>
    };

    rows.forEach(row => {
      const count = parseInt(row.total) || 0;
      stats.total += count;
      
      stats.byPlatform[row.platform] = (stats.byPlatform[row.platform] || 0) + count;
      stats.byCategory[row.category] = (stats.byCategory[row.category] || 0) + count;
      stats.byContentType[row.content_type] = (stats.byContentType[row.content_type] || 0) + count;
    });

    return stats;
  }
}