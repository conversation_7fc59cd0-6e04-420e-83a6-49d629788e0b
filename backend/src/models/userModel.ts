import { RowDataPacket } from 'mysql2/promise';
import { QueryHelper } from '../config/queryHelper';

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  createdAt: Date;
  updatedAt: Date;
}

interface UserRow extends RowDataPacket {
  id: string;
  username: string;
  email: string;
  password_hash: string;
  role: 'admin' | 'user';
  created_at: Date;
  updated_at: Date;
}

export class UserModel {
  private static mapRowToUser(row: UserRow): User {
    return {
      id: row.id,
      username: row.username,
      email: row.email,
      role: row.role,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  static async create(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'> & { passwordHash: string }): Promise<User> {
    const id = QueryHelper.generateUUID();
    const now = new Date();
    
    const sql = `
      INSERT INTO users (id, username, email, password_hash, role, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      id,
      user.username,
      user.email,
      user.passwordHash,
      user.role || 'user',
      now,
      now
    ];

    await QueryHelper.query(sql, params);

    return {
      id,
      username: user.username,
      email: user.email,
      role: user.role || 'user',
      createdAt: now,
      updatedAt: now
    };
  }

  static async findById(id: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE id = ?';
    const rows = await QueryHelper.query<UserRow[]>(sql, [id]);
    
    if (rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }

  static async findByEmail(email: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE email = ?';
    const rows = await QueryHelper.query<UserRow[]>(sql, [email]);
    
    if (rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }

  static async findByUsername(username: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE username = ?';
    const rows = await QueryHelper.query<UserRow[]>(sql, [username]);
    
    if (rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }

  static async findByEmailOrUsername(emailOrUsername: string): Promise<User | null> {
    const sql = 'SELECT * FROM users WHERE email = ? OR username = ?';
    const rows = await QueryHelper.query<UserRow[]>(sql, [emailOrUsername, emailOrUsername]);
    
    if (rows.length === 0) {
      return null;
    }

    return this.mapRowToUser(rows[0]);
  }

  static async getPasswordHash(id: string): Promise<string | null> {
    const sql = 'SELECT password_hash FROM users WHERE id = ?';
    const rows = await QueryHelper.query<any[]>(sql, [id]);
    
    if (rows.length === 0) {
      return null;
    }

    return rows[0].password_hash;
  }

  static async updatePassword(id: string, passwordHash: string): Promise<void> {
    const sql = 'UPDATE users SET password_hash = ?, updated_at = ? WHERE id = ?';
    const affectedRows = await QueryHelper.update(sql, [passwordHash, new Date(), id]);
    
    if (affectedRows === 0) {
      throw new Error('User not found');
    }
  }

  static async update(id: string, updates: Partial<Omit<User, 'id' | 'createdAt'>>): Promise<User> {
    const updateFields: string[] = [];
    const params: any[] = [];

    // Build dynamic update query
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'id' && key !== 'createdAt') {
        const dbKey = key === 'updatedAt' ? 'updated_at' : key;
        updateFields.push(`${dbKey} = ?`);
        params.push(value);
      }
    });

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    // Always update the updated_at timestamp
    updateFields.push('updated_at = ?');
    params.push(new Date());
    params.push(id);

    const sql = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;
    const affectedRows = await QueryHelper.update(sql, params);

    if (affectedRows === 0) {
      throw new Error('User not found');
    }

    // Return the updated user
    const updatedUser = await this.findById(id);
    if (!updatedUser) {
      throw new Error('Failed to retrieve updated user');
    }

    return updatedUser;
  }

  static async delete(id: string): Promise<boolean> {
    const sql = 'DELETE FROM users WHERE id = ?';
    const affectedRows = await QueryHelper.delete(sql, [id]);
    return affectedRows > 0;
  }

  static async list(page: number = 1, limit: number = 20): Promise<{ users: User[]; total: number }> {
    // Get total count
    const countSql = 'SELECT COUNT(*) as total FROM users';
    const countRows = await QueryHelper.query<any[]>(countSql);
    const total = parseInt(countRows[0].total) || 0;

    // Get users with pagination
    const sql = 'SELECT * FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?';
    const rows = await QueryHelper.query<UserRow[]>(sql, [limit, (page - 1) * limit]);
    const users = rows.map(row => this.mapRowToUser(row));

    return { users, total };
  }

  static async search(
    searchTerm: string,
    page: number = 1,
    limit: number = 20
  ): Promise<{ users: User[]; total: number }> {
    const searchPattern = `%${searchTerm}%`;
    
    // Get total count
    const countSql = 'SELECT COUNT(*) as total FROM users WHERE username LIKE ? OR email LIKE ?';
    const countRows = await QueryHelper.query<any[]>(countSql, [searchPattern, searchPattern]);
    const total = parseInt(countRows[0].total) || 0;

    // Get users with pagination
    const sql = `
      SELECT * FROM users 
      WHERE username LIKE ? OR email LIKE ? 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `;
    const rows = await QueryHelper.query<UserRow[]>(sql, [
      searchPattern, 
      searchPattern, 
      limit, 
      (page - 1) * limit
    ]);
    const users = rows.map(row => this.mapRowToUser(row));

    return { users, total };
  }

  static async getUserStats(): Promise<{
    total: number;
    admins: number;
    regularUsers: number;
    recentlyActive: number;
  }> {
    const sql = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admins,
        SUM(CASE WHEN role = 'user' THEN 1 ELSE 0 END) as regular_users,
        SUM(CASE WHEN updated_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recently_active
      FROM users
    `;

    const rows = await QueryHelper.query<any[]>(sql);
    const stats = rows[0];

    return {
      total: parseInt(stats.total) || 0,
      admins: parseInt(stats.admins) || 0,
      regularUsers: parseInt(stats.regular_users) || 0,
      recentlyActive: parseInt(stats.recently_active) || 0
    };
  }
}