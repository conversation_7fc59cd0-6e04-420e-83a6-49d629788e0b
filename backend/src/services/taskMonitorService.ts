import { EventEmitter } from 'events';
import { Task, TaskStatus } from '../types';
import { TaskModel } from '../models/taskModel';
import { TaskLogModel, TaskLog } from '../models/taskLogModel';

export interface TaskProgressUpdate {
  taskId: string;
  progress: number;
  collectedCount: number;
  estimatedTime?: number;
  currentAction?: string;
  error?: string;
}

export interface TaskMonitorStats {
  taskId: string;
  status: TaskStatus;
  progress: number;
  collectedCount: number;
  estimatedTime?: number;
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  averageSpeed?: number; // items per minute
  errorCount: number;
  warningCount: number;
  lastActivity?: Date;
}

export class TaskMonitorService extends EventEmitter {
  private progressTrackers: Map<string, {
    startTime: Date;
    lastUpdate: Date;
    collectedCount: number;
    progress: number;
    samples: Array<{ time: Date; count: number }>;
  }> = new Map();

  private readonly SAMPLE_SIZE = 10; // Number of samples for speed calculation
  private readonly UPDATE_INTERVAL = 5000; // 5 seconds
  private cleanupInterval?: NodeJS.Timeout;

  constructor() {
    super();
    // Only start cleanup timer in production environment
    if (process.env.NODE_ENV !== 'test') {
      this.startCleanupTimer();
    }
  }

  /**
   * 开始监控任务
   */
  async startMonitoring(taskId: string): Promise<void> {
    const task = await TaskModel.findById(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    // 初始化进度跟踪器
    this.progressTrackers.set(taskId, {
      startTime: new Date(),
      lastUpdate: new Date(),
      collectedCount: task.collectedCount,
      progress: task.progress,
      samples: [{ time: new Date(), count: task.collectedCount }]
    });

    // 记录开始监控日志
    await TaskLogModel.logInfo(taskId, '开始任务监控', {
      initialProgress: task.progress,
      initialCount: task.collectedCount
    });

    this.emit('monitoringStarted', { taskId, task });
  }

  /**
   * 停止监控任务
   */
  async stopMonitoring(taskId: string): Promise<void> {
    const tracker = this.progressTrackers.get(taskId);
    if (!tracker) {
      return;
    }

    const task = await TaskModel.findById(taskId);
    if (task) {
      const duration = Date.now() - tracker.startTime.getTime();
      await TaskLogModel.logInfo(taskId, '停止任务监控', {
        finalProgress: task.progress,
        finalCount: task.collectedCount,
        duration: Math.round(duration / 1000) // seconds
      });
    }

    this.progressTrackers.delete(taskId);
    this.emit('monitoringStopped', { taskId });
  }

  /**
   * 更新任务进度
   */
  async updateProgress(update: TaskProgressUpdate): Promise<void> {
    const { taskId, progress, collectedCount, currentAction, error } = update;
    
    // 验证进度值
    if (progress < 0 || progress > 100) {
      throw new Error('进度值必须在0-100之间');
    }

    const tracker = this.progressTrackers.get(taskId);
    if (!tracker) {
      throw new Error('任务未在监控中');
    }

    const now = new Date();
    
    // 更新跟踪器数据
    tracker.lastUpdate = now;
    tracker.progress = progress;
    tracker.collectedCount = collectedCount;
    
    // 添加新样本用于速度计算
    tracker.samples.push({ time: now, count: collectedCount });
    if (tracker.samples.length > this.SAMPLE_SIZE) {
      tracker.samples.shift();
    }

    // 计算预估时间
    const estimatedTime = this.calculateEstimatedTime(taskId, progress);

    // 更新数据库
    await TaskModel.updateProgress(taskId, progress, collectedCount);

    // 记录进度日志
    await TaskLogModel.logInfo(taskId, '任务进度更新', {
      progress,
      collectedCount,
      estimatedTime,
      currentAction
    });

    // 如果有错误，记录错误日志
    if (error) {
      await TaskLogModel.logError(taskId, '任务执行错误', { error });
    }

    // 发出进度更新事件
    this.emit('progressUpdated', {
      taskId,
      progress,
      collectedCount,
      estimatedTime,
      currentAction,
      error
    });
  }

  /**
   * 计算预估完成时间
   */
  private calculateEstimatedTime(taskId: string, currentProgress: number): number | undefined {
    const tracker = this.progressTrackers.get(taskId);
    if (!tracker || tracker.samples.length < 2) {
      return undefined;
    }

    // 计算平均速度（每分钟采集数量）
    const samples = tracker.samples;
    const firstSample = samples[0];
    const lastSample = samples[samples.length - 1];
    
    const timeDiff = lastSample.time.getTime() - firstSample.time.getTime();
    const countDiff = lastSample.count - firstSample.count;
    
    if (timeDiff <= 0 || countDiff <= 0) {
      return undefined;
    }

    const speed = (countDiff / timeDiff) * 60000; // items per minute
    
    if (currentProgress >= 100) {
      return 0;
    }

    // 基于进度百分比估算剩余时间
    const remainingProgress = 100 - currentProgress;
    const progressSpeed = (currentProgress - (tracker.samples[0] ? 0 : currentProgress)) / (timeDiff / 60000);
    
    if (progressSpeed <= 0) {
      return undefined;
    }

    return Math.round(remainingProgress / progressSpeed); // minutes
  }

  /**
   * 获取任务监控统计信息
   */
  async getTaskStats(taskId: string): Promise<TaskMonitorStats> {
    const task = await TaskModel.findById(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    const tracker = this.progressTrackers.get(taskId);
    const logStats = await TaskLogModel.getLogStats(taskId);

    let averageSpeed: number | undefined;
    let duration: number | undefined;
    let estimatedTime: number | undefined;

    if (tracker) {
      // 计算平均速度
      if (tracker.samples.length >= 2) {
        const firstSample = tracker.samples[0];
        const lastSample = tracker.samples[tracker.samples.length - 1];
        const timeDiff = lastSample.time.getTime() - firstSample.time.getTime();
        const countDiff = lastSample.count - firstSample.count;
        
        if (timeDiff > 0 && countDiff > 0) {
          averageSpeed = (countDiff / timeDiff) * 60000; // items per minute
        }
      }

      // 计算持续时间
      duration = Date.now() - tracker.startTime.getTime();
      
      // 计算预估时间
      estimatedTime = this.calculateEstimatedTime(taskId, task.progress);
    }

    return {
      taskId,
      status: task.status,
      progress: task.progress,
      collectedCount: task.collectedCount,
      estimatedTime,
      startTime: tracker?.startTime,
      endTime: task.status === TaskStatus.COMPLETED || task.status === TaskStatus.FAILED 
        ? task.updatedAt : undefined,
      duration,
      averageSpeed,
      errorCount: logStats.error,
      warningCount: logStats.warning,
      lastActivity: tracker?.lastUpdate
    };
  }

  /**
   * 获取所有正在监控的任务
   */
  getMonitoredTasks(): string[] {
    return Array.from(this.progressTrackers.keys());
  }

  /**
   * 批量获取任务统计信息
   */
  async getBatchTaskStats(taskIds: string[]): Promise<TaskMonitorStats[]> {
    const statsPromises = taskIds.map(taskId => 
      this.getTaskStats(taskId).catch(error => {
        console.error(`获取任务 ${taskId} 统计信息失败:`, error);
        return null;
      })
    );

    const results = await Promise.all(statsPromises);
    return results.filter((stats): stats is TaskMonitorStats => stats !== null);
  }

  /**
   * 记录任务错误
   */
  async logTaskError(taskId: string, error: string, details?: any): Promise<void> {
    await TaskLogModel.logError(taskId, error, details);
    
    this.emit('taskError', {
      taskId,
      error,
      details,
      timestamp: new Date()
    });
  }

  /**
   * 记录任务警告
   */
  async logTaskWarning(taskId: string, warning: string, details?: any): Promise<void> {
    await TaskLogModel.logWarning(taskId, warning, details);
    
    this.emit('taskWarning', {
      taskId,
      warning,
      details,
      timestamp: new Date()
    });
  }

  /**
   * 记录任务信息
   */
  async logTaskInfo(taskId: string, message: string, details?: any): Promise<void> {
    await TaskLogModel.logInfo(taskId, message, details);
    
    this.emit('taskInfo', {
      taskId,
      message,
      details,
      timestamp: new Date()
    });
  }

  /**
   * 获取任务日志
   */
  async getTaskLogs(
    taskId: string,
    level?: 'info' | 'warning' | 'error',
    page: number = 1,
    limit: number = 50
  ): Promise<{
    logs: TaskLog[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const offset = (page - 1) * limit;
    const logs = await TaskLogModel.findByTaskId(taskId, level, limit, offset);
    
    // 获取总数（简化实现，实际应该有专门的计数查询）
    const allLogs = await TaskLogModel.findByTaskId(taskId, level);
    const total = allLogs.length;

    return {
      logs,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * 清理过期的监控数据
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      const now = Date.now();
      const expiredTasks: string[] = [];

      for (const [taskId, tracker] of this.progressTrackers.entries()) {
        // 如果超过1小时没有更新，认为任务可能已经结束
        if (now - tracker.lastUpdate.getTime() > 60 * 60 * 1000) {
          expiredTasks.push(taskId);
        }
      }

      // 清理过期的跟踪器
      expiredTasks.forEach(taskId => {
        this.progressTrackers.delete(taskId);
        this.emit('monitoringExpired', { taskId });
      });

      // 清理过期的日志（30天前的日志）
      if (TaskLogModel && TaskLogModel.deleteOldLogs) {
        TaskLogModel.deleteOldLogs(30).catch(error => {
          console.error('清理过期日志失败:', error);
        });
      }

    }, 60 * 60 * 1000); // 每小时执行一次清理
  }

  /**
   * 停止清理定时器（主要用于测试）
   */
  stopCleanupTimer(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }
  }

  /**
   * 重置任务监控状态
   */
  async resetTaskMonitoring(taskId: string): Promise<void> {
    const task = await TaskModel.findById(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    // 停止当前监控
    await this.stopMonitoring(taskId);

    // 重置任务状态
    await TaskModel.update(taskId, {
      progress: 0,
      collectedCount: 0,
      status: TaskStatus.PENDING
    });

    // 记录重置日志
    await TaskLogModel.logInfo(taskId, '任务监控状态已重置');

    this.emit('monitoringReset', { taskId });
  }

  /**
   * 获取系统监控概览
   */
  async getSystemOverview(): Promise<{
    totalMonitoredTasks: number;
    runningTasks: number;
    completedTasks: number;
    failedTasks: number;
    totalErrors: number;
    totalWarnings: number;
  }> {
    const monitoredTasks = this.getMonitoredTasks();
    const runningTasks = await TaskModel.findByStatus(TaskStatus.RUNNING);
    const completedTasks = await TaskModel.findByStatus(TaskStatus.COMPLETED);
    const failedTasks = await TaskModel.findByStatus(TaskStatus.FAILED);
    const logStats = await TaskLogModel.getLogStats();

    return {
      totalMonitoredTasks: monitoredTasks.length,
      runningTasks: runningTasks.length,
      completedTasks: completedTasks.length,
      failedTasks: failedTasks.length,
      totalErrors: logStats.error,
      totalWarnings: logStats.warning
    };
  }
}

// 创建单例实例
export const taskMonitorService = new TaskMonitorService();