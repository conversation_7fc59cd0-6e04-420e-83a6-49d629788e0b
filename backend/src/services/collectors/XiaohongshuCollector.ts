import { BaseCollector } from './BaseCollector';
import { 
  Platform, 
  CollectConfig, 
  RawContent, 
  CollectionContext,
  ContentType,
  ContentCategory 
} from '../../types';

/**
 * 小红书内容采集器
 * Handles content collection from Xiaohongshu (Little Red Book) platform
 * Supports mixed content types (text + images), topic extraction, and comprehensive metrics
 */
export class XiaohongshuCollector extends BaseCollector {
  private readonly baseUrl = 'https://www.xiaohongshu.com';
  private readonly apiBaseUrl = 'https://edith.xiaohongshu.com/api';
  private readonly searchUrl = 'https://www.xiaohongshu.com/web_api/sns/v3/page/notes';
  
  // Psychology, growth, and relationship related keywords for content categorization
  private readonly categoryKeywords = {
    [ContentCategory.PSYCHOLOGY]: [
      '心理', '心理健康', '心理学', '情绪', '焦虑', '抑郁', '压力', '心态', 
      '自我认知', '心理咨询', '心理治疗', '冥想', '正念', '心理建设'
    ],
    [ContentCategory.GROWTH]: [
      '成长', '个人成长', '自我提升', '学习', '技能', '习惯', '目标', '规划',
      '时间管理', '效率', '读书', '思维', '认知', '能力', '进步', '改变'
    ],
    [ContentCategory.RELATIONSHIP]: [
      '关系', '人际关系', '恋爱', '婚姻', '友情', '亲情', '沟通', '相处',
      '情感', '爱情', '家庭', '社交', '交友', '伴侣', '夫妻', '亲子'
    ]
  };

  constructor() {
    super(Platform.XIAOHONGSHU);
  }

  /**
   * Collect content from Xiaohongshu based on keywords and filters
   */
  protected async collectRawContent(config: CollectConfig, context: CollectionContext): Promise<RawContent[]> {
    const rawContents: RawContent[] = [];
    
    try {
      for (const keyword of config.keywords) {
        const searchResults = await this.searchByKeyword(keyword, config.maxResults);
        rawContents.push(...searchResults);
        
        // Respect rate limits
        await this.sleep(1000);
      }

      // Remove duplicates based on content ID
      const uniqueContents = this.removeDuplicates(rawContents);
      
      return uniqueContents.slice(0, config.maxResults);
    } catch (error) {
      console.error('Error collecting from Xiaohongshu:', error);
      throw error;
    }
  }

  /**
   * Collect hot/trending content from Xiaohongshu
   */
  protected async collectHotContent(limit: number): Promise<RawContent[]> {
    try {
      // Simulate API call to get trending content
      // In real implementation, this would call Xiaohongshu's trending API
      const trendingContent = await this.fetchTrendingContent(limit);
      return trendingContent;
    } catch (error) {
      console.error('Error collecting hot content from Xiaohongshu:', error);
      throw error;
    }
  }

  /**
   * Check if Xiaohongshu platform is accessible
   */
  protected async checkPlatformAccess(): Promise<boolean> {
    try {
      // Simple connectivity check
      // In real implementation, this would verify API access or scraping capability
      return true; // Placeholder - implement actual access check
    } catch (error) {
      return false;
    }
  }

  /**
   * Parse Xiaohongshu content from HTML or API response
   * Handles mixed content types (text + images) and extracts comprehensive data
   */
  protected async parseContent(rawHtml: string, url: string): Promise<RawContent | null> {
    try {
      // In a real implementation, this would parse actual Xiaohongshu HTML/JSON
      // For now, we simulate the parsing with enhanced mock data
      
      const contentData = this.simulateContentParsing(rawHtml, url);
      if (!contentData) return null;

      // Extract and process content information
      const processedContent = await this.processXiaohongshuContent(contentData, url);
      
      return processedContent;
    } catch (error) {
      console.error('Error parsing Xiaohongshu content:', error);
      return null;
    }
  }

  /**
   * Simulate content parsing from Xiaohongshu HTML/API response
   * In real implementation, this would use actual parsing logic
   */
  private simulateContentParsing(rawHtml: string, url: string): any {
    // Extract note ID from URL
    const noteIdMatch = url.match(/\/discovery\/item\/([a-zA-Z0-9]+)/);
    const noteId = noteIdMatch ? noteIdMatch[1] : this.generateMockId();

    // Simulate parsed data structure similar to actual Xiaohongshu API response
    return {
      note_id: noteId,
      title: this.generateRealisticTitle(),
      desc: this.generateRealisticDescription(),
      user: {
        user_id: `user_${this.generateMockId()}`,
        nickname: this.generateRealisticUsername(),
        avatar: `https://sns-avatar-qc.xhscdn.com/avatar/${this.generateMockId()}.jpg`,
        desc: this.generateUserDescription(),
        fan_count: Math.floor(Math.random() * 50000) + 100
      },
      interact_info: {
        liked_count: Math.floor(Math.random() * 2000) + 10,
        collected_count: Math.floor(Math.random() * 500) + 5,
        comment_count: Math.floor(Math.random() * 200) + 1,
        share_count: Math.floor(Math.random() * 100)
      },
      image_list: this.generateImageList(),
      tag_list: this.generateRealisticTags(),
      time: Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000), // Within last 30 days
      type: this.determineContentType()
    };
  }

  /**
   * Process parsed Xiaohongshu content into RawContent format
   */
  private async processXiaohongshuContent(contentData: any, url: string): Promise<RawContent> {
    // Extract comprehensive author information
    const authorInfo = this.extractComprehensiveAuthorInfo(contentData);
    
    // Parse mixed content (text + images/video)
    const { contentType, mediaUrls, textContent } = this.parseMixedContent(contentData);
    
    // Extract and clean text content
    const { title, description } = this.extractTextContent(contentData);
    
    // Extract and process tags with enhanced logic
    const tags = this.extractAndProcessTags(contentData, textContent);
    
    // Extract topic themes with enhanced processing
    const topics = this.extractTopicThemesEnhanced(textContent, tags);
    
    // Process interaction metrics with better accuracy
    const metrics = this.processInteractionMetrics(contentData.interact_info);

    const rawContent: RawContent = {
      platform: Platform.XIAOHONGSHU,
      authorId: authorInfo.id,
      authorName: authorInfo.name,
      authorFollowers: authorInfo.followers,
      contentId: contentData.note_id,
      contentType,
      title,
      description,
      contentText: textContent,
      mediaUrls,
      tags: [...new Set([...tags, ...topics])], // Combine and deduplicate tags and topics
      metrics,
      originalUrl: url,
      publishedAt: new Date(contentData.time),
      collectedAt: new Date()
    };

    return rawContent;
  }

  /**
   * Search for content by keyword with enhanced processing
   */
  private async searchByKeyword(keyword: string, maxResults: number): Promise<RawContent[]> {
    try {
      // In real implementation, this would make actual API calls to Xiaohongshu search
      // For now, we simulate the search with enhanced mock data processing
      
      const mockResults: RawContent[] = [];
      const resultCount = Math.min(maxResults, 15); // Increased for better variety

      for (let i = 0; i < resultCount; i++) {
        // Simulate API response data
        const mockApiData = this.generateMockApiResponse(keyword, i);
        
        // Process through the same pipeline as real content
        const processedContent = await this.processXiaohongshuContent(
          mockApiData, 
          `${this.baseUrl}/discovery/item/${mockApiData.note_id}`
        );

        // Validate content quality
        if (this.validateXiaohongshuContent(processedContent)) {
          mockResults.push(processedContent);
        }
      }

      return mockResults;
    } catch (error) {
      console.error(`Error searching for keyword "${keyword}":`, error);
      return [];
    }
  }

  /**
   * Generate mock API response data for testing
   */
  private generateMockApiResponse(keyword: string, index: number): any {
    const contentVariations = this.getContentVariationsByKeyword(keyword);
    const variation = contentVariations[index % contentVariations.length];
    
    return {
      note_id: `${keyword}_${index}_${this.generateMockId()}`,
      title: variation.title,
      desc: variation.description,
      user: {
        user_id: `user_${keyword}_${index}`,
        nickname: this.generateContextualUsername(keyword),
        avatar: `https://sns-avatar-qc.xhscdn.com/avatar/${this.generateMockId()}.jpg`,
        desc: this.generateContextualUserDesc(keyword),
        fan_count: Math.floor(Math.random() * 30000) + 500
      },
      interact_info: {
        liked_count: Math.floor(Math.random() * 1500) + 20,
        collected_count: Math.floor(Math.random() * 300) + 10,
        comment_count: Math.floor(Math.random() * 150) + 5,
        share_count: Math.floor(Math.random() * 80) + 1
      },
      image_list: this.generateContextualImages(keyword),
      tag_list: this.generateContextualTags(keyword),
      time: Date.now() - Math.floor(Math.random() * 45 * 24 * 60 * 60 * 1000), // Within last 45 days
      type: this.determineContentType()
    };
  }

  /**
   * Get content variations based on keyword context
   */
  private getContentVariationsByKeyword(keyword: string): Array<{title: string, description: string}> {
    const variations: Record<string, Array<{title: string, description: string}>> = {
      '心理健康': [
        {
          title: '分享几个简单有效的心理调节方法',
          description: '最近学到了一些心理学技巧，对缓解焦虑和压力很有帮助，分享给需要的朋友们。'
        },
        {
          title: '如何建立健康的心理边界',
          description: '心理边界对我们的心理健康非常重要，学会说不，保护自己的心理空间。'
        },
        {
          title: '情绪管理的5个实用技巧',
          description: '情绪管理是一门必修课，这些方法帮助我更好地理解和处理自己的情绪。'
        }
      ],
      '个人成长': [
        {
          title: '30天养成一个好习惯的方法',
          description: '习惯的力量真的很强大，分享我成功养成好习惯的具体方法和心得体会。'
        },
        {
          title: '如何制定并坚持个人成长计划',
          description: '成长需要有计划有方法，这套系统帮助我在各个方面都有了明显的提升。'
        },
        {
          title: '读书如何真正改变一个人',
          description: '读书不只是获取信息，更重要的是如何将知识转化为智慧和行动力。'
        }
      ],
      '人际关系': [
        {
          title: '高情商的人都在用的沟通技巧',
          description: '沟通是人际关系的核心，掌握这些技巧让我的人际关系质量大大提升。'
        },
        {
          title: '如何处理职场中的复杂关系',
          description: '职场关系确实复杂，但掌握了这些原则，可以让工作环境更加和谐。'
        },
        {
          title: '建立深度友谊的几个要点',
          description: '真正的友谊需要用心经营，分享一些我在维护友谊方面的经验和感悟。'
        }
      ]
    };

    // Return variations for the keyword, or default variations
    return variations[keyword] || [
      {
        title: `关于${keyword}的一些思考和分享`,
        description: `最近在${keyword}方面有了一些新的认知和体验，想和大家分享交流。`
      },
      {
        title: `${keyword}让我学到的重要一课`,
        description: `通过${keyword}的学习和实践，我获得了很多宝贵的经验和启发。`
      }
    ];
  }

  /**
   * Generate contextual username based on keyword
   */
  private generateContextualUsername(keyword: string): string {
    const contextualPrefixes: Record<string, string[]> = {
      '心理健康': ['心理小助手', '心灵导师', '情绪管理师', '心理咨询师'],
      '个人成长': ['成长日记', '自我提升', '学习达人', '进步青年'],
      '人际关系': ['沟通专家', '关系达人', '社交高手', '情感导师']
    };

    const prefixes = contextualPrefixes[keyword] || ['生活分享', '经验总结', '思考记录'];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = Math.random() > 0.5 ? Math.floor(Math.random() * 99) + 1 : '';
    
    return prefix + suffix;
  }

  /**
   * Generate contextual user description
   */
  private generateContextualUserDesc(keyword: string): string {
    const descriptions: Record<string, string[]> = {
      '心理健康': ['专注心理健康科普', '分享心理调节方法', '心理咨询师在线'],
      '个人成长': ['记录成长路上的点滴', '分享学习方法和经验', '终身学习践行者'],
      '人际关系': ['专注人际关系研究', '分享沟通技巧', '情感咨询师']
    };

    const contextualDescs = descriptions[keyword] || ['分享生活感悟', '记录思考过程'];
    return contextualDescs[Math.floor(Math.random() * contextualDescs.length)];
  }

  /**
   * Generate contextual images based on keyword
   */
  private generateContextualImages(keyword: string): Array<{url: string, width: number, height: number}> {
    // Different content types have different image patterns
    const imageCount = keyword.includes('心理') ? 
      Math.floor(Math.random() * 3) + 1 : // Psychology content: 1-3 images
      Math.floor(Math.random() * 6) + 2;   // Other content: 2-7 images
    
    const images = [];
    for (let i = 0; i < imageCount; i++) {
      images.push({
        url: `https://sns-webpic-qc.xhscdn.com/${keyword}_${this.generateMockId()}.jpg`,
        width: 720 + Math.floor(Math.random() * 200),
        height: 960 + Math.floor(Math.random() * 200)
      });
    }
    
    return images;
  }

  /**
   * Generate contextual tags based on keyword
   */
  private generateContextualTags(keyword: string): Array<{name: string, type: string}> {
    const contextualTags: Record<string, string[]> = {
      '心理健康': ['心理学', '情绪管理', '心态调节', '自我认知', '心理咨询', '正念冥想'],
      '个人成长': ['自我提升', '学习方法', '时间管理', '目标规划', '习惯养成', '思维模式'],
      '人际关系': ['沟通技巧', '情商提升', '社交能力', '恋爱心理', '职场关系', '家庭关系']
    };

    const baseTags = contextualTags[keyword] || ['生活感悟', '经验分享', '个人思考'];
    const generalTags = ['干货分享', '实用技巧', '正能量', '成长笔记'];
    
    const allTags = [...baseTags, ...generalTags];
    const selectedTags = [];
    const tagCount = Math.floor(Math.random() * 4) + 3; // 3-6 tags
    
    // Always include the main keyword as a tag
    selectedTags.push({ name: keyword, type: 'topic' });
    
    for (let i = 1; i < tagCount; i++) {
      const tag = allTags[Math.floor(Math.random() * allTags.length)];
      if (!selectedTags.find(t => t.name === tag)) {
        selectedTags.push({
          name: tag,
          type: 'topic'
        });
      }
    }
    
    return selectedTags;
  }

  /**
   * Fetch trending content from Xiaohongshu
   */
  private async fetchTrendingContent(limit: number): Promise<RawContent[]> {
    try {
      // Placeholder implementation for trending content
      const trendingTopics = ['心理健康', '个人成长', '情感关系', '生活方式', '职场发展'];
      const mockResults: RawContent[] = [];

      for (let i = 0; i < Math.min(limit, 20); i++) {
        const topic = trendingTopics[i % trendingTopics.length];
        
        const mockContent: RawContent = {
          platform: Platform.XIAOHONGSHU,
          authorId: `trending_author_${i}`,
          authorName: `热门博主${i}`,
          authorFollowers: Math.floor(Math.random() * 50000) + 1000,
          contentId: `trending_content_${i}`,
          contentType: this.getRandomContentType(),
          title: `【热门】${topic}相关内容`,
          description: `这是关于${topic}的热门内容，获得了很多用户的关注和互动。`,
          contentText: `热门${topic}内容的详细文本，包含了深度的分析和实用的建议。`,
          mediaUrls: this.generateMockMediaUrls(),
          tags: [topic, '热门', '推荐'],
          metrics: {
            likes: Math.floor(Math.random() * 5000) + 500,
            comments: Math.floor(Math.random() * 500) + 50,
            shares: Math.floor(Math.random() * 200) + 20,
            views: Math.floor(Math.random() * 20000) + 2000,
            favorites: Math.floor(Math.random() * 1000) + 100
          },
          originalUrl: `${this.baseUrl}/discovery/item/${this.generateMockId()}`,
          publishedAt: this.generateRandomDate(7), // Within last 7 days
          collectedAt: new Date()
        };

        mockResults.push(mockContent);
      }

      return mockResults;
    } catch (error) {
      console.error('Error fetching trending content:', error);
      return [];
    }
  }

  /**
   * Remove duplicate content based on content ID
   */
  private removeDuplicates(contents: RawContent[]): RawContent[] {
    const seen = new Set<string>();
    return contents.filter(content => {
      if (seen.has(content.contentId)) {
        return false;
      }
      seen.add(content.contentId);
      return true;
    });
  }

  /**
   * Generate random content type for mock data
   */
  private getRandomContentType(): ContentType {
    const types = [ContentType.TEXT, ContentType.IMAGE, ContentType.MIXED];
    return types[Math.floor(Math.random() * types.length)];
  }

  /**
   * Generate mock media URLs
   */
  private generateMockMediaUrls(): string[] {
    const count = Math.floor(Math.random() * 3) + 1;
    const urls: string[] = [];
    
    for (let i = 0; i < count; i++) {
      urls.push(`https://ci.xiaohongshu.com/mock_image_${this.generateMockId()}.jpg`);
    }
    
    return urls;
  }

  /**
   * Generate mock tags based on keyword
   */
  private generateMockTags(keyword: string): string[] {
    const baseTags = ['心理', '成长', '生活', '分享', '经验'];
    const tags = [keyword, ...baseTags.slice(0, Math.floor(Math.random() * 3) + 2)];
    return [...new Set(tags)]; // Remove duplicates
  }

  /**
   * Generate mock ID
   */
  private generateMockId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  /**
   * Generate random date within specified days
   */
  private generateRandomDate(daysBack: number = 30): Date {
    const now = new Date();
    const randomDays = Math.floor(Math.random() * daysBack);
    const randomHours = Math.floor(Math.random() * 24);
    const randomMinutes = Math.floor(Math.random() * 60);
    
    const date = new Date(now);
    date.setDate(date.getDate() - randomDays);
    date.setHours(randomHours, randomMinutes, 0, 0);
    
    return date;
  }

  /**
   * Extract topics and hashtags from Xiaohongshu content
   */
  private extractTopicsAndTags(text: string): string[] {
    const topics: string[] = [];
    
    // Extract hashtags (# format)
    const hashtagRegex = /#([^\s#]+)/g;
    let match;
    while ((match = hashtagRegex.exec(text)) !== null) {
      topics.push(match[1]);
    }
    
    // Extract topics in brackets
    const bracketRegex = /【([^】]+)】/g;
    while ((match = bracketRegex.exec(text)) !== null) {
      topics.push(match[1]);
    }
    
    return [...new Set(topics)];
  }

  /**
   * Determine if content is verified author
   */
  private isVerifiedAuthor(authorInfo: any): boolean {
    // Placeholder logic for determining verified status
    // In real implementation, this would check actual verification badges
    return authorInfo.followers > 10000 || authorInfo.hasVerificationBadge;
  }

  /**
   * Extract author information from content
   */
  private extractAuthorInfo(contentData: any): {
    id: string;
    name: string;
    followers: number;
    isVerified: boolean;
    avatarUrl?: string;
    description?: string;
  } {
    return {
      id: contentData.authorId || 'unknown',
      name: contentData.authorName || '未知用户',
      followers: contentData.authorFollowers || 0,
      isVerified: this.isVerifiedAuthor(contentData),
      avatarUrl: contentData.authorAvatar,
      description: contentData.authorDescription
    };
  }

  /**
   * Generate realistic title for mock content
   */
  private generateRealisticTitle(): string {
    const titleTemplates = [
      '分享一个改变我人生的心理学技巧',
      '如何在30天内建立一个好习惯',
      '关于情绪管理，我想说的几个要点',
      '个人成长路上的5个重要认知',
      '和大家聊聊最近的心理状态',
      '这样做让我的人际关系变得更好',
      '从焦虑到平静，我的心理调节方法',
      '成长就是不断地认识自己',
      '分享一些关于爱情的思考',
      '如何处理工作中的人际关系'
    ];
    
    return titleTemplates[Math.floor(Math.random() * titleTemplates.length)];
  }

  /**
   * Generate realistic description for mock content
   */
  private generateRealisticDescription(): string {
    const descriptions = [
      '最近在读心理学相关的书籍，有一些感悟想和大家分享。希望对正在迷茫中的朋友有所帮助。',
      '个人成长是一个持续的过程，每个人都有自己的节奏。分享一些我的经验和思考。',
      '情绪管理真的很重要，学会和自己的情绪相处是一门必修课。',
      '人际关系中的沟通技巧，这些方法让我受益匪浅。',
      '关于自我认知和个人发展的一些思考，希望能给大家一些启发。'
    ];
    
    return descriptions[Math.floor(Math.random() * descriptions.length)];
  }

  /**
   * Generate realistic username
   */
  private generateRealisticUsername(): string {
    const prefixes = ['心理小助手', '成长日记', '情感导师', '生活感悟', '心灵成长', '自我提升'];
    const suffixes = ['', '01', '小姐姐', '分享', '笔记', '日常'];
    
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
    
    return prefix + suffix;
  }

  /**
   * Generate user description
   */
  private generateUserDescription(): string {
    const descriptions = [
      '分享心理学知识和个人成长经验',
      '专注情感咨询和人际关系',
      '记录生活中的感悟和思考',
      '心理健康倡导者',
      '个人成长路上的同行者'
    ];
    
    return descriptions[Math.floor(Math.random() * descriptions.length)];
  }

  /**
   * Generate realistic image list for mixed content
   */
  private generateImageList(): Array<{url: string, width: number, height: number}> {
    const imageCount = Math.floor(Math.random() * 6) + 1; // 1-6 images
    const images = [];
    
    for (let i = 0; i < imageCount; i++) {
      images.push({
        url: `https://sns-webpic-qc.xhscdn.com/${this.generateMockId()}.jpg`,
        width: 720 + Math.floor(Math.random() * 200),
        height: 960 + Math.floor(Math.random() * 200)
      });
    }
    
    return images;
  }

  /**
   * Generate realistic tags
   */
  private generateRealisticTags(): Array<{name: string, type: string}> {
    const psychologyTags = ['心理健康', '情绪管理', '心理学', '自我认知', '心态调节'];
    const growthTags = ['个人成长', '自我提升', '学习方法', '时间管理', '目标规划'];
    const relationshipTags = ['人际关系', '沟通技巧', '情感咨询', '恋爱心理', '社交技能'];
    const generalTags = ['生活感悟', '日常分享', '经验总结', '思考记录', '正能量'];
    
    const allTags = [...psychologyTags, ...growthTags, ...relationshipTags, ...generalTags];
    const selectedTags = [];
    const tagCount = Math.floor(Math.random() * 5) + 2; // 2-6 tags
    
    for (let i = 0; i < tagCount; i++) {
      const tag = allTags[Math.floor(Math.random() * allTags.length)];
      if (!selectedTags.find(t => t.name === tag)) {
        selectedTags.push({
          name: tag,
          type: 'topic'
        });
      }
    }
    
    return selectedTags;
  }

  /**
   * Determine content type from parsed data
   */
  private determineContentTypeFromData(contentData: any): ContentType {
    const hasImages = contentData.image_list && contentData.image_list.length > 0;
    const hasText = contentData.desc && contentData.desc.trim().length > 0;
    
    if (hasImages && hasText) {
      return ContentType.MIXED;
    } else if (hasImages) {
      return ContentType.IMAGE;
    } else {
      return ContentType.TEXT;
    }
  }

  /**
   * Extract text content from parsed data
   */
  private extractTextContent(contentData: any): {
    title: string;
    description: string;
    fullText: string;
  } {
    const title = contentData.title || '';
    const description = contentData.desc || '';
    const fullText = [title, description].filter(Boolean).join('\n\n');
    
    return { title, description, fullText };
  }

  /**
   * Extract media URLs from parsed data
   */
  private extractMediaUrls(contentData: any): string[] {
    if (!contentData.image_list || !Array.isArray(contentData.image_list)) {
      return [];
    }
    
    return contentData.image_list.map((img: any) => img.url).filter(Boolean);
  }

  /**
   * Extract and process tags from content data
   */
  private extractAndProcessTags(contentData: any, fullText: string): string[] {
    const tags = new Set<string>();
    
    // Add tags from tag_list
    if (contentData.tag_list && Array.isArray(contentData.tag_list)) {
      contentData.tag_list.forEach((tag: any) => {
        if (tag.name) {
          tags.add(tag.name);
        }
      });
    }
    
    // Extract hashtags from text
    const hashtagMatches = fullText.match(/#([^\s#]+)/g);
    if (hashtagMatches) {
      hashtagMatches.forEach(hashtag => {
        tags.add(hashtag.substring(1)); // Remove # symbol
      });
    }
    
    // Extract topics in brackets
    const bracketMatches = fullText.match(/【([^】]+)】/g);
    if (bracketMatches) {
      bracketMatches.forEach(match => {
        const topic = match.substring(1, match.length - 1); // Remove brackets
        tags.add(topic);
      });
    }
    
    return Array.from(tags);
  }

  /**
   * Extract topic themes based on content analysis
   */
  private extractTopicThemes(fullText: string, existingTags: string[]): string[] {
    const themes = new Set<string>();
    const lowerText = fullText.toLowerCase();
    
    // Check for category-specific themes
    Object.entries(this.categoryKeywords).forEach(([category, keywords]) => {
      const matchCount = keywords.filter(keyword => 
        lowerText.includes(keyword.toLowerCase())
      ).length;
      
      // If content matches multiple keywords from a category, add theme
      if (matchCount >= 2) {
        switch (category) {
          case ContentCategory.PSYCHOLOGY:
            themes.add('心理健康');
            break;
          case ContentCategory.GROWTH:
            themes.add('个人成长');
            break;
          case ContentCategory.RELATIONSHIP:
            themes.add('人际关系');
            break;
        }
      }
    });
    
    // Extract common themes from existing tags
    const commonThemes = ['生活方式', '经验分享', '学习笔记', '感悟思考'];
    existingTags.forEach(tag => {
      if (tag.includes('分享') || tag.includes('经验')) {
        themes.add('经验分享');
      }
      if (tag.includes('学习') || tag.includes('方法')) {
        themes.add('学习笔记');
      }
      if (tag.includes('感悟') || tag.includes('思考')) {
        themes.add('感悟思考');
      }
    });
    
    return Array.from(themes);
  }

  /**
   * Estimate view count based on engagement metrics
   * Xiaohongshu doesn't always provide view counts, so we estimate
   */
  private estimateViewCount(interactInfo: any): number {
    const likes = interactInfo.liked_count || 0;
    const comments = interactInfo.comment_count || 0;
    const shares = interactInfo.share_count || 0;
    const favorites = interactInfo.collected_count || 0;
    
    // Estimate based on typical engagement rates (1-5% for Xiaohongshu)
    const totalEngagement = likes + comments + shares + favorites;
    const estimatedViews = Math.floor(totalEngagement / 0.03); // Assume 3% engagement rate
    
    // Ensure minimum view count
    return Math.max(estimatedViews, totalEngagement * 10);
  }

  /**
   * Categorize content based on keywords and themes
   */
  public categorizeContent(content: RawContent): ContentCategory {
    const searchText = [
      content.title,
      content.description,
      content.contentText,
      ...content.tags
    ].join(' ').toLowerCase();

    // Count matches for each category
    const categoryScores = {
      [ContentCategory.PSYCHOLOGY]: 0,
      [ContentCategory.GROWTH]: 0,
      [ContentCategory.RELATIONSHIP]: 0
    };

    Object.entries(this.categoryKeywords).forEach(([category, keywords]) => {
      keywords.forEach(keyword => {
        if (searchText.includes(keyword.toLowerCase())) {
          categoryScores[category as ContentCategory]++;
        }
      });
    });

    // Return category with highest score
    const maxScore = Math.max(...Object.values(categoryScores));
    if (maxScore === 0) return ContentCategory.OTHER;

    const topCategory = Object.entries(categoryScores).find(
      ([_, score]) => score === maxScore
    );

    return (topCategory?.[0] as ContentCategory) || ContentCategory.OTHER;
  }

  /**
   * Enhanced content validation for Xiaohongshu
   */
  public validateXiaohongshuContent(content: RawContent): boolean {
    // Basic validation
    if (!content.contentId || !content.authorId) return false;
    
    // Check for minimum content quality
    const hasText = content.title || content.description || content.contentText;
    const hasMedia = content.mediaUrls && content.mediaUrls.length > 0;
    
    if (!hasText && !hasMedia) return false;
    
    // Check for minimum engagement (avoid spam/low-quality content)
    const totalEngagement = (content.metrics.likes || 0) + 
                           (content.metrics.comments || 0) + 
                           (content.metrics.favorites || 0);
    
    if (totalEngagement < 1) return false;
    
    // Check for relevant tags (should have at least one meaningful tag)
    if (!content.tags || content.tags.length === 0) return false;
    
    return true;
  }

  /**
   * Override convertRawToContentItem to add Xiaohongshu-specific processing
   */
  protected async convertRawToContentItem(rawContent: RawContent): Promise<ContentItem | null> {
    try {
      // First validate the content
      if (!this.validateXiaohongshuContent(rawContent)) {
        return null;
      }

      // Use base class conversion
      const contentItem = await super.convertRawToContentItem(rawContent);
      if (!contentItem) return null;

      // Add Xiaohongshu-specific enhancements
      
      // Determine if author is verified (based on follower count and engagement)
      contentItem.author.isVerified = this.determineVerificationStatus(rawContent);
      
      // Add avatar URL if available
      if (rawContent.authorId) {
        contentItem.author.avatarUrl = `https://sns-avatar-qc.xhscdn.com/avatar/${rawContent.authorId}.jpg`;
      }

      // Categorize content based on keywords and themes
      contentItem.content.category = this.categorizeContent(rawContent);

      // Enhance tags with topic themes
      const additionalThemes = this.extractTopicThemes(
        rawContent.contentText || rawContent.description || rawContent.title || '',
        rawContent.tags
      );
      contentItem.content.tags = [...new Set([...rawContent.tags, ...additionalThemes])];

      return contentItem;
    } catch (error) {
      console.error('Error converting Xiaohongshu raw content:', error);
      return null;
    }
  }

  /**
   * Determine verification status based on follower count and engagement patterns
   */
  private determineVerificationStatus(rawContent: RawContent): boolean {
    const followers = rawContent.authorFollowers || 0;
    const totalEngagement = (rawContent.metrics.likes || 0) + 
                           (rawContent.metrics.comments || 0) + 
                           (rawContent.metrics.favorites || 0);
    
    // Consider verified if high follower count or high engagement
    return followers > 10000 || totalEngagement > 1000;
  }

  /**
   * Enhanced topic extraction with better Chinese text processing
   */
  private extractTopicThemesEnhanced(text: string, existingTags: string[]): string[] {
    const themes = new Set<string>();
    const lowerText = text.toLowerCase();
    
    // Check for category-specific themes with better matching
    Object.entries(this.categoryKeywords).forEach(([category, keywords]) => {
      let matchCount = 0;
      const matchedKeywords: string[] = [];
      
      keywords.forEach(keyword => {
        if (lowerText.includes(keyword.toLowerCase())) {
          matchCount++;
          matchedKeywords.push(keyword);
        }
      });
      
      // If content matches multiple keywords from a category, add theme
      if (matchCount >= 2) {
        switch (category) {
          case ContentCategory.PSYCHOLOGY:
            themes.add('心理健康');
            themes.add('情绪管理');
            break;
          case ContentCategory.GROWTH:
            themes.add('个人成长');
            themes.add('自我提升');
            break;
          case ContentCategory.RELATIONSHIP:
            themes.add('人际关系');
            themes.add('情感咨询');
            break;
        }
      } else if (matchCount === 1) {
        // Add the specific matched keyword as a theme
        matchedKeywords.forEach(keyword => themes.add(keyword));
      }
    });
    
    // Extract common themes from existing tags with better logic
    existingTags.forEach(tag => {
      if (tag.includes('分享') || tag.includes('经验') || tag.includes('心得')) {
        themes.add('经验分享');
      }
      if (tag.includes('学习') || tag.includes('方法') || tag.includes('技巧')) {
        themes.add('学习方法');
      }
      if (tag.includes('感悟') || tag.includes('思考') || tag.includes('反思')) {
        themes.add('生活感悟');
      }
      if (tag.includes('成长') || tag.includes('提升') || tag.includes('进步')) {
        themes.add('个人成长');
      }
    });
    
    return Array.from(themes);
  }

  /**
   * Enhanced mixed content parsing for Xiaohongshu
   */
  private parseMixedContent(contentData: any): {
    contentType: ContentType;
    mediaUrls: string[];
    textContent: string;
  } {
    const hasImages = contentData.image_list && contentData.image_list.length > 0;
    const hasVideo = contentData.video && contentData.video.url;
    const textContent = [contentData.title, contentData.desc].filter(Boolean).join('\n\n');
    const hasText = textContent.trim().length > 0;
    
    let contentType: ContentType;
    let mediaUrls: string[] = [];
    
    if (hasVideo) {
      contentType = hasText ? ContentType.MIXED : ContentType.VIDEO;
      mediaUrls.push(contentData.video.url);
    } else if (hasImages) {
      contentType = hasText ? ContentType.MIXED : ContentType.IMAGE;
      mediaUrls = contentData.image_list.map((img: any) => img.url).filter(Boolean);
    } else {
      contentType = ContentType.TEXT;
    }
    
    return { contentType, mediaUrls, textContent };
  }

  /**
   * Extract comprehensive author information
   */
  private extractComprehensiveAuthorInfo(contentData: any): {
    id: string;
    name: string;
    followers: number;
    description?: string;
    avatarUrl?: string;
  } {
    return {
      id: contentData.user?.user_id || contentData.authorId || this.generateMockId(),
      name: contentData.user?.nickname || contentData.authorName || '未知用户',
      followers: contentData.user?.fan_count || contentData.authorFollowers || 0,
      description: contentData.user?.desc || contentData.authorDescription,
      avatarUrl: contentData.user?.avatar || `https://sns-avatar-qc.xhscdn.com/avatar/${contentData.user?.user_id || 'default'}.jpg`
    };
  }

  /**
   * Process interaction metrics with better accuracy
   */
  private processInteractionMetrics(interactInfo: any): {
    likes: number;
    comments: number;
    shares: number;
    views: number;
    favorites: number;
  } {
    const likes = interactInfo?.liked_count || interactInfo?.likes || 0;
    const comments = interactInfo?.comment_count || interactInfo?.comments || 0;
    const shares = interactInfo?.share_count || interactInfo?.shares || 0;
    const favorites = interactInfo?.collected_count || interactInfo?.favorites || 0;
    
    // Estimate views if not provided (common for Xiaohongshu)
    let views = interactInfo?.view_count || interactInfo?.views || 0;
    if (views === 0) {
      // Estimate based on engagement (typical view-to-engagement ratio for Xiaohongshu is 20-50:1)
      const totalEngagement = likes + comments + shares + favorites;
      views = Math.floor(totalEngagement * (25 + Math.random() * 25)); // 25-50x multiplier
    }
    
    return { likes, comments, shares, views, favorites };
  }
}