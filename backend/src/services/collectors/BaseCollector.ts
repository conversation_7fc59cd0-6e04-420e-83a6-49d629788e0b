import { 
  Platform, 
  CollectConfig, 
  RawContent, 
  ContentItem, 
  CollectionError, 
  CollectionContext,
  FilterConfig,
  ContentType,
  MetricsInfo
} from '../../types';

/**
 * Abstract base class for all platform collectors
 * Provides common functionality and defines the interface for platform-specific implementations
 */
export abstract class BaseCollector {
  protected platform: Platform;
  protected maxRetries: number = 3;
  protected retryDelay: number = 1000; // milliseconds

  constructor(platform: Platform) {
    this.platform = platform;
  }

  /**
   * Main collection method - orchestrates the entire collection process
   */
  async collect(config: CollectConfig, context: CollectionContext): Promise<ContentItem[]> {
    try {
      // Validate configuration
      this.validateConfig(config);

      // Get raw content from platform
      const rawContents = await this.collectRawContent(config, context);

      // Process and validate content
      const processedContents = await this.processRawContent(rawContents, config);

      // Apply quality filters
      const filteredContents = this.applyQualityFilters(processedContents, config.filters);

      return filteredContents;
    } catch (error) {
      throw this.handleCollectionError(error, context);
    }
  }

  /**
   * Get hot/trending content from the platform
   */
  async getHotContent(limit: number = 50): Promise<ContentItem[]> {
    try {
      const rawContents = await this.collectHotContent(limit);
      return await this.processRawContent(rawContents, { 
        keywords: [], 
        filters: {}, 
        maxResults: limit, 
        platform: this.platform 
      });
    } catch (error) {
      throw this.handleCollectionError(error, {
        taskId: 'hot-content',
        platform: this.platform,
        keywords: [],
        attempt: 1,
        maxAttempts: 3
      });
    }
  }

  /**
   * Validate platform access and credentials
   */
  async validateAccess(): Promise<boolean> {
    try {
      return await this.checkPlatformAccess();
    } catch (error) {
      console.error(`Platform access validation failed for ${this.platform}:`, error);
      return false;
    }
  }

  // Abstract methods that must be implemented by platform-specific collectors

  /**
   * Platform-specific content collection implementation
   */
  protected abstract collectRawContent(config: CollectConfig, context: CollectionContext): Promise<RawContent[]>;

  /**
   * Platform-specific hot content collection
   */
  protected abstract collectHotContent(limit: number): Promise<RawContent[]>;

  /**
   * Platform-specific access validation
   */
  protected abstract checkPlatformAccess(): Promise<boolean>;

  /**
   * Platform-specific content parsing and extraction
   */
  protected abstract parseContent(rawHtml: string, url: string): Promise<RawContent | null>;

  // Common utility methods

  /**
   * Validate collection configuration
   */
  protected validateConfig(config: CollectConfig): void {
    if (!config.keywords || config.keywords.length === 0) {
      throw new Error('Keywords are required for content collection');
    }

    if (config.maxResults && config.maxResults <= 0) {
      throw new Error('Max results must be a positive number');
    }

    if (config.platform !== this.platform) {
      throw new Error(`Platform mismatch: expected ${this.platform}, got ${config.platform}`);
    }
  }

  /**
   * Process raw content into structured ContentItem format
   */
  protected async processRawContent(rawContents: RawContent[], config: CollectConfig): Promise<ContentItem[]> {
    const processedContents: ContentItem[] = [];

    for (const rawContent of rawContents) {
      try {
        const contentItem = await this.convertRawToContentItem(rawContent);
        if (contentItem) {
          processedContents.push(contentItem);
        }
      } catch (error) {
        console.error('Error processing raw content:', error);
        // Continue processing other items
      }
    }

    return processedContents;
  }

  /**
   * Convert raw content to structured ContentItem
   */
  protected async convertRawToContentItem(rawContent: RawContent): Promise<ContentItem | null> {
    try {
      // Generate unique ID for the content item
      const contentId = this.generateContentId(rawContent);

      // Process metrics
      const metrics: MetricsInfo = {
        likesCount: rawContent.metrics.likes || 0,
        commentsCount: rawContent.metrics.comments || 0,
        sharesCount: rawContent.metrics.shares || 0,
        viewsCount: rawContent.metrics.views || 0,
        favoritesCount: rawContent.metrics.favorites || 0,
        engagementRate: this.calculateEngagementRate(rawContent.metrics)
      };

      // Create ContentItem
      const contentItem: ContentItem = {
        id: contentId,
        taskId: '', // Will be set by the calling service
        platform: rawContent.platform,
        author: {
          id: rawContent.authorId,
          name: rawContent.authorName,
          followers: rawContent.authorFollowers || 0,
          isVerified: false, // Platform-specific logic should override this
          platform: rawContent.platform
        },
        content: {
          id: rawContent.contentId,
          title: rawContent.title,
          description: rawContent.description,
          contentText: rawContent.contentText,
          mediaUrls: rawContent.mediaUrls,
          tags: rawContent.tags,
          publishedAt: rawContent.publishedAt || rawContent.collectedAt,
          contentType: rawContent.contentType
        },
        metrics,
        originalUrl: rawContent.originalUrl,
        collectedAt: rawContent.collectedAt
      };

      return contentItem;
    } catch (error) {
      console.error('Error converting raw content to ContentItem:', error);
      return null;
    }
  }

  /**
   * Apply quality filters to content
   */
  protected applyQualityFilters(contents: ContentItem[], filters: FilterConfig): ContentItem[] {
    return contents.filter(content => {
      // Check minimum followers
      if (filters.minFollowers && content.author.followers < filters.minFollowers) {
        return false;
      }

      // Check minimum likes
      if (filters.minLikes && content.metrics.likesCount < filters.minLikes) {
        return false;
      }

      // Check minimum comments
      if (filters.minComments && content.metrics.commentsCount < filters.minComments) {
        return false;
      }

      // Check minimum views
      if (filters.minViews && content.metrics.viewsCount < filters.minViews) {
        return false;
      }

      // Check content types
      if (filters.contentTypes && filters.contentTypes.length > 0) {
        if (!filters.contentTypes.includes(content.content.contentType)) {
          return false;
        }
      }

      // Check categories
      if (filters.categories && filters.categories.length > 0 && content.content.category) {
        if (!filters.categories.includes(content.content.category)) {
          return false;
        }
      }

      // Check time range
      if (filters.timeRange) {
        const publishedAt = content.content.publishedAt;
        if (publishedAt < filters.timeRange.startDate || publishedAt > filters.timeRange.endDate) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Calculate engagement rate from metrics
   */
  protected calculateEngagementRate(metrics: any): number {
    const totalEngagement = (metrics.likes || 0) + (metrics.comments || 0) + (metrics.shares || 0);
    const views = metrics.views || 0;
    
    if (views === 0) return 0;
    
    return Math.round((totalEngagement / views) * 10000) / 10000; // 4 decimal places
  }

  /**
   * Generate unique content ID
   */
  protected generateContentId(rawContent: RawContent): string {
    const timestamp = Date.now();
    const platformPrefix = rawContent.platform.substring(0, 3).toUpperCase();
    const contentHash = this.simpleHash(rawContent.contentId + rawContent.authorId);
    return `${platformPrefix}_${timestamp}_${contentHash}`;
  }

  /**
   * Simple hash function for generating IDs
   */
  protected simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Handle collection errors with retry logic
   */
  protected handleCollectionError(error: any, context: CollectionContext): CollectionError {
    let errorType: CollectionError['type'] = 'UNKNOWN';
    let retryable = false;
    let retryAfter: number | undefined;

    // Classify error types
    if (error.message?.includes('rate limit') || error.status === 429) {
      errorType = 'RATE_LIMIT';
      retryable = true;
      retryAfter = 60000; // 1 minute
    } else if (error.message?.includes('blocked') || error.message?.includes('captcha')) {
      errorType = 'ANTI_CRAWLER';
      retryable = true;
      retryAfter = 300000; // 5 minutes
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorType = 'NETWORK_ERROR';
      retryable = true;
      retryAfter = 5000; // 5 seconds
    } else if (error.message?.includes('parse') || error.message?.includes('invalid')) {
      errorType = 'PARSE_ERROR';
      retryable = false;
    } else if (error.status === 401 || error.status === 403) {
      errorType = 'AUTH_ERROR';
      retryable = false;
    }

    return {
      type: errorType,
      message: error.message || 'Unknown error occurred',
      platform: context.platform,
      retryable,
      retryAfter
    };
  }

  /**
   * Implement retry logic with exponential backoff
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    context: CollectionContext,
    maxRetries: number = this.maxRetries
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        const collectionError = this.handleCollectionError(error, { ...context, attempt });
        
        if (!collectionError.retryable || attempt === maxRetries) {
          throw collectionError;
        }

        // Wait before retry with exponential backoff
        const delay = collectionError.retryAfter || (this.retryDelay * Math.pow(2, attempt - 1));
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * Sleep utility for delays
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Extract keywords from text content
   */
  protected extractKeywords(text: string): string[] {
    if (!text) return [];

    // Simple keyword extraction - can be enhanced with NLP libraries
    const words = text
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, '') // Keep alphanumeric and Chinese characters
      .split(/\s+/)
      .filter(word => word.length > 1);

    // Remove duplicates and return
    return [...new Set(words)];
  }

  /**
   * Check if content matches keywords
   */
  protected matchesKeywords(content: RawContent, keywords: string[]): boolean {
    if (!keywords || keywords.length === 0) return true;

    const searchText = [
      content.title,
      content.description,
      content.contentText,
      ...content.tags
    ].join(' ').toLowerCase();

    return keywords.some(keyword => 
      searchText.includes(keyword.toLowerCase())
    );
  }
}