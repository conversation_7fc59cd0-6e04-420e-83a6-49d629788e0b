// Export all collector classes
export { <PERSON><PERSON>ollector } from './BaseCollector';
export { <PERSON><PERSON><PERSON><PERSON>ollector } from './XiaohongshuCollector';
export { DouyinCollector } from './DouyinCollector';

// Collector factory for creating platform-specific collectors
import { Platform } from '../../types';
import { BaseCollector } from './BaseCollector';
import { <PERSON><PERSON>shuCollector } from './XiaohongshuCollector';
import { <PERSON>uyinCollector } from './DouyinCollector';

export class CollectorFactory {
  /**
   * Create a collector instance for the specified platform
   */
  static createCollector(platform: Platform): BaseCollector {
    switch (platform) {
      case Platform.XIAOHONGSHU:
        return new <PERSON><PERSON>shuCollector();
      
      case Platform.DOUYIN:
        return new DouyinCollector();
      
      case Platform.WECHAT_ARTICLE:
      case Platform.WECHAT_VIDEO:
        // WeChat collectors will be implemented in future tasks
        throw new Error(`WeChat platform collectors not yet implemented: ${platform}`);
      
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  /**
   * Get all supported platforms
   */
  static getSupportedPlatforms(): Platform[] {
    return [
      Platform.XIAOHONGSHU,
      Platform.DOUYIN
      // WeChat platforms will be added when implemented
    ];
  }

  /**
   * Check if a platform is supported
   */
  static isPlatformSupported(platform: Platform): boolean {
    return this.getSupportedPlatforms().includes(platform);
  }
}