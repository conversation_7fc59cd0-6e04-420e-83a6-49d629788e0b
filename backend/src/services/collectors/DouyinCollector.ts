import { BaseCollector } from './BaseCollector';
import { 
  Platform, 
  CollectConfig, 
  RawContent, 
  CollectionContext,
  ContentType 
} from '../../types';

/**
 * 抖音内容采集器
 * Handles content collection from Do<PERSON>in (TikTok China) platform
 */
export class DouyinCollector extends BaseCollector {
  private readonly baseUrl = 'https://www.douyin.com';
  private readonly apiBaseUrl = 'https://www.douyin.com/aweme/v1';

  constructor() {
    super(Platform.DOUYIN);
  }

  /**
   * Collect content from Douyin based on keywords and filters
   */
  protected async collectRawContent(config: CollectConfig, context: CollectionContext): Promise<RawContent[]> {
    const rawContents: RawContent[] = [];
    
    try {
      for (const keyword of config.keywords) {
        const searchResults = await this.searchByKeyword(keyword, config.maxResults);
        rawContents.push(...searchResults);
        
        // Respect rate limits - <PERSON><PERSON><PERSON> has stricter limits
        await this.sleep(2000);
      }

      // Remove duplicates based on content ID
      const uniqueContents = this.removeDuplicates(rawContents);
      
      return uniqueContents.slice(0, config.maxResults);
    } catch (error) {
      console.error('Error collecting from Douyin:', error);
      throw error;
    }
  }

  /**
   * Collect hot/trending content from Douyin
   */
  protected async collectHotContent(limit: number): Promise<RawContent[]> {
    try {
      // Simulate API call to get trending videos
      // In real implementation, this would call Douyin's trending API
      const trendingContent = await this.fetchTrendingVideos(limit);
      return trendingContent;
    } catch (error) {
      console.error('Error collecting hot content from Douyin:', error);
      throw error;
    }
  }

  /**
   * Check if Douyin platform is accessible
   */
  protected async checkPlatformAccess(): Promise<boolean> {
    try {
      // Simple connectivity check
      // In real implementation, this would verify API access or scraping capability
      return true; // Placeholder - implement actual access check
    } catch (error) {
      return false;
    }
  }

  /**
   * Parse Douyin content from HTML or API response
   */
  protected async parseContent(rawHtml: string, url: string): Promise<RawContent | null> {
    try {
      // This is a placeholder implementation
      // In real implementation, this would parse actual Douyin HTML/JSON
      
      const mockContent: RawContent = {
        platform: Platform.DOUYIN,
        authorId: 'mock_douyin_author',
        authorName: 'Mock Douyin Creator',
        authorFollowers: 5000,
        contentId: 'mock_douyin_video',
        contentType: ContentType.VIDEO,
        title: 'Mock Douyin Video',
        description: 'This is a mock video description for testing',
        contentText: 'Mock video content with hashtags',
        mediaUrls: ['https://example.com/video1.mp4'],
        tags: ['心理健康', '成长', '正能量'],
        metrics: {
          likes: 500,
          comments: 50,
          shares: 25,
          views: 10000,
          favorites: 100
        },
        originalUrl: url,
        publishedAt: new Date(),
        collectedAt: new Date()
      };

      return mockContent;
    } catch (error) {
      console.error('Error parsing Douyin content:', error);
      return null;
    }
  }

  /**
   * Search for videos by keyword
   */
  private async searchByKeyword(keyword: string, maxResults: number): Promise<RawContent[]> {
    try {
      // Placeholder implementation
      // In real implementation, this would make actual API calls or web scraping
      
      const mockResults: RawContent[] = [];
      const resultCount = Math.min(maxResults, 15); // Limit for mock data

      for (let i = 0; i < resultCount; i++) {
        const mockContent: RawContent = {
          platform: Platform.DOUYIN,
          authorId: `dy_author_${i}`,
          authorName: `抖音创作者${i}`,
          authorFollowers: Math.floor(Math.random() * 100000) + 1000,
          contentId: `dy_video_${keyword}_${i}`,
          contentType: ContentType.VIDEO,
          title: `${keyword}相关视频 - ${i}`,
          description: `这是一个关于${keyword}的短视频，内容丰富有趣，值得观看。`,
          contentText: `#${keyword} 分享一些关于${keyword}的心得体会，希望对大家有帮助！`,
          mediaUrls: [this.generateMockVideoUrl()],
          tags: this.generateMockHashtags(keyword),
          metrics: {
            likes: Math.floor(Math.random() * 10000) + 100,
            comments: Math.floor(Math.random() * 1000) + 10,
            shares: Math.floor(Math.random() * 500) + 5,
            views: Math.floor(Math.random() * 100000) + 1000,
            favorites: Math.floor(Math.random() * 2000) + 50
          },
          originalUrl: `${this.baseUrl}/video/${this.generateMockVideoId()}`,
          publishedAt: this.generateRandomDate(),
          collectedAt: new Date()
        };

        mockResults.push(mockContent);
      }

      return mockResults;
    } catch (error) {
      console.error(`Error searching for keyword "${keyword}":`, error);
      return [];
    }
  }

  /**
   * Fetch trending videos from Douyin
   */
  private async fetchTrendingVideos(limit: number): Promise<RawContent[]> {
    try {
      // Placeholder implementation for trending videos
      const trendingHashtags = ['心理健康', '个人成长', '情感治愈', '生活感悟', '正能量'];
      const mockResults: RawContent[] = [];

      for (let i = 0; i < Math.min(limit, 25); i++) {
        const hashtag = trendingHashtags[i % trendingHashtags.length];
        
        const mockContent: RawContent = {
          platform: Platform.DOUYIN,
          authorId: `trending_creator_${i}`,
          authorName: `热门创作者${i}`,
          authorFollowers: Math.floor(Math.random() * 500000) + 10000,
          contentId: `trending_video_${i}`,
          contentType: ContentType.VIDEO,
          title: `【热门】${hashtag}主题视频`,
          description: `这是一个关于${hashtag}的热门视频，获得了大量用户的喜爱和分享。`,
          contentText: `#${hashtag} #热门推荐 分享${hashtag}相关的内容，希望能给大家带来正能量！`,
          mediaUrls: [this.generateMockVideoUrl()],
          tags: [hashtag, '热门', '推荐', '正能量'],
          metrics: {
            likes: Math.floor(Math.random() * 50000) + 5000,
            comments: Math.floor(Math.random() * 5000) + 500,
            shares: Math.floor(Math.random() * 2000) + 200,
            views: Math.floor(Math.random() * 500000) + 50000,
            favorites: Math.floor(Math.random() * 10000) + 1000
          },
          originalUrl: `${this.baseUrl}/video/${this.generateMockVideoId()}`,
          publishedAt: this.generateRandomDate(3), // Within last 3 days for trending
          collectedAt: new Date()
        };

        mockResults.push(mockContent);
      }

      return mockResults;
    } catch (error) {
      console.error('Error fetching trending videos:', error);
      return [];
    }
  }

  /**
   * Remove duplicate content based on content ID
   */
  private removeDuplicates(contents: RawContent[]): RawContent[] {
    const seen = new Set<string>();
    return contents.filter(content => {
      if (seen.has(content.contentId)) {
        return false;
      }
      seen.add(content.contentId);
      return true;
    });
  }

  /**
   * Generate mock video URL
   */
  private generateMockVideoUrl(): string {
    return `https://aweme.snssdk.com/aweme/v1/play/?video_id=${this.generateMockVideoId()}`;
  }

  /**
   * Generate mock hashtags based on keyword
   */
  private generateMockHashtags(keyword: string): string[] {
    const baseTags = ['心理', '成长', '生活', '分享', '正能量', '治愈', '感悟'];
    const tags = [keyword, ...baseTags.slice(0, Math.floor(Math.random() * 4) + 2)];
    return [...new Set(tags)]; // Remove duplicates
  }

  /**
   * Generate mock video ID
   */
  private generateMockVideoId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * Generate random date within specified days
   */
  private generateRandomDate(daysBack: number = 30): Date {
    const now = new Date();
    const randomDays = Math.floor(Math.random() * daysBack);
    const randomHours = Math.floor(Math.random() * 24);
    const randomMinutes = Math.floor(Math.random() * 60);
    
    const date = new Date(now);
    date.setDate(date.getDate() - randomDays);
    date.setHours(randomHours, randomMinutes, 0, 0);
    
    return date;
  }

  /**
   * Extract hashtags from Douyin video description
   */
  private extractHashtags(text: string): string[] {
    const hashtags: string[] = [];
    
    // Extract hashtags (# format)
    const hashtagRegex = /#([^\s#]+)/g;
    let match;
    while ((match = hashtagRegex.exec(text)) !== null) {
      hashtags.push(match[1]);
    }
    
    return [...new Set(hashtags)];
  }

  /**
   * Extract video metadata from Douyin content
   */
  private extractVideoMetadata(videoData: any): {
    duration: number;
    resolution: string;
    coverUrl: string;
    musicInfo?: {
      title: string;
      author: string;
      url: string;
    };
  } {
    return {
      duration: videoData.duration || 0,
      resolution: videoData.resolution || '720p',
      coverUrl: videoData.coverUrl || '',
      musicInfo: videoData.music ? {
        title: videoData.music.title || '',
        author: videoData.music.author || '',
        url: videoData.music.url || ''
      } : undefined
    };
  }

  /**
   * Determine if creator is verified
   */
  private isVerifiedCreator(creatorInfo: any): boolean {
    // Placeholder logic for determining verified status
    // In real implementation, this would check actual verification badges
    return creatorInfo.followers > 50000 || creatorInfo.hasVerificationBadge;
  }

  /**
   * Extract creator information from video data
   */
  private extractCreatorInfo(videoData: any): {
    id: string;
    name: string;
    followers: number;
    isVerified: boolean;
    avatarUrl?: string;
    description?: string;
  } {
    return {
      id: videoData.authorId || 'unknown',
      name: videoData.authorName || '未知创作者',
      followers: videoData.authorFollowers || 0,
      isVerified: this.isVerifiedCreator(videoData),
      avatarUrl: videoData.authorAvatar,
      description: videoData.authorDescription
    };
  }

  /**
   * Calculate video engagement score
   */
  private calculateVideoEngagementScore(metrics: any): number {
    const likes = metrics.likes || 0;
    const comments = metrics.comments || 0;
    const shares = metrics.shares || 0;
    const views = metrics.views || 1;

    // Weighted engagement score for videos
    const engagementScore = (likes * 1 + comments * 3 + shares * 5) / views;
    return Math.round(engagementScore * 10000) / 10000;
  }

  /**
   * Analyze video content for category classification
   */
  private analyzeVideoContent(content: RawContent): string[] {
    const categories: string[] = [];
    const text = (content.title + ' ' + content.description + ' ' + content.contentText).toLowerCase();

    // Psychology-related keywords
    const psychologyKeywords = ['心理', '情绪', '焦虑', '抑郁', '压力', '心态', '治愈'];
    if (psychologyKeywords.some(keyword => text.includes(keyword))) {
      categories.push('psychology');
    }

    // Growth-related keywords
    const growthKeywords = ['成长', '学习', '进步', '提升', '发展', '改变', '突破'];
    if (growthKeywords.some(keyword => text.includes(keyword))) {
      categories.push('growth');
    }

    // Relationship-related keywords
    const relationshipKeywords = ['关系', '爱情', '友情', '家庭', '沟通', '相处', '情感'];
    if (relationshipKeywords.some(keyword => text.includes(keyword))) {
      categories.push('relationship');
    }

    return categories.length > 0 ? categories : ['other'];
  }
}