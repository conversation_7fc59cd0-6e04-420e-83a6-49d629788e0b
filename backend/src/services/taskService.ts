import { Task, TaskConfig, TaskStatus, Platform, ContentType, ContentCategory } from '../types';
import { TaskModel } from '../models/taskModel';

export class TaskService {
  /**
   * 验证任务配置
   */
  private validateTaskConfig(config: TaskConfig): void {
    if (!config.name || config.name.trim().length === 0) {
      throw new Error('任务名称不能为空');
    }

    if (!config.platforms || config.platforms.length === 0) {
      throw new Error('必须选择至少一个平台');
    }

    // 验证平台是否有效
    const validPlatforms = Object.values(Platform);
    for (const platform of config.platforms) {
      if (!validPlatforms.includes(platform)) {
        throw new Error(`无效的平台: ${platform}`);
      }
    }

    if (!config.keywords || config.keywords.length === 0) {
      throw new Error('必须提供至少一个关键词');
    }

    // 验证关键词
    for (const keyword of config.keywords) {
      if (!keyword || keyword.trim().length === 0) {
        throw new Error('关键词不能为空');
      }
      if (keyword.length > 100) {
        throw new Error('关键词长度不能超过100个字符');
      }
    }

    // 验证过滤器配置
    if (config.filters) {
      const { filters } = config;
      
      if (filters.minFollowers !== undefined && filters.minFollowers < 0) {
        throw new Error('最小粉丝数不能为负数');
      }
      
      if (filters.minLikes !== undefined && filters.minLikes < 0) {
        throw new Error('最小点赞数不能为负数');
      }
      
      if (filters.minComments !== undefined && filters.minComments < 0) {
        throw new Error('最小评论数不能为负数');
      }
      
      if (filters.minViews !== undefined && filters.minViews < 0) {
        throw new Error('最小观看数不能为负数');
      }

      // 验证内容类型
      if (filters.contentTypes) {
        const validContentTypes = Object.values(ContentType);
        for (const contentType of filters.contentTypes) {
          if (!validContentTypes.includes(contentType)) {
            throw new Error(`无效的内容类型: ${contentType}`);
          }
        }
      }

      // 验证内容分类
      if (filters.categories) {
        const validCategories = Object.values(ContentCategory);
        for (const category of filters.categories) {
          if (!validCategories.includes(category)) {
            throw new Error(`无效的内容分类: ${category}`);
          }
        }
      }

      // 验证时间范围
      if (filters.timeRange) {
        const { startDate, endDate } = filters.timeRange;
        if (startDate >= endDate) {
          throw new Error('开始时间必须早于结束时间');
        }
        if (endDate > new Date()) {
          throw new Error('结束时间不能晚于当前时间');
        }
      }
    }

    // 验证最大结果数
    if (config.maxResults !== undefined) {
      if (config.maxResults <= 0) {
        throw new Error('最大结果数必须大于0');
      }
      if (config.maxResults > 10000) {
        throw new Error('最大结果数不能超过10000');
      }
    }

    // 验证调度配置
    if (config.schedule && config.schedule.enabled) {
      if (config.schedule.cron && config.schedule.interval) {
        throw new Error('不能同时设置cron表达式和间隔时间');
      }
      if (!config.schedule.cron && !config.schedule.interval) {
        throw new Error('启用调度时必须设置cron表达式或间隔时间');
      }
      if (config.schedule.interval && config.schedule.interval < 60) {
        throw new Error('调度间隔不能少于60秒');
      }
    }
  }

  /**
   * 创建新任务
   */
  async createTask(userId: string, config: TaskConfig): Promise<Task> {
    // 验证配置
    this.validateTaskConfig(config);

    // 创建任务对象
    const taskData = {
      userId,
      name: config.name.trim(),
      config,
      status: TaskStatus.PENDING,
      progress: 0,
      collectedCount: 0
    };

    try {
      const task = await TaskModel.create(taskData);
      return task;
    } catch (error) {
      throw new Error(`创建任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 启动任务
   */
  async startTask(taskId: string): Promise<void> {
    const task = await TaskModel.findById(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status === TaskStatus.RUNNING) {
      throw new Error('任务已在运行中');
    }

    if (task.status === TaskStatus.COMPLETED) {
      throw new Error('已完成的任务不能重新启动');
    }

    try {
      await TaskModel.updateStatus(taskId, TaskStatus.RUNNING);
      
      // TODO: 在后续任务中实现实际的采集逻辑
      // 这里只是更新状态，实际的采集工作将在采集服务中实现
      
    } catch (error) {
      await TaskModel.updateStatus(taskId, TaskStatus.FAILED, 
        `启动任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
      throw error;
    }
  }

  /**
   * 暂停任务
   */
  async pauseTask(taskId: string): Promise<void> {
    const task = await TaskModel.findById(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status !== TaskStatus.RUNNING) {
      throw new Error('只能暂停正在运行的任务');
    }

    try {
      await TaskModel.updateStatus(taskId, TaskStatus.PENDING);
      
      // TODO: 在后续任务中实现实际的暂停逻辑
      // 这里需要通知采集服务停止当前任务的执行
      
    } catch (error) {
      throw new Error(`暂停任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId: string): Promise<Task> {
    const task = await TaskModel.findById(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }
    return task;
  }

  /**
   * 获取用户的任务历史
   */
  async getTaskHistory(userId: string, page: number = 1, limit: number = 20): Promise<{
    tasks: Task[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const { tasks, total } = await TaskModel.search(userId, undefined, undefined, page, limit);
      
      return {
        tasks,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new Error(`获取任务历史失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 搜索任务
   */
  async searchTasks(
    userId: string,
    searchTerm?: string,
    status?: TaskStatus,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    tasks: Task[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const { tasks, total } = await TaskModel.search(userId, searchTerm, status, page, limit);
      
      return {
        tasks,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new Error(`搜索任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 更新任务进度
   */
  async updateTaskProgress(taskId: string, progress: number, collectedCount?: number): Promise<void> {
    if (progress < 0 || progress > 100) {
      throw new Error('进度值必须在0-100之间');
    }

    try {
      await TaskModel.updateProgress(taskId, progress, collectedCount);
    } catch (error) {
      throw new Error(`更新任务进度失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 完成任务
   */
  async completeTask(taskId: string, collectedCount: number): Promise<void> {
    try {
      await TaskModel.update(taskId, {
        status: TaskStatus.COMPLETED,
        progress: 100,
        collectedCount,
        completedAt: new Date()
      });
    } catch (error) {
      throw new Error(`完成任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 标记任务失败
   */
  async failTask(taskId: string, error: string): Promise<void> {
    try {
      await TaskModel.updateStatus(taskId, TaskStatus.FAILED, error);
    } catch (error) {
      throw new Error(`标记任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId: string, userId: string): Promise<void> {
    const task = await TaskModel.findById(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.userId !== userId) {
      throw new Error('无权限删除此任务');
    }

    if (task.status === TaskStatus.RUNNING) {
      throw new Error('不能删除正在运行的任务');
    }

    try {
      const deleted = await TaskModel.delete(taskId);
      if (!deleted) {
        throw new Error('删除任务失败');
      }
    } catch (error) {
      throw new Error(`删除任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStats(userId: string): Promise<{
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
  }> {
    try {
      return await TaskModel.getTaskStats(userId);
    } catch (error) {
      throw new Error(`获取任务统计失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取正在运行的任务
   */
  async getRunningTasks(): Promise<Task[]> {
    try {
      return await TaskModel.findByStatus(TaskStatus.RUNNING);
    } catch (error) {
      throw new Error(`获取运行中任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 批量更新任务状态
   */
  async batchUpdateTaskStatus(taskIds: string[], status: TaskStatus, error?: string): Promise<void> {
    const updatePromises = taskIds.map(taskId => 
      TaskModel.updateStatus(taskId, status, error)
    );

    try {
      await Promise.all(updatePromises);
    } catch (error) {
      throw new Error(`批量更新任务状态失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}