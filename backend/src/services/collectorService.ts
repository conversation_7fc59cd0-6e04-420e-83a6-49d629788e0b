import { 
  ContentItem, 
  Platform, 
  CollectConfig, 
  CollectionContext,
  CollectionError 
} from '../types';
import { CollectorFactory } from './collectors';

/**
 * Main collector service that orchestrates content collection from different platforms
 */
export class CollectorService {
  private readonly maxRetries = 3;

  /**
   * Collect content from a specific platform
   */
  async collectFromPlatform(
    platform: Platform, 
    config: CollectConfig, 
    taskId: string = 'default'
  ): Promise<ContentItem[]> {
    try {
      // Validate platform support
      if (!CollectorFactory.isPlatformSupported(platform)) {
        throw new Error(`Platform ${platform} is not supported`);
      }

      // Create platform-specific collector
      const collector = CollectorFactory.createCollector(platform);

      // Create collection context
      const context: CollectionContext = {
        taskId,
        platform,
        keywords: config.keywords,
        attempt: 1,
        maxAttempts: this.maxRetries
      };

      // Collect content using the platform collector
      const contentItems = await collector.collect(config, context);

      // Set task ID for all collected items
      contentItems.forEach(item => {
        item.taskId = taskId;
      });

      return contentItems;
    } catch (error) {
      console.error(`Error collecting from platform ${platform}:`, error);
      throw this.handleCollectionError(error, platform);
    }
  }

  /**
   * Validate access to a specific platform
   */
  async validatePlatformAccess(platform: Platform): Promise<boolean> {
    try {
      // Check if platform is supported
      if (!CollectorFactory.isPlatformSupported(platform)) {
        return false;
      }

      // Create platform-specific collector and validate access
      const collector = CollectorFactory.createCollector(platform);
      return await collector.validateAccess();
    } catch (error) {
      console.error(`Error validating access to platform ${platform}:`, error);
      return false;
    }
  }

  /**
   * Get hot/trending content from a specific platform
   */
  async getHotContent(platform: Platform, limit: number = 50): Promise<ContentItem[]> {
    try {
      // Validate platform support
      if (!CollectorFactory.isPlatformSupported(platform)) {
        throw new Error(`Platform ${platform} is not supported`);
      }

      // Create platform-specific collector
      const collector = CollectorFactory.createCollector(platform);

      // Get hot content
      const contentItems = await collector.getHotContent(limit);

      // Set task ID for hot content items
      contentItems.forEach(item => {
        item.taskId = 'hot-content';
      });

      return contentItems;
    } catch (error) {
      console.error(`Error getting hot content from platform ${platform}:`, error);
      throw this.handleCollectionError(error, platform);
    }
  }

  /**
   * Collect content from multiple platforms
   */
  async collectFromMultiplePlatforms(
    platforms: Platform[], 
    config: CollectConfig, 
    taskId: string = 'multi-platform'
  ): Promise<ContentItem[]> {
    const allContent: ContentItem[] = [];
    const errors: Array<{ platform: Platform; error: any }> = [];

    for (const platform of platforms) {
      try {
        const platformConfig = { ...config, platform };
        const content = await this.collectFromPlatform(platform, platformConfig, taskId);
        allContent.push(...content);
      } catch (error) {
        errors.push({ platform, error });
        console.error(`Failed to collect from platform ${platform}:`, error);
      }
    }

    // If all platforms failed, throw an error
    if (errors.length === platforms.length) {
      throw new Error(`Failed to collect from all platforms: ${errors.map(e => e.platform).join(', ')}`);
    }

    return allContent;
  }

  /**
   * Get supported platforms
   */
  getSupportedPlatforms(): Platform[] {
    return CollectorFactory.getSupportedPlatforms();
  }

  /**
   * Check if a platform is supported
   */
  isPlatformSupported(platform: Platform): boolean {
    return CollectorFactory.isPlatformSupported(platform);
  }

  /**
   * Validate collection configuration
   */
  validateCollectionConfig(config: CollectConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check required fields
    if (!config.keywords || config.keywords.length === 0) {
      errors.push('Keywords are required');
    }

    if (!config.platform) {
      errors.push('Platform is required');
    }

    if (config.maxResults && config.maxResults <= 0) {
      errors.push('Max results must be a positive number');
    }

    // Check platform support
    if (config.platform && !this.isPlatformSupported(config.platform)) {
      errors.push(`Platform ${config.platform} is not supported`);
    }

    // Validate filters
    if (config.filters) {
      if (config.filters.minFollowers && config.filters.minFollowers < 0) {
        errors.push('Minimum followers must be non-negative');
      }

      if (config.filters.minLikes && config.filters.minLikes < 0) {
        errors.push('Minimum likes must be non-negative');
      }

      if (config.filters.minComments && config.filters.minComments < 0) {
        errors.push('Minimum comments must be non-negative');
      }

      if (config.filters.minViews && config.filters.minViews < 0) {
        errors.push('Minimum views must be non-negative');
      }

      // Validate time range
      if (config.filters.timeRange) {
        const { startDate, endDate } = config.filters.timeRange;
        if (startDate >= endDate) {
          errors.push('Start date must be before end date');
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Handle collection errors
   */
  private handleCollectionError(error: any, platform: Platform): Error {
    if (error instanceof Error) {
      return error;
    }

    // Handle CollectionError type
    if (error.type && error.platform) {
      return new Error(`Collection error on ${platform}: ${error.message}`);
    }

    return new Error(`Unknown error occurred while collecting from ${platform}: ${error}`);
  }

  /**
   * Get collection statistics
   */
  async getCollectionStats(): Promise<{
    supportedPlatforms: number;
    totalCollectors: number;
    platformStatus: Record<Platform, boolean>;
  }> {
    const supportedPlatforms = this.getSupportedPlatforms();
    const platformStatus: Record<Platform, boolean> = {} as Record<Platform, boolean>;

    // Check status of each supported platform
    for (const platform of supportedPlatforms) {
      try {
        platformStatus[platform] = await this.validatePlatformAccess(platform);
      } catch (error) {
        platformStatus[platform] = false;
      }
    }

    return {
      supportedPlatforms: supportedPlatforms.length,
      totalCollectors: supportedPlatforms.length,
      platformStatus
    };
  }
}