import mysql from 'mysql2/promise';
import { createClient } from 'redis';

// MySQL database configuration
export const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'social_content_collector',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// Create MySQL connection pool
export const createDatabasePool = () => {
  return mysql.createPool(dbConfig);
};

// Redis configuration
export const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0')
};

// Create Redis client
export const createRedisClient = () => {
  const client = createClient({
    socket: {
      host: redisConfig.host,
      port: redisConfig.port
    },
    password: redisConfig.password,
    database: redisConfig.db
  });

  client.on('error', (err) => {
    console.error('Redis Client Error:', err);
  });

  client.on('connect', () => {
    console.log('Connected to Redis');
  });

  return client;
};

// Database connection instances
let dbPool: mysql.Pool;
let redisClient: ReturnType<typeof createRedisClient>;

export const initializeDatabase = async () => {
  try {
    // Initialize MySQL pool
    dbPool = createDatabasePool();
    
    // Test MySQL connection
    const connection = await dbPool.getConnection();
    console.log('Connected to MySQL database');
    connection.release();

    // Initialize Redis client
    redisClient = createRedisClient();
    await redisClient.connect();
    
    // Initialize QueryHelper
    const { QueryHelper } = await import('./queryHelper');
    QueryHelper.initialize();
    
    return { dbPool, redisClient };
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
};

export const getDatabase = () => {
  if (!dbPool) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return dbPool;
};

export const getRedisClient = () => {
  if (!redisClient) {
    throw new Error('Redis client not initialized. Call initializeDatabase() first.');
  }
  return redisClient;
};