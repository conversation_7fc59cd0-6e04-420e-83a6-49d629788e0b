import { Pool, RowDataPacket, ResultSetHeader } from 'mysql2/promise';
import { getDatabase } from './database';

export class QueryHelper {
  private static db: Pool;

  static initialize() {
    this.db = getDatabase();
  }

  static getDb(): Pool {
    if (!this.db) {
      this.initialize();
    }
    return this.db;
  }

  // Generic query method
  static async query<T extends RowDataPacket[]>(
    sql: string, 
    params: any[] = []
  ): Promise<T> {
    const db = this.getDb();
    const [rows] = await db.execute<T>(sql, params);
    return rows;
  }

  // Insert method that returns the inserted ID
  static async insert(
    sql: string, 
    params: any[] = []
  ): Promise<string> {
    const db = this.getDb();
    const [result] = await db.execute<ResultSetHeader>(sql, params);
    return result.insertId?.toString() || '';
  }

  // Update method that returns affected rows count
  static async update(
    sql: string, 
    params: any[] = []
  ): Promise<number> {
    const db = this.getDb();
    const [result] = await db.execute<ResultSetHeader>(sql, params);
    return result.affectedRows;
  }

  // Delete method that returns affected rows count
  static async delete(
    sql: string, 
    params: any[] = []
  ): Promise<number> {
    const db = this.getDb();
    const [result] = await db.execute<ResultSetHeader>(sql, params);
    return result.affectedRows;
  }

  // Transaction helper
  static async transaction<T>(
    callback: (connection: any) => Promise<T>
  ): Promise<T> {
    const db = this.getDb();
    const connection = await db.getConnection();
    
    try {
      await connection.beginTransaction();
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // Utility method to generate UUID
  static generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // Utility method to build WHERE clause from filters
  static buildWhereClause(filters: Record<string, any>): { clause: string; params: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          if (value.length > 0) {
            conditions.push(`${key} IN (${value.map(() => '?').join(', ')})`);
            params.push(...value);
          }
        } else if (typeof value === 'object' && value.min !== undefined) {
          conditions.push(`${key} >= ?`);
          params.push(value.min);
        } else if (typeof value === 'object' && value.max !== undefined) {
          conditions.push(`${key} <= ?`);
          params.push(value.max);
        } else if (typeof value === 'string' && value.includes('%')) {
          conditions.push(`${key} LIKE ?`);
          params.push(value);
        } else {
          conditions.push(`${key} = ?`);
          params.push(value);
        }
      }
    });

    return {
      clause: conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '',
      params
    };
  }

  // Utility method to build ORDER BY clause
  static buildOrderClause(
    sortBy?: string, 
    sortOrder: 'ASC' | 'DESC' = 'DESC'
  ): string {
    if (!sortBy) return '';
    return `ORDER BY ${sortBy} ${sortOrder}`;
  }

  // Utility method to build LIMIT clause for pagination
  static buildLimitClause(page?: number, limit?: number): { clause: string; params: any[] } {
    if (!page || !limit) return { clause: '', params: [] };
    
    const offset = (page - 1) * limit;
    return {
      clause: 'LIMIT ? OFFSET ?',
      params: [limit, offset]
    };
  }
}