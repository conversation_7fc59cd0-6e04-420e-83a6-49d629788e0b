#!/usr/bin/env node

import { initializeDatabase } from './database';
import { runMigrations } from './migrate';
import { PlatformConfigModel } from '../models/platformConfigModel';

/**
 * Complete database initialization script
 * This script will:
 * 1. Initialize database connections
 * 2. Run all migrations
 * 3. Set up default platform configurations
 * 4. Verify the setup
 */
async function initDatabase() {
  try {
    console.log('🚀 Starting database initialization...');

    // Step 1: Initialize database connections
    console.log('📡 Initializing database connections...');
    await initializeDatabase();
    console.log('✅ Database connections established');

    // Step 2: Run migrations
    console.log('🔄 Running database migrations...');
    await runMigrations();
    console.log('✅ Database migrations completed');

    // Step 3: Initialize default platform configurations
    console.log('⚙️  Setting up default platform configurations...');
    await PlatformConfigModel.initializeDefaultConfigs();
    console.log('✅ Default platform configurations initialized');

    // Step 4: Verify setup
    console.log('🔍 Verifying database setup...');
    await verifyDatabaseSetup();
    console.log('✅ Database setup verification completed');

    console.log('🎉 Database initialization completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Start the backend server: npm run dev');
    console.log('2. The system is ready to accept tasks and collect content');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
}

/**
 * Verify that the database setup is working correctly
 */
async function verifyDatabaseSetup() {
  const { QueryHelper } = await import('./queryHelper');
  
  // Test basic connectivity
  const testQuery = 'SELECT 1 as test';
  const result = await QueryHelper.query(testQuery);
  
  if (!result || result.length === 0) {
    throw new Error('Database connectivity test failed');
  }

  // Verify tables exist
  const tables = [
    'users',
    'tasks', 
    'contents',
    'content_metrics',
    'task_logs',
    'platform_configs'
  ];

  for (const table of tables) {
    const tableCheck = await QueryHelper.query(
      'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?',
      [table]
    );
    
    if (!tableCheck[0] || tableCheck[0].count === 0) {
      throw new Error(`Table '${table}' does not exist`);
    }
  }

  // Verify default data exists
  const userCount = await QueryHelper.query('SELECT COUNT(*) as count FROM users');
  const configCount = await QueryHelper.query('SELECT COUNT(*) as count FROM platform_configs');
  
  console.log(`📊 Database verification results:`);
  console.log(`   - Users: ${userCount[0].count}`);
  console.log(`   - Platform configs: ${configCount[0].count}`);
  console.log(`   - All required tables: ✅`);
}

// Run the initialization if this file is executed directly
if (require.main === module) {
  initDatabase()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('Initialization error:', error);
      process.exit(1);
    });
}

export { initDatabase, verifyDatabaseSetup };