import { CollectorFactory } from '../../services/collectors';
import { <PERSON>hongshuCollector } from '../../services/collectors/XiaohongshuCollector';
import { DouyinCollector } from '../../services/collectors/DouyinCollector';
import { Platform } from '../../types';

describe('CollectorFactory', () => {
  describe('createCollector', () => {
    it('should create XiaohongshuCollector for XIAOHONGSHU platform', () => {
      const collector = CollectorFactory.createCollector(Platform.XIAOHONGSHU);
      
      expect(collector).toBeInstanceOf(XiaohongshuCollector);
    });

    it('should create DouyinCollector for DOUYIN platform', () => {
      const collector = CollectorFactory.createCollector(Platform.DOUYIN);
      
      expect(collector).toBeInstanceOf(DouyinCollector);
    });

    it('should throw error for unsupported WECHAT_ARTICLE platform', () => {
      expect(() => {
        CollectorFactory.createCollector(Platform.WECHAT_ARTICLE);
      }).toThrow('WeChat platform collectors not yet implemented: wechat_article');
    });

    it('should throw error for unsupported WECHAT_VIDEO platform', () => {
      expect(() => {
        CollectorFactory.createCollector(Platform.WECHAT_VIDEO);
      }).toThrow('WeChat platform collectors not yet implemented: wechat_video');
    });

    it('should throw error for invalid platform', () => {
      expect(() => {
        CollectorFactory.createCollector('invalid_platform' as Platform);
      }).toThrow('Unsupported platform: invalid_platform');
    });
  });

  describe('getSupportedPlatforms', () => {
    it('should return array of supported platforms', () => {
      const supportedPlatforms = CollectorFactory.getSupportedPlatforms();
      
      expect(Array.isArray(supportedPlatforms)).toBe(true);
      expect(supportedPlatforms).toContain(Platform.XIAOHONGSHU);
      expect(supportedPlatforms).toContain(Platform.DOUYIN);
      expect(supportedPlatforms).not.toContain(Platform.WECHAT_ARTICLE);
      expect(supportedPlatforms).not.toContain(Platform.WECHAT_VIDEO);
    });

    it('should return exactly 2 supported platforms currently', () => {
      const supportedPlatforms = CollectorFactory.getSupportedPlatforms();
      
      expect(supportedPlatforms).toHaveLength(2);
    });
  });

  describe('isPlatformSupported', () => {
    it('should return true for XIAOHONGSHU platform', () => {
      const isSupported = CollectorFactory.isPlatformSupported(Platform.XIAOHONGSHU);
      
      expect(isSupported).toBe(true);
    });

    it('should return true for DOUYIN platform', () => {
      const isSupported = CollectorFactory.isPlatformSupported(Platform.DOUYIN);
      
      expect(isSupported).toBe(true);
    });

    it('should return false for WECHAT_ARTICLE platform', () => {
      const isSupported = CollectorFactory.isPlatformSupported(Platform.WECHAT_ARTICLE);
      
      expect(isSupported).toBe(false);
    });

    it('should return false for WECHAT_VIDEO platform', () => {
      const isSupported = CollectorFactory.isPlatformSupported(Platform.WECHAT_VIDEO);
      
      expect(isSupported).toBe(false);
    });

    it('should return false for invalid platform', () => {
      const isSupported = CollectorFactory.isPlatformSupported('invalid_platform' as Platform);
      
      expect(isSupported).toBe(false);
    });
  });

  describe('factory consistency', () => {
    it('should create different instances for each call', () => {
      const collector1 = CollectorFactory.createCollector(Platform.XIAOHONGSHU);
      const collector2 = CollectorFactory.createCollector(Platform.XIAOHONGSHU);
      
      expect(collector1).not.toBe(collector2);
      expect(collector1).toBeInstanceOf(XiaohongshuCollector);
      expect(collector2).toBeInstanceOf(XiaohongshuCollector);
    });

    it('should create collectors that match supported platforms', () => {
      const supportedPlatforms = CollectorFactory.getSupportedPlatforms();
      
      supportedPlatforms.forEach(platform => {
        expect(() => {
          const collector = CollectorFactory.createCollector(platform);
          expect(collector).toBeDefined();
        }).not.toThrow();
      });
    });

    it('should have consistent platform support checking', () => {
      const allPlatforms = Object.values(Platform);
      
      allPlatforms.forEach(platform => {
        const isSupported = CollectorFactory.isPlatformSupported(platform);
        
        if (isSupported) {
          // If platform is supported, creating collector should not throw
          expect(() => {
            CollectorFactory.createCollector(platform);
          }).not.toThrow();
        } else {
          // If platform is not supported, creating collector should throw
          expect(() => {
            CollectorFactory.createCollector(platform);
          }).toThrow();
        }
      });
    });
  });

  describe('future extensibility', () => {
    it('should be easy to add new platforms', () => {
      // This test documents the expected behavior when new platforms are added
      const currentSupportedCount = CollectorFactory.getSupportedPlatforms().length;
      
      expect(currentSupportedCount).toBe(2);
      
      // When WeChat platforms are implemented, this count should increase
      // and the isPlatformSupported method should return true for them
    });

    it('should maintain backward compatibility', () => {
      // Ensure that existing supported platforms continue to work
      const xiaohongshuCollector = CollectorFactory.createCollector(Platform.XIAOHONGSHU);
      const douyinCollector = CollectorFactory.createCollector(Platform.DOUYIN);
      
      expect(xiaohongshuCollector).toBeInstanceOf(XiaohongshuCollector);
      expect(douyinCollector).toBeInstanceOf(DouyinCollector);
      
      // Both should have the required methods
      expect(typeof xiaohongshuCollector.collect).toBe('function');
      expect(typeof xiaohongshuCollector.getHotContent).toBe('function');
      expect(typeof xiaohongshuCollector.validateAccess).toBe('function');
      
      expect(typeof douyinCollector.collect).toBe('function');
      expect(typeof douyinCollector.getHotContent).toBe('function');
      expect(typeof douyinCollector.validateAccess).toBe('function');
    });
  });
});