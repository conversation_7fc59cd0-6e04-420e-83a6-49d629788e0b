import { DouyinCollector } from '../../services/collectors/DouyinCollector';
import { 
  Platform, 
  CollectConfig, 
  CollectionContext,
  ContentType 
} from '../../types';

describe('DouyinCollector', () => {
  let collector: DouyinCollector;
  let mockConfig: CollectConfig;
  let mockContext: CollectionContext;

  beforeEach(() => {
    collector = new DouyinCollector();
    mockConfig = {
      keywords: ['心理健康', '个人成长'],
      filters: {
        minFollowers: 1000,
        minLikes: 50,
        minViews: 1000
      },
      maxResults: 15,
      platform: Platform.DOUYIN
    };
    mockContext = {
      taskId: 'test_douyin_task',
      platform: Platform.DOUYIN,
      keywords: ['心理健康', '个人成长'],
      attempt: 1,
      maxAttempts: 3
    };
  });

  describe('collect', () => {
    it('should collect video content from Douyin successfully', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result.length).toBeLessThanOrEqual(mockConfig.maxResults);
      
      // Check that all results are from Douyin and are videos
      result.forEach(item => {
        expect(item.platform).toBe(Platform.DOUYIN);
        expect(item.content.contentType).toBe(ContentType.VIDEO);
        expect(item).toHaveProperty('id');
        expect(item).toHaveProperty('author');
        expect(item).toHaveProperty('content');
        expect(item).toHaveProperty('metrics');
        expect(item).toHaveProperty('originalUrl');
      });
    });

    it('should handle multiple keywords for video search', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      
      // Should have video content related to keywords
      const hasKeywordContent = result.some(item => 
        item.content.title?.includes('心理健康') || 
        item.content.title?.includes('个人成长') ||
        item.content.contentText?.includes('心理健康') ||
        item.content.contentText?.includes('个人成长')
      );
      expect(hasKeywordContent).toBe(true);
    });

    it('should respect maxResults limit for videos', async () => {
      const limitedConfig = { ...mockConfig, maxResults: 8 };
      const result = await collector.collect(limitedConfig, mockContext);
      
      expect(result.length).toBeLessThanOrEqual(8);
    });

    it('should apply video-specific quality filters', async () => {
      const strictConfig = {
        ...mockConfig,
        filters: {
          minFollowers: 10000,
          minLikes: 500,
          minViews: 5000
        }
      };
      
      const result = await collector.collect(strictConfig, mockContext);
      
      result.forEach(item => {
        expect(item.author.followers).toBeGreaterThanOrEqual(10000);
        expect(item.metrics.likesCount).toBeGreaterThanOrEqual(500);
        expect(item.metrics.viewsCount).toBeGreaterThanOrEqual(5000);
      });
    });
  });

  describe('getHotContent', () => {
    it('should get trending videos from Douyin', async () => {
      const result = await collector.getHotContent(10);
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result.length).toBeLessThanOrEqual(10);
      
      result.forEach(item => {
        expect(item.platform).toBe(Platform.DOUYIN);
        expect(item.content.contentType).toBe(ContentType.VIDEO);
        expect(item).toHaveProperty('id');
        expect(item).toHaveProperty('author');
        expect(item).toHaveProperty('content');
        expect(item).toHaveProperty('metrics');
        
        // Trending videos should have high engagement
        expect(item.metrics.likesCount).toBeGreaterThan(0);
        expect(item.metrics.viewsCount).toBeGreaterThan(0);
      });
    });

    it('should return trending videos with recent dates', async () => {
      const result = await collector.getHotContent(5);
      
      const now = new Date();
      const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
      
      result.forEach(item => {
        expect(item.content.publishedAt.getTime()).toBeGreaterThanOrEqual(threeDaysAgo.getTime());
      });
    });

    it('should return trending videos with higher engagement metrics', async () => {
      const result = await collector.getHotContent(10);
      
      result.forEach(item => {
        // Trending videos should have significant engagement
        expect(item.metrics.likesCount).toBeGreaterThan(1000);
        expect(item.metrics.viewsCount).toBeGreaterThan(10000);
      });
    });
  });

  describe('validateAccess', () => {
    it('should validate Douyin platform access', async () => {
      const result = await collector.validateAccess();
      
      expect(typeof result).toBe('boolean');
      // In mock implementation, this should return true
      expect(result).toBe(true);
    });
  });

  describe('video content parsing', () => {
    it('should parse Douyin video content correctly', async () => {
      const mockHtml = '<html><body>Mock Douyin video content</body></html>';
      const mockUrl = 'https://www.douyin.com/video/123456789';
      
      const result = await (collector as any).parseContent(mockHtml, mockUrl);
      
      expect(result).toBeDefined();
      expect(result.platform).toBe(Platform.DOUYIN);
      expect(result.contentType).toBe(ContentType.VIDEO);
      expect(result.originalUrl).toBe(mockUrl);
      expect(result).toHaveProperty('authorId');
      expect(result).toHaveProperty('contentId');
      expect(result).toHaveProperty('metrics');
    });
  });

  describe('video-specific features', () => {
    it('should include video URLs in mediaUrls', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.content.mediaUrls).toBeDefined();
        expect(Array.isArray(item.content.mediaUrls)).toBe(true);
        expect(item.content.mediaUrls.length).toBeGreaterThan(0);
        
        // Should contain video URLs
        item.content.mediaUrls.forEach(url => {
          expect(typeof url).toBe('string');
          expect(url.length).toBeGreaterThan(0);
        });
      });
    });

    it('should handle video descriptions and hashtags', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.content.description).toBeDefined();
        expect(item.content.contentText).toBeDefined();
        
        // Should contain hashtags in content text
        if (item.content.contentText) {
          const hasHashtags = item.content.contentText.includes('#');
          // Most Douyin videos should have hashtags
          expect(typeof hasHashtags).toBe('boolean');
        }
      });
    });
  });

  describe('hashtags and topics', () => {
    it('should extract hashtags from video content', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.content.tags).toBeDefined();
        expect(Array.isArray(item.content.tags)).toBe(true);
        expect(item.content.tags.length).toBeGreaterThan(0);
      });
    });

    it('should include relevant hashtags for psychology and growth videos', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      const allTags = result.flatMap(item => item.content.tags);
      const relevantTags = ['心理', '成长', '正能量', '治愈', '感悟'];
      
      const hasRelevantTags = relevantTags.some(tag => allTags.includes(tag));
      expect(hasRelevantTags).toBe(true);
    });
  });

  describe('video metrics and engagement', () => {
    it('should include comprehensive video metrics', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.metrics).toHaveProperty('likesCount');
        expect(item.metrics).toHaveProperty('commentsCount');
        expect(item.metrics).toHaveProperty('sharesCount');
        expect(item.metrics).toHaveProperty('viewsCount');
        expect(item.metrics).toHaveProperty('favoritesCount');
        expect(item.metrics).toHaveProperty('engagementRate');
        
        // Video metrics should be non-negative numbers
        expect(item.metrics.likesCount).toBeGreaterThanOrEqual(0);
        expect(item.metrics.commentsCount).toBeGreaterThanOrEqual(0);
        expect(item.metrics.sharesCount).toBeGreaterThanOrEqual(0);
        expect(item.metrics.viewsCount).toBeGreaterThanOrEqual(0);
        expect(item.metrics.favoritesCount).toBeGreaterThanOrEqual(0);
        
        // Videos should have views
        expect(item.metrics.viewsCount).toBeGreaterThan(0);
      });
    });

    it('should calculate video engagement rate correctly', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        const { likesCount, commentsCount, sharesCount, viewsCount } = item.metrics;
        const expectedRate = viewsCount > 0 ? 
          (likesCount + commentsCount + sharesCount) / viewsCount : 0;
        
        expect(item.metrics.engagementRate).toBeCloseTo(expectedRate, 4);
      });
    });

    it('should have higher engagement rates for quality videos', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      // Filter for videos with good metrics
      const qualityVideos = result.filter(item => 
        item.metrics.likesCount > 1000 && item.metrics.viewsCount > 10000
      );
      
      if (qualityVideos.length > 0) {
        qualityVideos.forEach(item => {
          expect(item.metrics.engagementRate).toBeGreaterThan(0);
        });
      }
    });
  });

  describe('creator information', () => {
    it('should include complete creator information', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.author).toHaveProperty('id');
        expect(item.author).toHaveProperty('name');
        expect(item.author).toHaveProperty('followers');
        expect(item.author).toHaveProperty('isVerified');
        expect(item.author).toHaveProperty('platform', Platform.DOUYIN);
        
        expect(typeof item.author.id).toBe('string');
        expect(typeof item.author.name).toBe('string');
        expect(typeof item.author.followers).toBe('number');
        expect(typeof item.author.isVerified).toBe('boolean');
        expect(item.author.followers).toBeGreaterThanOrEqual(0);
      });
    });

    it('should identify verified creators correctly', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      // Check if verification logic is working
      const verifiedCreators = result.filter(item => item.author.isVerified);
      const highFollowerCreators = result.filter(item => item.author.followers > 50000);
      
      // Most high-follower creators should be verified in the mock data
      if (highFollowerCreators.length > 0) {
        expect(verifiedCreators.length).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('error handling', () => {
    it('should handle network errors gracefully', async () => {
      // Mock a network error scenario
      const errorConfig = { ...mockConfig, keywords: ['网络错误测试'] };
      
      // Should not throw but may return empty results
      const result = await collector.collect(errorConfig, mockContext);
      expect(Array.isArray(result)).toBe(true);
    });

    it('should handle empty video search results', async () => {
      const emptyConfig = { ...mockConfig, keywords: ['不存在的视频关键词12345'] };
      
      const result = await collector.collect(emptyConfig, mockContext);
      expect(Array.isArray(result)).toBe(true);
      // May return empty array for non-existent keywords
    });

    it('should handle rate limiting appropriately', async () => {
      // Test that the collector respects rate limits
      const startTime = Date.now();
      
      await collector.collect(mockConfig, mockContext);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should take some time due to rate limiting delays
      expect(duration).toBeGreaterThan(0);
    });
  });

  describe('deduplication', () => {
    it('should remove duplicate videos', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      const contentIds = result.map(item => item.content.id);
      const uniqueIds = new Set(contentIds);
      
      expect(contentIds.length).toBe(uniqueIds.size);
    });

    it('should remove duplicate video URLs', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      const originalUrls = result.map(item => item.originalUrl);
      const uniqueUrls = new Set(originalUrls);
      
      expect(originalUrls.length).toBe(uniqueUrls.size);
    });
  });

  describe('date and timing', () => {
    it('should include valid video publication dates', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.content.publishedAt).toBeInstanceOf(Date);
        expect(item.collectedAt).toBeInstanceOf(Date);
        
        // Published date should not be in the future
        expect(item.content.publishedAt.getTime()).toBeLessThanOrEqual(Date.now());
        
        // Collection date should be recent
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        expect(item.collectedAt.getTime()).toBeGreaterThan(fiveMinutesAgo);
      });
    });

    it('should have reasonable video publication dates', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      
      result.forEach(item => {
        // Most videos should be from the last 30 days
        expect(item.content.publishedAt.getTime()).toBeGreaterThanOrEqual(thirtyDaysAgo.getTime());
      });
    });
  });

  describe('content quality', () => {
    it('should collect videos with meaningful content', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        // Videos should have titles or descriptions
        const hasContent = item.content.title || item.content.description || item.content.contentText;
        expect(hasContent).toBeTruthy();
        
        // Content should not be empty
        if (item.content.title) {
          expect(item.content.title.trim().length).toBeGreaterThan(0);
        }
        if (item.content.description) {
          expect(item.content.description.trim().length).toBeGreaterThan(0);
        }
      });
    });

    it('should filter out low-quality videos based on metrics', async () => {
      const qualityConfig = {
        ...mockConfig,
        filters: {
          minLikes: 100,
          minViews: 1000,
          minComments: 10
        }
      };
      
      const result = await collector.collect(qualityConfig, mockContext);
      
      result.forEach(item => {
        expect(item.metrics.likesCount).toBeGreaterThanOrEqual(100);
        expect(item.metrics.viewsCount).toBeGreaterThanOrEqual(1000);
        expect(item.metrics.commentsCount).toBeGreaterThanOrEqual(10);
      });
    });
  });
});