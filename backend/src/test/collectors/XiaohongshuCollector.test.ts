import { XiaohongshuCollector } from '../../services/collectors/XiaohongshuCollector';
import { 
  Platform, 
  CollectConfig, 
  CollectionContext,
  ContentType 
} from '../../types';

describe('XiaohongshuCollector', () => {
  let collector: XiaohongshuCollector;
  let mockConfig: CollectConfig;
  let mockContext: CollectionContext;

  beforeEach(() => {
    collector = new XiaohongshuCollector();
    mockConfig = {
      keywords: ['心理健康', '个人成长'],
      filters: {
        minFollowers: 100,
        minLikes: 10
      },
      maxResults: 20,
      platform: Platform.XIAOHONGSHU
    };
    mockContext = {
      taskId: 'test_xiaohongshu_task',
      platform: Platform.XIAOHONGSHU,
      keywords: ['心理健康', '个人成长'],
      attempt: 1,
      maxAttempts: 3
    };
  });

  describe('collect', () => {
    it('should collect content from Xiaohongshu successfully', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result.length).toBeLessThanOrEqual(mockConfig.maxResults);
      
      // Check that all results are from Xiaohongshu
      result.forEach(item => {
        expect(item.platform).toBe(Platform.XIAOHONGSHU);
        expect(item).toHaveProperty('id');
        expect(item).toHaveProperty('author');
        expect(item).toHaveProperty('content');
        expect(item).toHaveProperty('metrics');
        expect(item).toHaveProperty('originalUrl');
      });
    });

    it('should handle multiple keywords', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      
      // Should have content related to both keywords
      const hasKeywordContent = result.some(item => 
        item.content.title?.includes('心理健康') || 
        item.content.title?.includes('个人成长')
      );
      expect(hasKeywordContent).toBe(true);
    });

    it('should respect maxResults limit', async () => {
      const limitedConfig = { ...mockConfig, maxResults: 5 };
      const result = await collector.collect(limitedConfig, mockContext);
      
      expect(result.length).toBeLessThanOrEqual(5);
    });

    it('should apply quality filters', async () => {
      const strictConfig = {
        ...mockConfig,
        filters: {
          minFollowers: 1000,
          minLikes: 50
        }
      };
      
      const result = await collector.collect(strictConfig, mockContext);
      
      result.forEach(item => {
        expect(item.author.followers).toBeGreaterThanOrEqual(1000);
        expect(item.metrics.likesCount).toBeGreaterThanOrEqual(50);
      });
    });
  });

  describe('getHotContent', () => {
    it('should get hot content from Xiaohongshu', async () => {
      const result = await collector.getHotContent(10);
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result.length).toBeLessThanOrEqual(10);
      
      result.forEach(item => {
        expect(item.platform).toBe(Platform.XIAOHONGSHU);
        expect(item).toHaveProperty('id');
        expect(item).toHaveProperty('author');
        expect(item).toHaveProperty('content');
        expect(item).toHaveProperty('metrics');
        
        // Hot content should have higher engagement
        expect(item.metrics.likesCount).toBeGreaterThan(0);
      });
    });

    it('should return trending content with recent dates', async () => {
      const result = await collector.getHotContent(5);
      
      const now = new Date();
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      
      result.forEach(item => {
        expect(item.content.publishedAt.getTime()).toBeGreaterThanOrEqual(sevenDaysAgo.getTime());
      });
    });
  });

  describe('validateAccess', () => {
    it('should validate platform access', async () => {
      const result = await collector.validateAccess();
      
      expect(typeof result).toBe('boolean');
      // In mock implementation, this should return true
      expect(result).toBe(true);
    });
  });

  describe('content parsing', () => {
    it('should parse Xiaohongshu content correctly', async () => {
      const mockHtml = '<html><body>Mock Xiaohongshu content</body></html>';
      const mockUrl = 'https://www.xiaohongshu.com/discovery/item/123';
      
      const result = await (collector as any).parseContent(mockHtml, mockUrl);
      
      expect(result).toBeDefined();
      expect(result.platform).toBe(Platform.XIAOHONGSHU);
      expect(result.originalUrl).toBe(mockUrl);
      expect(result).toHaveProperty('authorId');
      expect(result).toHaveProperty('contentId');
      expect(result).toHaveProperty('metrics');
    });
  });

  describe('content types', () => {
    it('should handle mixed content types', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      const contentTypes = new Set(result.map(item => item.content.contentType));
      
      // Should include various content types
      expect(contentTypes.size).toBeGreaterThan(0);
      expect([...contentTypes].every(type => 
        Object.values(ContentType).includes(type)
      )).toBe(true);
    });

    it('should include media URLs for image and mixed content', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      const mediaContent = result.filter(item => 
        item.content.contentType === ContentType.IMAGE || 
        item.content.contentType === ContentType.MIXED
      );
      
      mediaContent.forEach(item => {
        expect(item.content.mediaUrls).toBeDefined();
        expect(Array.isArray(item.content.mediaUrls)).toBe(true);
      });
    });
  });

  describe('tags and topics', () => {
    it('should extract tags from content', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.content.tags).toBeDefined();
        expect(Array.isArray(item.content.tags)).toBe(true);
        expect(item.content.tags.length).toBeGreaterThan(0);
      });
    });

    it('should include relevant tags for psychology and growth content', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      const allTags = result.flatMap(item => item.content.tags);
      const relevantTags = ['心理', '成长', '生活', '分享', '经验'];
      
      const hasRelevantTags = relevantTags.some(tag => allTags.includes(tag));
      expect(hasRelevantTags).toBe(true);
    });
  });

  describe('metrics and engagement', () => {
    it('should include comprehensive metrics', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.metrics).toHaveProperty('likesCount');
        expect(item.metrics).toHaveProperty('commentsCount');
        expect(item.metrics).toHaveProperty('sharesCount');
        expect(item.metrics).toHaveProperty('viewsCount');
        expect(item.metrics).toHaveProperty('favoritesCount');
        expect(item.metrics).toHaveProperty('engagementRate');
        
        // All metrics should be non-negative numbers
        expect(item.metrics.likesCount).toBeGreaterThanOrEqual(0);
        expect(item.metrics.commentsCount).toBeGreaterThanOrEqual(0);
        expect(item.metrics.sharesCount).toBeGreaterThanOrEqual(0);
        expect(item.metrics.viewsCount).toBeGreaterThanOrEqual(0);
        expect(item.metrics.favoritesCount).toBeGreaterThanOrEqual(0);
      });
    });

    it('should calculate engagement rate correctly', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        const { likesCount, commentsCount, sharesCount, viewsCount } = item.metrics;
        const expectedRate = viewsCount > 0 ? 
          (likesCount + commentsCount + sharesCount) / viewsCount : 0;
        
        expect(item.metrics.engagementRate).toBeCloseTo(expectedRate, 4);
      });
    });
  });

  describe('author information', () => {
    it('should include complete author information', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.author).toHaveProperty('id');
        expect(item.author).toHaveProperty('name');
        expect(item.author).toHaveProperty('followers');
        expect(item.author).toHaveProperty('isVerified');
        expect(item.author).toHaveProperty('platform', Platform.XIAOHONGSHU);
        
        expect(typeof item.author.id).toBe('string');
        expect(typeof item.author.name).toBe('string');
        expect(typeof item.author.followers).toBe('number');
        expect(typeof item.author.isVerified).toBe('boolean');
        expect(item.author.followers).toBeGreaterThanOrEqual(0);
      });
    });
  });

  describe('error handling', () => {
    it('should handle network errors gracefully', async () => {
      // Mock a network error scenario
      const errorConfig = { ...mockConfig, keywords: ['网络错误测试'] };
      
      // Should not throw but may return empty results
      const result = await collector.collect(errorConfig, mockContext);
      expect(Array.isArray(result)).toBe(true);
    });

    it('should handle empty keyword results', async () => {
      const emptyConfig = { ...mockConfig, keywords: ['不存在的关键词12345'] };
      
      const result = await collector.collect(emptyConfig, mockContext);
      expect(Array.isArray(result)).toBe(true);
      // May return empty array for non-existent keywords
    });
  });

  describe('deduplication', () => {
    it('should remove duplicate content', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      const contentIds = result.map(item => item.content.id);
      const uniqueIds = new Set(contentIds);
      
      expect(contentIds.length).toBe(uniqueIds.size);
    });
  });

  describe('date handling', () => {
    it('should include valid publication dates', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      result.forEach(item => {
        expect(item.content.publishedAt).toBeInstanceOf(Date);
        expect(item.collectedAt).toBeInstanceOf(Date);
        
        // Published date should not be in the future
        expect(item.content.publishedAt.getTime()).toBeLessThanOrEqual(Date.now());
        
        // Collection date should be recent
        const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
        expect(item.collectedAt.getTime()).toBeGreaterThan(fiveMinutesAgo);
      });
    });
  });
});