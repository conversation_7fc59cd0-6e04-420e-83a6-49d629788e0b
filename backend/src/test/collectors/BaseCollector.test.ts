import { BaseCollector } from '../../services/collectors/BaseCollector';
import { 
  Platform, 
  CollectConfig, 
  RawContent, 
  CollectionContext,
  ContentType,
  FilterConfig 
} from '../../types';

// Mock implementation of BaseCollector for testing
class MockCollector extends BaseCollector {
  constructor() {
    super(Platform.XIAOHONGSHU);
  }

  protected async collectRawContent(config: CollectConfig, context: CollectionContext): Promise<RawContent[]> {
    return [this.createMockRawContent()];
  }

  protected async collectHotContent(limit: number): Promise<RawContent[]> {
    return [this.createMockRawContent()];
  }

  protected async checkPlatformAccess(): Promise<boolean> {
    return true;
  }

  protected async parseContent(rawHtml: string, url: string): Promise<RawContent | null> {
    return this.createMockRawContent();
  }

  private createMockRawContent(): RawContent {
    return {
      platform: Platform.XIAOHONGSHU,
      authorId: 'test_author',
      authorName: 'Test Author',
      authorFollowers: 1000,
      contentId: 'test_content',
      contentType: ContentType.TEXT,
      title: 'Test Title',
      description: 'Test Description',
      contentText: 'Test content text',
      mediaUrls: [],
      tags: ['test', 'mock'],
      metrics: {
        likes: 100,
        comments: 20,
        shares: 5,
        views: 1000,
        favorites: 30
      },
      originalUrl: 'https://test.com/content',
      publishedAt: new Date(),
      collectedAt: new Date()
    };
  }
}

describe('BaseCollector', () => {
  let collector: MockCollector;
  let mockConfig: CollectConfig;
  let mockContext: CollectionContext;

  beforeEach(() => {
    collector = new MockCollector();
    mockConfig = {
      keywords: ['test'],
      filters: {},
      maxResults: 10,
      platform: Platform.XIAOHONGSHU
    };
    mockContext = {
      taskId: 'test_task',
      platform: Platform.XIAOHONGSHU,
      keywords: ['test'],
      attempt: 1,
      maxAttempts: 3
    };
  });

  describe('collect', () => {
    it('should successfully collect and process content', async () => {
      const result = await collector.collect(mockConfig, mockContext);
      
      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('id');
      expect(result[0]).toHaveProperty('platform', Platform.XIAOHONGSHU);
      expect(result[0]).toHaveProperty('author');
      expect(result[0]).toHaveProperty('content');
      expect(result[0]).toHaveProperty('metrics');
    });

    it('should validate configuration before collecting', async () => {
      const invalidConfig = { ...mockConfig, keywords: [] };
      
      await expect(collector.collect(invalidConfig, mockContext))
        .rejects.toThrow('Keywords are required for content collection');
    });

    it('should validate platform match', async () => {
      const invalidConfig = { ...mockConfig, platform: Platform.DOUYIN };
      
      await expect(collector.collect(invalidConfig, mockContext))
        .rejects.toThrow('Platform mismatch');
    });

    it('should validate max results', async () => {
      const invalidConfig = { ...mockConfig, maxResults: -1 };
      
      await expect(collector.collect(invalidConfig, mockContext))
        .rejects.toThrow('Max results must be a positive number');
    });
  });

  describe('getHotContent', () => {
    it('should successfully get hot content', async () => {
      const result = await collector.getHotContent(5);
      
      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('platform', Platform.XIAOHONGSHU);
    });

    it('should use default limit when not specified', async () => {
      const result = await collector.getHotContent();
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('validateAccess', () => {
    it('should return true for valid access', async () => {
      const result = await collector.validateAccess();
      
      expect(result).toBe(true);
    });
  });

  describe('applyQualityFilters', () => {
    let mockContents: any[];

    beforeEach(() => {
      mockContents = [
        {
          author: { followers: 1000 },
          metrics: { likesCount: 100, commentsCount: 20, viewsCount: 1000 },
          content: { contentType: ContentType.TEXT, publishedAt: new Date() }
        },
        {
          author: { followers: 500 },
          metrics: { likesCount: 50, commentsCount: 10, viewsCount: 500 },
          content: { contentType: ContentType.VIDEO, publishedAt: new Date() }
        }
      ];
    });

    it('should filter by minimum followers', () => {
      const filters: FilterConfig = { minFollowers: 800 };
      const result = (collector as any).applyQualityFilters(mockContents, filters);
      
      expect(result).toHaveLength(1);
      expect(result[0].author.followers).toBeGreaterThanOrEqual(800);
    });

    it('should filter by minimum likes', () => {
      const filters: FilterConfig = { minLikes: 75 };
      const result = (collector as any).applyQualityFilters(mockContents, filters);
      
      expect(result).toHaveLength(1);
      expect(result[0].metrics.likesCount).toBeGreaterThanOrEqual(75);
    });

    it('should filter by content type', () => {
      const filters: FilterConfig = { contentTypes: [ContentType.VIDEO] };
      const result = (collector as any).applyQualityFilters(mockContents, filters);
      
      expect(result).toHaveLength(1);
      expect(result[0].content.contentType).toBe(ContentType.VIDEO);
    });

    it('should apply multiple filters', () => {
      const filters: FilterConfig = { 
        minFollowers: 600,
        minLikes: 60,
        contentTypes: [ContentType.TEXT]
      };
      const result = (collector as any).applyQualityFilters(mockContents, filters);
      
      expect(result).toHaveLength(1);
      expect(result[0].author.followers).toBeGreaterThanOrEqual(600);
      expect(result[0].metrics.likesCount).toBeGreaterThanOrEqual(60);
      expect(result[0].content.contentType).toBe(ContentType.TEXT);
    });
  });

  describe('calculateEngagementRate', () => {
    it('should calculate engagement rate correctly', () => {
      const metrics = { likes: 100, comments: 20, shares: 5, views: 1000 };
      const rate = (collector as any).calculateEngagementRate(metrics);
      
      expect(rate).toBe(0.125); // (100 + 20 + 5) / 1000
    });

    it('should return 0 for zero views', () => {
      const metrics = { likes: 100, comments: 20, shares: 5, views: 0 };
      const rate = (collector as any).calculateEngagementRate(metrics);
      
      expect(rate).toBe(0);
    });

    it('should handle missing metrics', () => {
      const metrics = { views: 1000 };
      const rate = (collector as any).calculateEngagementRate(metrics);
      
      expect(rate).toBe(0);
    });
  });

  describe('generateContentId', () => {
    it('should generate unique IDs for different content', () => {
      const rawContent1 = {
        platform: Platform.XIAOHONGSHU,
        contentId: 'content1',
        authorId: 'author1'
      } as RawContent;

      const rawContent2 = {
        platform: Platform.XIAOHONGSHU,
        contentId: 'content2',
        authorId: 'author2'
      } as RawContent;

      const id1 = (collector as any).generateContentId(rawContent1);
      const id2 = (collector as any).generateContentId(rawContent2);

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^XIA_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^XIA_\d+_[a-z0-9]+$/);
    });
  });

  describe('extractKeywords', () => {
    it('should extract keywords from text', () => {
      const text = '这是一个关于心理健康和个人成长的内容';
      const keywords = (collector as any).extractKeywords(text);
      
      expect(keywords).toContain('心理健康');
      expect(keywords).toContain('个人成长');
      expect(keywords).toContain('内容');
    });

    it('should handle empty text', () => {
      const keywords = (collector as any).extractKeywords('');
      
      expect(keywords).toEqual([]);
    });

    it('should remove duplicates', () => {
      const text = '测试 测试 内容 内容';
      const keywords = (collector as any).extractKeywords(text);
      
      expect(keywords).toEqual(['测试', '内容']);
    });
  });

  describe('matchesKeywords', () => {
    const mockRawContent: RawContent = {
      platform: Platform.XIAOHONGSHU,
      authorId: 'test',
      authorName: 'test',
      contentId: 'test',
      contentType: ContentType.TEXT,
      title: '心理健康相关内容',
      description: '这是关于个人成长的描述',
      contentText: '详细的心理学内容',
      mediaUrls: [],
      tags: ['心理', '成长'],
      metrics: {},
      originalUrl: 'test',
      collectedAt: new Date()
    };

    it('should match keywords in title', () => {
      const matches = (collector as any).matchesKeywords(mockRawContent, ['心理健康']);
      expect(matches).toBe(true);
    });

    it('should match keywords in description', () => {
      const matches = (collector as any).matchesKeywords(mockRawContent, ['个人成长']);
      expect(matches).toBe(true);
    });

    it('should match keywords in tags', () => {
      const matches = (collector as any).matchesKeywords(mockRawContent, ['心理']);
      expect(matches).toBe(true);
    });

    it('should return false for non-matching keywords', () => {
      const matches = (collector as any).matchesKeywords(mockRawContent, ['不相关']);
      expect(matches).toBe(false);
    });

    it('should return true for empty keywords array', () => {
      const matches = (collector as any).matchesKeywords(mockRawContent, []);
      expect(matches).toBe(true);
    });
  });

  describe('error handling', () => {
    it('should handle collection errors properly', () => {
      const error = new Error('Test error');
      const collectionError = (collector as any).handleCollectionError(error, mockContext);
      
      expect(collectionError).toHaveProperty('type');
      expect(collectionError).toHaveProperty('message', 'Test error');
      expect(collectionError).toHaveProperty('platform', Platform.XIAOHONGSHU);
      expect(collectionError).toHaveProperty('retryable');
    });

    it('should classify rate limit errors', () => {
      const error = new Error('rate limit exceeded');
      const collectionError = (collector as any).handleCollectionError(error, mockContext);
      
      expect(collectionError.type).toBe('RATE_LIMIT');
      expect(collectionError.retryable).toBe(true);
      expect(collectionError.retryAfter).toBe(60000);
    });

    it('should classify anti-crawler errors', () => {
      const error = new Error('blocked by anti-crawler');
      const collectionError = (collector as any).handleCollectionError(error, mockContext);
      
      expect(collectionError.type).toBe('ANTI_CRAWLER');
      expect(collectionError.retryable).toBe(true);
      expect(collectionError.retryAfter).toBe(300000);
    });

    it('should classify network errors', () => {
      const error = { code: 'ENOTFOUND', message: 'Network error' };
      const collectionError = (collector as any).handleCollectionError(error, mockContext);
      
      expect(collectionError.type).toBe('NETWORK_ERROR');
      expect(collectionError.retryable).toBe(true);
      expect(collectionError.retryAfter).toBe(5000);
    });
  });
});