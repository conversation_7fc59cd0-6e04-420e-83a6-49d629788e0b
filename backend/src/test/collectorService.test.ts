import { CollectorService } from '../services/collectorService';
import { 
  Platform, 
  CollectConfig, 
  ContentType,
  FilterConfig 
} from '../types';

// Mock the collectors
jest.mock('../services/collectors', () => ({
  CollectorFactory: {
    createCollector: jest.fn(),
    getSupportedPlatforms: jest.fn(() => [Platform.XIAOHONGSHU, Platform.DOUYIN]),
    isPlatformSupported: jest.fn((platform: Platform) => 
      [Platform.XIAOHONGSHU, Platform.DOUYIN].includes(platform)
    )
  }
}));

import { CollectorFactory } from '../services/collectors';

describe('CollectorService', () => {
  let collectorService: CollectorService;
  let mockCollector: any;

  beforeEach(() => {
    collectorService = new CollectorService();
    
    // Create mock collector
    mockCollector = {
      collect: jest.fn(),
      getHotContent: jest.fn(),
      validateAccess: jest.fn()
    };

    // Reset mocks
    jest.clearAllMocks();
    (CollectorFactory.createCollector as jest.Mock).mockReturnValue(mockCollector);
  });

  describe('collectFromPlatform', () => {
    const mockConfig: CollectConfig = {
      keywords: ['心理健康', '个人成长'],
      filters: { minLikes: 10 },
      maxResults: 20,
      platform: Platform.XIAOHONGSHU
    };

    const mockContentItems = [
      {
        id: 'content_1',
        taskId: '',
        platform: Platform.XIAOHONGSHU,
        author: {
          id: 'author_1',
          name: 'Test Author',
          followers: 1000,
          isVerified: false,
          platform: Platform.XIAOHONGSHU
        },
        content: {
          id: 'content_1',
          title: 'Test Content',
          contentType: ContentType.TEXT,
          mediaUrls: [],
          tags: ['test'],
          publishedAt: new Date()
        },
        metrics: {
          likesCount: 100,
          commentsCount: 20,
          sharesCount: 5,
          viewsCount: 1000,
          favoritesCount: 30,
          engagementRate: 0.125
        },
        originalUrl: 'https://test.com',
        collectedAt: new Date()
      }
    ];

    it('should collect content from supported platform successfully', async () => {
      mockCollector.collect.mockResolvedValue(mockContentItems);

      const result = await collectorService.collectFromPlatform(
        Platform.XIAOHONGSHU, 
        mockConfig, 
        'test_task'
      );

      expect(CollectorFactory.isPlatformSupported).toHaveBeenCalledWith(Platform.XIAOHONGSHU);
      expect(CollectorFactory.createCollector).toHaveBeenCalledWith(Platform.XIAOHONGSHU);
      expect(mockCollector.collect).toHaveBeenCalledWith(
        mockConfig,
        expect.objectContaining({
          taskId: 'test_task',
          platform: Platform.XIAOHONGSHU,
          keywords: mockConfig.keywords,
          attempt: 1,
          maxAttempts: 3
        })
      );

      expect(result).toHaveLength(1);
      expect(result[0].taskId).toBe('test_task');
    });

    it('should throw error for unsupported platform', async () => {
      (CollectorFactory.isPlatformSupported as jest.Mock).mockReturnValue(false);

      await expect(
        collectorService.collectFromPlatform(Platform.WECHAT_ARTICLE, mockConfig)
      ).rejects.toThrow('Platform wechat_article is not supported');
    });

    it('should handle collection errors', async () => {
      const error = new Error('Collection failed');
      mockCollector.collect.mockRejectedValue(error);

      await expect(
        collectorService.collectFromPlatform(Platform.XIAOHONGSHU, mockConfig)
      ).rejects.toThrow('Collection failed');
    });

    it('should use default taskId when not provided', async () => {
      mockCollector.collect.mockResolvedValue(mockContentItems);

      const result = await collectorService.collectFromPlatform(
        Platform.XIAOHONGSHU, 
        mockConfig
      );

      expect(result[0].taskId).toBe('default');
    });
  });

  describe('validatePlatformAccess', () => {
    it('should validate access for supported platform', async () => {
      mockCollector.validateAccess.mockResolvedValue(true);

      const result = await collectorService.validatePlatformAccess(Platform.XIAOHONGSHU);

      expect(CollectorFactory.isPlatformSupported).toHaveBeenCalledWith(Platform.XIAOHONGSHU);
      expect(CollectorFactory.createCollector).toHaveBeenCalledWith(Platform.XIAOHONGSHU);
      expect(mockCollector.validateAccess).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should return false for unsupported platform', async () => {
      (CollectorFactory.isPlatformSupported as jest.Mock).mockReturnValue(false);

      const result = await collectorService.validatePlatformAccess(Platform.WECHAT_ARTICLE);

      expect(result).toBe(false);
      expect(CollectorFactory.createCollector).not.toHaveBeenCalled();
    });

    it('should handle validation errors', async () => {
      mockCollector.validateAccess.mockRejectedValue(new Error('Validation failed'));

      const result = await collectorService.validatePlatformAccess(Platform.XIAOHONGSHU);

      expect(result).toBe(false);
    });
  });

  describe('getHotContent', () => {
    const mockHotContent = [
      {
        id: 'hot_content_1',
        taskId: '',
        platform: Platform.DOUYIN,
        author: {
          id: 'hot_author_1',
          name: 'Hot Creator',
          followers: 50000,
          isVerified: true,
          platform: Platform.DOUYIN
        },
        content: {
          id: 'hot_content_1',
          title: 'Hot Video',
          contentType: ContentType.VIDEO,
          mediaUrls: ['https://video.com/hot.mp4'],
          tags: ['热门', '推荐'],
          publishedAt: new Date()
        },
        metrics: {
          likesCount: 5000,
          commentsCount: 500,
          sharesCount: 200,
          viewsCount: 50000,
          favoritesCount: 1000,
          engagementRate: 0.114
        },
        originalUrl: 'https://douyin.com/video/hot',
        collectedAt: new Date()
      }
    ];

    it('should get hot content from supported platform', async () => {
      mockCollector.getHotContent.mockResolvedValue(mockHotContent);

      const result = await collectorService.getHotContent(Platform.DOUYIN, 10);

      expect(CollectorFactory.isPlatformSupported).toHaveBeenCalledWith(Platform.DOUYIN);
      expect(CollectorFactory.createCollector).toHaveBeenCalledWith(Platform.DOUYIN);
      expect(mockCollector.getHotContent).toHaveBeenCalledWith(10);

      expect(result).toHaveLength(1);
      expect(result[0].taskId).toBe('hot-content');
    });

    it('should use default limit when not specified', async () => {
      mockCollector.getHotContent.mockResolvedValue(mockHotContent);

      await collectorService.getHotContent(Platform.DOUYIN);

      expect(mockCollector.getHotContent).toHaveBeenCalledWith(50);
    });

    it('should throw error for unsupported platform', async () => {
      (CollectorFactory.isPlatformSupported as jest.Mock).mockReturnValue(false);

      await expect(
        collectorService.getHotContent(Platform.WECHAT_VIDEO)
      ).rejects.toThrow('Platform wechat_video is not supported');
    });
  });

  describe('collectFromMultiplePlatforms', () => {
    const mockConfig: CollectConfig = {
      keywords: ['test'],
      filters: {},
      maxResults: 10,
      platform: Platform.XIAOHONGSHU // This will be overridden for each platform
    };

    it('should collect from multiple platforms successfully', async () => {
      const xiaohongshuContent = [{ id: 'xhs_1', platform: Platform.XIAOHONGSHU, taskId: '' } as any];
      const douyinContent = [{ id: 'dy_1', platform: Platform.DOUYIN, taskId: '' } as any];

      mockCollector.collect
        .mockResolvedValueOnce(xiaohongshuContent)
        .mockResolvedValueOnce(douyinContent);

      const result = await collectorService.collectFromMultiplePlatforms(
        [Platform.XIAOHONGSHU, Platform.DOUYIN],
        mockConfig,
        'multi_task'
      );

      expect(result).toHaveLength(2);
      expect(result[0].taskId).toBe('multi_task');
      expect(result[1].taskId).toBe('multi_task');
    });

    it('should continue collecting even if one platform fails', async () => {
      const douyinContent = [{ id: 'dy_1', platform: Platform.DOUYIN, taskId: '' } as any];

      mockCollector.collect
        .mockRejectedValueOnce(new Error('Xiaohongshu failed'))
        .mockResolvedValueOnce(douyinContent);

      const result = await collectorService.collectFromMultiplePlatforms(
        [Platform.XIAOHONGSHU, Platform.DOUYIN],
        mockConfig
      );

      expect(result).toHaveLength(1);
      expect(result[0].platform).toBe(Platform.DOUYIN);
    });

    it('should throw error if all platforms fail', async () => {
      mockCollector.collect
        .mockRejectedValue(new Error('Collection failed'));

      await expect(
        collectorService.collectFromMultiplePlatforms(
          [Platform.XIAOHONGSHU, Platform.DOUYIN],
          mockConfig
        )
      ).rejects.toThrow('Failed to collect from all platforms');
    });
  });

  describe('getSupportedPlatforms', () => {
    it('should return supported platforms from factory', () => {
      const result = collectorService.getSupportedPlatforms();

      expect(CollectorFactory.getSupportedPlatforms).toHaveBeenCalled();
      expect(result).toEqual([Platform.XIAOHONGSHU, Platform.DOUYIN]);
    });
  });

  describe('isPlatformSupported', () => {
    it('should check platform support via factory', () => {
      const result = collectorService.isPlatformSupported(Platform.XIAOHONGSHU);

      expect(CollectorFactory.isPlatformSupported).toHaveBeenCalledWith(Platform.XIAOHONGSHU);
      expect(result).toBe(true);
    });
  });

  describe('validateCollectionConfig', () => {
    it('should validate valid configuration', () => {
      const validConfig: CollectConfig = {
        keywords: ['test'],
        filters: { minLikes: 10 },
        maxResults: 20,
        platform: Platform.XIAOHONGSHU
      };

      const result = collectorService.validateCollectionConfig(validConfig);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing keywords', () => {
      const invalidConfig: CollectConfig = {
        keywords: [],
        filters: {},
        maxResults: 20,
        platform: Platform.XIAOHONGSHU
      };

      const result = collectorService.validateCollectionConfig(invalidConfig);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Keywords are required');
    });

    it('should detect missing platform', () => {
      const invalidConfig = {
        keywords: ['test'],
        filters: {},
        maxResults: 20
      } as CollectConfig;

      const result = collectorService.validateCollectionConfig(invalidConfig);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Platform is required');
    });

    it('should detect invalid max results', () => {
      const invalidConfig: CollectConfig = {
        keywords: ['test'],
        filters: {},
        maxResults: -1,
        platform: Platform.XIAOHONGSHU
      };

      const result = collectorService.validateCollectionConfig(invalidConfig);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Max results must be a positive number');
    });

    it('should detect unsupported platform', () => {
      (CollectorFactory.isPlatformSupported as jest.Mock).mockReturnValue(false);

      const invalidConfig: CollectConfig = {
        keywords: ['test'],
        filters: {},
        maxResults: 20,
        platform: Platform.WECHAT_ARTICLE
      };

      const result = collectorService.validateCollectionConfig(invalidConfig);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Platform wechat_article is not supported');
    });

    it('should validate filter constraints', () => {
      const invalidConfig: CollectConfig = {
        keywords: ['test'],
        filters: {
          minFollowers: -1,
          minLikes: -5,
          timeRange: {
            startDate: new Date('2023-12-01'),
            endDate: new Date('2023-11-01') // End before start
          }
        },
        maxResults: 20,
        platform: Platform.XIAOHONGSHU
      };

      const result = collectorService.validateCollectionConfig(invalidConfig);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Minimum followers must be non-negative');
      expect(result.errors).toContain('Minimum likes must be non-negative');
      expect(result.errors).toContain('Start date must be before end date');
    });
  });

  describe('getCollectionStats', () => {
    it('should return collection statistics', async () => {
      mockCollector.validateAccess
        .mockResolvedValueOnce(true)  // For XIAOHONGSHU
        .mockResolvedValueOnce(false); // For DOUYIN

      const result = await collectorService.getCollectionStats();

      expect(result.supportedPlatforms).toBe(2);
      expect(result.totalCollectors).toBe(2);
      expect(result.platformStatus[Platform.XIAOHONGSHU]).toBe(true);
      expect(result.platformStatus[Platform.DOUYIN]).toBe(false);
    });

    it('should handle validation errors in stats', async () => {
      mockCollector.validateAccess.mockRejectedValue(new Error('Validation failed'));

      const result = await collectorService.getCollectionStats();

      expect(result.supportedPlatforms).toBe(2);
      expect(result.totalCollectors).toBe(2);
      expect(result.platformStatus[Platform.XIAOHONGSHU]).toBe(false);
      expect(result.platformStatus[Platform.DOUYIN]).toBe(false);
    });
  });

  describe('error handling', () => {
    it('should handle generic errors in collectFromPlatform', async () => {
      const genericError = 'String error';
      mockCollector.collect.mockRejectedValue(genericError);

      await expect(
        collectorService.collectFromPlatform(Platform.XIAOHONGSHU, {
          keywords: ['test'],
          filters: {},
          maxResults: 10,
          platform: Platform.XIAOHONGSHU
        })
      ).rejects.toThrow('Unknown error occurred while collecting from xiaohongshu: String error');
    });

    it('should handle CollectionError type', async () => {
      const collectionError = {
        type: 'RATE_LIMIT',
        message: 'Rate limit exceeded',
        platform: Platform.XIAOHONGSHU,
        retryable: true
      };
      mockCollector.collect.mockRejectedValue(collectionError);

      await expect(
        collectorService.collectFromPlatform(Platform.XIAOHONGSHU, {
          keywords: ['test'],
          filters: {},
          maxResults: 10,
          platform: Platform.XIAOHONGSHU
        })
      ).rejects.toThrow('Collection error on xiaohongshu: Rate limit exceeded');
    });
  });
});