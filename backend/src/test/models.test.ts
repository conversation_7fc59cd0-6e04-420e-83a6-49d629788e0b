import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { initializeDatabase } from '../config/database';
import { runMigrations } from '../config/migrate';
import { TaskModel, ContentModel, UserModel, TaskLogModel, PlatformConfigModel } from '../models';
import { Platform, TaskStatus, ContentType, ContentCategory } from '../types';

describe('Database Models', () => {
  beforeAll(async () => {
    // Initialize database for testing
    await initializeDatabase();
    await runMigrations();
  });

  describe('UserModel', () => {
    it('should create and retrieve a user', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        role: 'user' as const,
        passwordHash: 'hashedpassword123'
      };

      const user = await UserModel.create(userData);
      expect(user.id).toBeDefined();
      expect(user.username).toBe(userData.username);
      expect(user.email).toBe(userData.email);

      const retrievedUser = await UserModel.findById(user.id);
      expect(retrievedUser).toBeTruthy();
      expect(retrievedUser?.username).toBe(userData.username);
    });

    it('should find user by email', async () => {
      const userData = {
        username: 'testuser2',
        email: '<EMAIL>',
        role: 'user' as const,
        passwordHash: 'hashedpassword123'
      };

      const user = await UserModel.create(userData);
      const foundUser = await UserModel.findByEmail(userData.email);
      
      expect(foundUser).toBeTruthy();
      expect(foundUser?.id).toBe(user.id);
    });
  });

  describe('TaskModel', () => {
    let userId: string;

    beforeEach(async () => {
      const user = await UserModel.create({
        username: 'taskuser',
        email: '<EMAIL>',
        role: 'user',
        passwordHash: 'hashedpassword123'
      });
      userId = user.id;
    });

    it('should create and retrieve a task', async () => {
      const taskData = {
        userId,
        name: 'Test Task',
        config: {
          name: 'Test Task',
          platforms: [Platform.XIAOHONGSHU],
          keywords: ['test', 'keyword'],
          filters: {
            minLikes: 100
          }
        },
        status: TaskStatus.PENDING,
        progress: 0,
        collectedCount: 0
      };

      const task = await TaskModel.create(taskData);
      expect(task.id).toBeDefined();
      expect(task.name).toBe(taskData.name);
      expect(task.status).toBe(TaskStatus.PENDING);

      const retrievedTask = await TaskModel.findById(task.id);
      expect(retrievedTask).toBeTruthy();
      expect(retrievedTask?.name).toBe(taskData.name);
    });

    it('should update task progress', async () => {
      const taskData = {
        userId,
        name: 'Progress Test Task',
        config: {
          name: 'Progress Test Task',
          platforms: [Platform.DOUYIN],
          keywords: ['progress'],
          filters: {}
        },
        status: TaskStatus.RUNNING,
        progress: 0,
        collectedCount: 0
      };

      const task = await TaskModel.create(taskData);
      await TaskModel.updateProgress(task.id, 50, 25);

      const updatedTask = await TaskModel.findById(task.id);
      expect(updatedTask?.progress).toBe(50);
      expect(updatedTask?.collectedCount).toBe(25);
    });
  });

  describe('ContentModel', () => {
    let taskId: string;

    beforeEach(async () => {
      const user = await UserModel.create({
        username: 'contentuser',
        email: '<EMAIL>',
        role: 'user',
        passwordHash: 'hashedpassword123'
      });

      const task = await TaskModel.create({
        userId: user.id,
        name: 'Content Test Task',
        config: {
          name: 'Content Test Task',
          platforms: [Platform.XIAOHONGSHU],
          keywords: ['content'],
          filters: {}
        },
        status: TaskStatus.RUNNING,
        progress: 0,
        collectedCount: 0
      });

      taskId = task.id;
    });

    it('should create and retrieve content', async () => {
      const contentData = {
        taskId,
        platform: Platform.XIAOHONGSHU,
        author: {
          id: 'author123',
          name: 'Test Author',
          followers: 1000,
          isVerified: true,
          platform: Platform.XIAOHONGSHU
        },
        content: {
          id: 'content123',
          title: 'Test Content',
          description: 'This is a test content',
          contentText: 'Full content text here',
          mediaUrls: ['https://example.com/image.jpg'],
          tags: ['test', 'content'],
          publishedAt: new Date(),
          contentType: ContentType.MIXED,
          category: ContentCategory.PSYCHOLOGY
        },
        metrics: {
          likesCount: 100,
          commentsCount: 20,
          sharesCount: 5,
          viewsCount: 1000,
          favoritesCount: 50,
          engagementRate: 0.175
        },
        originalUrl: 'https://xiaohongshu.com/content123'
      };

      const content = await ContentModel.create(contentData);
      expect(content.id).toBeDefined();
      expect(content.author.name).toBe(contentData.author.name);
      expect(content.metrics.likesCount).toBe(contentData.metrics.likesCount);

      const retrievedContent = await ContentModel.findById(content.id);
      expect(retrievedContent).toBeTruthy();
      expect(retrievedContent?.content.title).toBe(contentData.content.title);
    });

    it('should search content by keywords', async () => {
      // Create test content
      const contentData = {
        taskId,
        platform: Platform.DOUYIN,
        author: {
          id: 'author456',
          name: 'Search Author',
          followers: 2000,
          isVerified: false,
          platform: Platform.DOUYIN
        },
        content: {
          id: 'content456',
          title: 'Searchable Content',
          description: 'This content should be searchable',
          contentText: 'Psychology and growth content',
          mediaUrls: [],
          tags: ['psychology', 'growth'],
          publishedAt: new Date(),
          contentType: ContentType.TEXT,
          category: ContentCategory.GROWTH
        },
        metrics: {
          likesCount: 200,
          commentsCount: 30,
          sharesCount: 10,
          viewsCount: 2000,
          favoritesCount: 75
        },
        originalUrl: 'https://douyin.com/content456'
      };

      await ContentModel.create(contentData);

      const searchResults = await ContentModel.search({
        keywords: 'psychology',
        platforms: [Platform.DOUYIN],
        limit: 10,
        page: 1
      });

      expect(searchResults.content.length).toBeGreaterThan(0);
      expect(searchResults.total).toBeGreaterThan(0);
    });
  });

  describe('TaskLogModel', () => {
    let taskId: string;

    beforeEach(async () => {
      const user = await UserModel.create({
        username: 'loguser',
        email: '<EMAIL>',
        role: 'user',
        passwordHash: 'hashedpassword123'
      });

      const task = await TaskModel.create({
        userId: user.id,
        name: 'Log Test Task',
        config: {
          name: 'Log Test Task',
          platforms: [Platform.XIAOHONGSHU],
          keywords: ['log'],
          filters: {}
        },
        status: TaskStatus.RUNNING,
        progress: 0,
        collectedCount: 0
      });

      taskId = task.id;
    });

    it('should create and retrieve task logs', async () => {
      const logData = {
        taskId,
        level: 'info' as const,
        message: 'Task started successfully',
        details: { timestamp: new Date().toISOString() }
      };

      const log = await TaskLogModel.create(logData);
      expect(log.id).toBeDefined();
      expect(log.message).toBe(logData.message);
      expect(log.level).toBe(logData.level);

      const logs = await TaskLogModel.findByTaskId(taskId);
      expect(logs.length).toBeGreaterThan(0);
      expect(logs[0].message).toBe(logData.message);
    });

    it('should use helper methods for logging', async () => {
      await TaskLogModel.logInfo(taskId, 'Info message');
      await TaskLogModel.logWarning(taskId, 'Warning message');
      await TaskLogModel.logError(taskId, 'Error message');

      const logs = await TaskLogModel.findByTaskId(taskId);
      expect(logs.length).toBe(3);

      const logStats = await TaskLogModel.getLogStats(taskId);
      expect(logStats.info).toBe(1);
      expect(logStats.warning).toBe(1);
      expect(logStats.error).toBe(1);
    });
  });

  describe('PlatformConfigModel', () => {
    it('should create and retrieve platform config', async () => {
      const configData = {
        platform: Platform.XIAOHONGSHU,
        configKey: 'test_config',
        configValue: 'test_value',
        isActive: true
      };

      const config = await PlatformConfigModel.create(configData);
      expect(config.id).toBeDefined();
      expect(config.configKey).toBe(configData.configKey);
      expect(config.configValue).toBe(configData.configValue);

      const retrievedConfig = await PlatformConfigModel.findByPlatformAndKey(
        Platform.XIAOHONGSHU,
        'test_config'
      );
      expect(retrievedConfig).toBeTruthy();
      expect(retrievedConfig?.configValue).toBe(configData.configValue);
    });

    it('should get config value with default', async () => {
      const value = await PlatformConfigModel.getConfigValue(
        Platform.DOUYIN,
        'nonexistent_config',
        'default_value'
      );
      expect(value).toBe('default_value');
    });

    it('should set and get config value', async () => {
      await PlatformConfigModel.setConfigValue(
        Platform.DOUYIN,
        'dynamic_config',
        'dynamic_value'
      );

      const value = await PlatformConfigModel.getConfigValue(
        Platform.DOUYIN,
        'dynamic_config'
      );
      expect(value).toBe('dynamic_value');
    });
  });
});