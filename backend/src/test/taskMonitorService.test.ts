import { TaskMonitorService, TaskProgressUpdate } from '../services/taskMonitorService';
import { TaskModel } from '../models/taskModel';
import { TaskLogModel } from '../models/taskLogModel';
import { Task, TaskStatus, TaskConfig } from '../types';

// Mock the models
jest.mock('../models/taskModel');
jest.mock('../models/taskLogModel');

const MockedTaskModel = TaskModel as jest.Mocked<typeof TaskModel>;
const MockedTaskLogModel = TaskLogModel as jest.Mocked<typeof TaskLogModel>;

describe('TaskMonitorService', () => {
  let taskMonitorService: TaskMonitorService;
  const mockTaskId = 'task-123';
  const mockUserId = 'user-456';

  const mockTask: Task = {
    id: mockTaskId,
    userId: mockUserId,
    name: '测试任务',
    config: {} as TaskConfig,
    status: TaskStatus.RUNNING,
    progress: 0,
    collectedCount: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    taskMonitorService = new TaskMonitorService();
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    taskMonitorService.stopCleanupTimer();
    jest.useRealTimers();
  });

  describe('startMonitoring', () => {
    it('应该成功开始监控任务', async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskLogModel.logInfo.mockResolvedValue({} as any);

      const monitoringStartedSpy = jest.fn();
      taskMonitorService.on('monitoringStarted', monitoringStartedSpy);

      await taskMonitorService.startMonitoring(mockTaskId);

      expect(MockedTaskModel.findById).toHaveBeenCalledWith(mockTaskId);
      expect(MockedTaskLogModel.logInfo).toHaveBeenCalledWith(
        mockTaskId,
        '开始任务监控',
        {
          initialProgress: mockTask.progress,
          initialCount: mockTask.collectedCount
        }
      );
      expect(monitoringStartedSpy).toHaveBeenCalledWith({
        taskId: mockTaskId,
        task: mockTask
      });
      expect(taskMonitorService.getMonitoredTasks()).toContain(mockTaskId);
    });

    it('应该拒绝监控不存在的任务', async () => {
      MockedTaskModel.findById.mockResolvedValue(null);

      await expect(taskMonitorService.startMonitoring(mockTaskId))
        .rejects.toThrow('任务不存在');
    });
  });

  describe('stopMonitoring', () => {
    beforeEach(async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskLogModel.logInfo.mockResolvedValue({} as any);
      await taskMonitorService.startMonitoring(mockTaskId);
    });

    it('应该成功停止监控任务', async () => {
      const monitoringStoppedSpy = jest.fn();
      taskMonitorService.on('monitoringStopped', monitoringStoppedSpy);

      await taskMonitorService.stopMonitoring(mockTaskId);

      expect(MockedTaskLogModel.logInfo).toHaveBeenCalledWith(
        mockTaskId,
        '停止任务监控',
        expect.objectContaining({
          finalProgress: mockTask.progress,
          finalCount: mockTask.collectedCount,
          duration: expect.any(Number)
        })
      );
      expect(monitoringStoppedSpy).toHaveBeenCalledWith({ taskId: mockTaskId });
      expect(taskMonitorService.getMonitoredTasks()).not.toContain(mockTaskId);
    });

    it('应该能够停止未监控的任务而不报错', async () => {
      await taskMonitorService.stopMonitoring('non-existent-task');
      // Should not throw error
    });
  });

  describe('updateProgress', () => {
    beforeEach(async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskLogModel.logInfo.mockResolvedValue({} as any);
      MockedTaskModel.updateProgress.mockResolvedValue();
      await taskMonitorService.startMonitoring(mockTaskId);
    });

    it('应该成功更新任务进度', async () => {
      const progressUpdate: TaskProgressUpdate = {
        taskId: mockTaskId,
        progress: 50,
        collectedCount: 100,
        currentAction: '正在采集数据'
      };

      const progressUpdatedSpy = jest.fn();
      taskMonitorService.on('progressUpdated', progressUpdatedSpy);

      await taskMonitorService.updateProgress(progressUpdate);

      expect(MockedTaskModel.updateProgress).toHaveBeenCalledWith(
        mockTaskId,
        50,
        100
      );
      expect(MockedTaskLogModel.logInfo).toHaveBeenCalledWith(
        mockTaskId,
        '任务进度更新',
        expect.objectContaining({
          progress: 50,
          collectedCount: 100,
          currentAction: '正在采集数据'
        })
      );
      expect(progressUpdatedSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          taskId: mockTaskId,
          progress: 50,
          collectedCount: 100,
          currentAction: '正在采集数据'
        })
      );
    });

    it('应该拒绝无效的进度值', async () => {
      const invalidUpdate: TaskProgressUpdate = {
        taskId: mockTaskId,
        progress: -1,
        collectedCount: 100
      };

      await expect(taskMonitorService.updateProgress(invalidUpdate))
        .rejects.toThrow('进度值必须在0-100之间');

      const invalidUpdate2: TaskProgressUpdate = {
        taskId: mockTaskId,
        progress: 101,
        collectedCount: 100
      };

      await expect(taskMonitorService.updateProgress(invalidUpdate2))
        .rejects.toThrow('进度值必须在0-100之间');
    });

    it('应该拒绝更新未监控的任务', async () => {
      const update: TaskProgressUpdate = {
        taskId: 'non-monitored-task',
        progress: 50,
        collectedCount: 100
      };

      await expect(taskMonitorService.updateProgress(update))
        .rejects.toThrow('任务未在监控中');
    });

    it('应该记录错误信息', async () => {
      const progressUpdate: TaskProgressUpdate = {
        taskId: mockTaskId,
        progress: 30,
        collectedCount: 50,
        error: '采集过程中发生错误'
      };

      MockedTaskLogModel.logError.mockResolvedValue({} as any);

      await taskMonitorService.updateProgress(progressUpdate);

      expect(MockedTaskLogModel.logError).toHaveBeenCalledWith(
        mockTaskId,
        '任务执行错误',
        { error: '采集过程中发生错误' }
      );
    });
  });

  describe('getTaskStats', () => {
    beforeEach(async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskLogModel.logInfo.mockResolvedValue({} as any);
      MockedTaskLogModel.getLogStats.mockResolvedValue({
        total: 10,
        info: 8,
        warning: 1,
        error: 1
      });
      await taskMonitorService.startMonitoring(mockTaskId);
    });

    it('应该成功获取任务统计信息', async () => {
      const stats = await taskMonitorService.getTaskStats(mockTaskId);

      expect(stats).toEqual(
        expect.objectContaining({
          taskId: mockTaskId,
          status: TaskStatus.RUNNING,
          progress: 0,
          collectedCount: 0,
          errorCount: 1,
          warningCount: 1,
          startTime: expect.any(Date),
          lastActivity: expect.any(Date)
        })
      );
    });

    it('应该拒绝获取不存在任务的统计信息', async () => {
      MockedTaskModel.findById.mockResolvedValue(null);

      await expect(taskMonitorService.getTaskStats('non-existent-task'))
        .rejects.toThrow('任务不存在');
    });

    it('应该计算平均速度', async () => {
      // 模拟多次进度更新以计算速度
      MockedTaskModel.updateProgress.mockResolvedValue();
      
      await taskMonitorService.updateProgress({
        taskId: mockTaskId,
        progress: 25,
        collectedCount: 50
      });

      // 前进时间
      jest.advanceTimersByTime(60000); // 1 minute

      await taskMonitorService.updateProgress({
        taskId: mockTaskId,
        progress: 50,
        collectedCount: 100
      });

      const stats = await taskMonitorService.getTaskStats(mockTaskId);
      expect(stats.averageSpeed).toBeGreaterThan(0);
    });
  });

  describe('logTaskError', () => {
    it('应该成功记录任务错误', async () => {
      MockedTaskLogModel.logError.mockResolvedValue({} as any);
      
      const taskErrorSpy = jest.fn();
      taskMonitorService.on('taskError', taskErrorSpy);

      await taskMonitorService.logTaskError(mockTaskId, '测试错误', { detail: 'error detail' });

      expect(MockedTaskLogModel.logError).toHaveBeenCalledWith(
        mockTaskId,
        '测试错误',
        { detail: 'error detail' }
      );
      expect(taskErrorSpy).toHaveBeenCalledWith({
        taskId: mockTaskId,
        error: '测试错误',
        details: { detail: 'error detail' },
        timestamp: expect.any(Date)
      });
    });
  });

  describe('logTaskWarning', () => {
    it('应该成功记录任务警告', async () => {
      MockedTaskLogModel.logWarning.mockResolvedValue({} as any);
      
      const taskWarningSpy = jest.fn();
      taskMonitorService.on('taskWarning', taskWarningSpy);

      await taskMonitorService.logTaskWarning(mockTaskId, '测试警告', { detail: 'warning detail' });

      expect(MockedTaskLogModel.logWarning).toHaveBeenCalledWith(
        mockTaskId,
        '测试警告',
        { detail: 'warning detail' }
      );
      expect(taskWarningSpy).toHaveBeenCalledWith({
        taskId: mockTaskId,
        warning: '测试警告',
        details: { detail: 'warning detail' },
        timestamp: expect.any(Date)
      });
    });
  });

  describe('logTaskInfo', () => {
    it('应该成功记录任务信息', async () => {
      MockedTaskLogModel.logInfo.mockResolvedValue({} as any);
      
      const taskInfoSpy = jest.fn();
      taskMonitorService.on('taskInfo', taskInfoSpy);

      await taskMonitorService.logTaskInfo(mockTaskId, '测试信息', { detail: 'info detail' });

      expect(MockedTaskLogModel.logInfo).toHaveBeenCalledWith(
        mockTaskId,
        '测试信息',
        { detail: 'info detail' }
      );
      expect(taskInfoSpy).toHaveBeenCalledWith({
        taskId: mockTaskId,
        message: '测试信息',
        details: { detail: 'info detail' },
        timestamp: expect.any(Date)
      });
    });
  });

  describe('getTaskLogs', () => {
    it('应该成功获取任务日志', async () => {
      const mockLogs = [
        {
          id: 'log-1',
          taskId: mockTaskId,
          level: 'info' as const,
          message: '测试日志1',
          createdAt: new Date()
        },
        {
          id: 'log-2',
          taskId: mockTaskId,
          level: 'error' as const,
          message: '测试日志2',
          createdAt: new Date()
        }
      ];

      MockedTaskLogModel.findByTaskId
        .mockResolvedValueOnce(mockLogs.slice(0, 1)) // 分页结果
        .mockResolvedValueOnce(mockLogs); // 总数查询

      const result = await taskMonitorService.getTaskLogs(mockTaskId, undefined, 1, 1);

      expect(result).toEqual({
        logs: [mockLogs[0]],
        total: 2,
        page: 1,
        totalPages: 2
      });
    });
  });

  describe('resetTaskMonitoring', () => {
    beforeEach(async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskLogModel.logInfo.mockResolvedValue({} as any);
      await taskMonitorService.startMonitoring(mockTaskId);
    });

    it('应该成功重置任务监控状态', async () => {
      MockedTaskModel.update.mockResolvedValue({} as Task);
      
      const monitoringResetSpy = jest.fn();
      taskMonitorService.on('monitoringReset', monitoringResetSpy);

      await taskMonitorService.resetTaskMonitoring(mockTaskId);

      expect(MockedTaskModel.update).toHaveBeenCalledWith(mockTaskId, {
        progress: 0,
        collectedCount: 0,
        status: TaskStatus.PENDING
      });
      expect(MockedTaskLogModel.logInfo).toHaveBeenCalledWith(
        mockTaskId,
        '任务监控状态已重置'
      );
      expect(monitoringResetSpy).toHaveBeenCalledWith({ taskId: mockTaskId });
      expect(taskMonitorService.getMonitoredTasks()).not.toContain(mockTaskId);
    });
  });

  describe('getBatchTaskStats', () => {
    it('应该成功批量获取任务统计信息', async () => {
      const taskIds = ['task-1', 'task-2'];
      
      MockedTaskModel.findById
        .mockResolvedValueOnce({ ...mockTask, id: 'task-1' })
        .mockResolvedValueOnce({ ...mockTask, id: 'task-2' });
      
      MockedTaskLogModel.getLogStats.mockResolvedValue({
        total: 5,
        info: 4,
        warning: 1,
        error: 0
      });

      const results = await taskMonitorService.getBatchTaskStats(taskIds);

      expect(results).toHaveLength(2);
      expect(results[0].taskId).toBe('task-1');
      expect(results[1].taskId).toBe('task-2');
    });

    it('应该处理获取失败的任务', async () => {
      const taskIds = ['task-1', 'non-existent-task'];
      
      MockedTaskModel.findById
        .mockResolvedValueOnce({ ...mockTask, id: 'task-1' })
        .mockResolvedValueOnce(null);
      
      MockedTaskLogModel.getLogStats.mockResolvedValue({
        total: 5,
        info: 4,
        warning: 1,
        error: 0
      });

      const results = await taskMonitorService.getBatchTaskStats(taskIds);

      expect(results).toHaveLength(1);
      expect(results[0].taskId).toBe('task-1');
    });
  });

  describe('getSystemOverview', () => {
    it('应该成功获取系统监控概览', async () => {
      MockedTaskModel.findByStatus
        .mockResolvedValueOnce([mockTask]) // running tasks
        .mockResolvedValueOnce([mockTask, mockTask]) // completed tasks
        .mockResolvedValueOnce([mockTask]); // failed tasks
      
      MockedTaskLogModel.getLogStats.mockResolvedValue({
        total: 100,
        info: 80,
        warning: 15,
        error: 5
      });

      // 添加一些监控中的任务
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskLogModel.logInfo.mockResolvedValue({} as any);
      await taskMonitorService.startMonitoring('task-1');
      await taskMonitorService.startMonitoring('task-2');

      const overview = await taskMonitorService.getSystemOverview();

      expect(overview).toEqual({
        totalMonitoredTasks: 2,
        runningTasks: 1,
        completedTasks: 2,
        failedTasks: 1,
        totalErrors: 5,
        totalWarnings: 15
      });
    });
  });

  describe('getMonitoredTasks', () => {
    it('应该返回正在监控的任务列表', async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskLogModel.logInfo.mockResolvedValue({} as any);

      expect(taskMonitorService.getMonitoredTasks()).toEqual([]);

      await taskMonitorService.startMonitoring('task-1');
      await taskMonitorService.startMonitoring('task-2');

      const monitoredTasks = taskMonitorService.getMonitoredTasks();
      expect(monitoredTasks).toContain('task-1');
      expect(monitoredTasks).toContain('task-2');
      expect(monitoredTasks).toHaveLength(2);
    });
  });
});