import { TaskService } from '../services/taskService';
import { TaskModel } from '../models/taskModel';
import { Task, TaskConfig, TaskStatus, Platform, ContentType, ContentCategory } from '../types';

// Mock the TaskModel
jest.mock('../models/taskModel');
const MockedTaskModel = TaskModel as jest.Mocked<typeof TaskModel>;

describe('TaskService', () => {
  let taskService: TaskService;
  const mockUserId = 'user-123';
  const mockTaskId = 'task-456';

  beforeEach(() => {
    taskService = new TaskService();
    jest.clearAllMocks();
  });

  describe('createTask', () => {
    const validTaskConfig: TaskConfig = {
      name: '测试任务',
      platforms: [Platform.XIAOHONGSHU, Platform.DOUYIN],
      keywords: ['心理健康', '成长'],
      filters: {
        minFollowers: 1000,
        minLikes: 100,
        contentTypes: [ContentType.TEXT, ContentType.VIDEO],
        categories: [ContentCategory.PSYCHOLOGY]
      },
      maxResults: 500
    };

    it('应该成功创建有效的任务', async () => {
      const mockTask: Task = {
        id: mockTaskId,
        userId: mockUserId,
        name: validTaskConfig.name,
        config: validTaskConfig,
        status: TaskStatus.PENDING,
        progress: 0,
        collectedCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      MockedTaskModel.create.mockResolvedValue(mockTask);

      const result = await taskService.createTask(mockUserId, validTaskConfig);

      expect(MockedTaskModel.create).toHaveBeenCalledWith({
        userId: mockUserId,
        name: validTaskConfig.name,
        config: validTaskConfig,
        status: TaskStatus.PENDING,
        progress: 0,
        collectedCount: 0
      });
      expect(result).toEqual(mockTask);
    });

    it('应该拒绝空任务名称', async () => {
      const invalidConfig = { ...validTaskConfig, name: '' };

      await expect(taskService.createTask(mockUserId, invalidConfig))
        .rejects.toThrow('任务名称不能为空');
    });

    it('应该拒绝空平台列表', async () => {
      const invalidConfig = { ...validTaskConfig, platforms: [] };

      await expect(taskService.createTask(mockUserId, invalidConfig))
        .rejects.toThrow('必须选择至少一个平台');
    });

    it('应该拒绝无效的平台', async () => {
      const invalidConfig = { 
        ...validTaskConfig, 
        platforms: ['invalid-platform' as Platform] 
      };

      await expect(taskService.createTask(mockUserId, invalidConfig))
        .rejects.toThrow('无效的平台: invalid-platform');
    });

    it('应该拒绝空关键词列表', async () => {
      const invalidConfig = { ...validTaskConfig, keywords: [] };

      await expect(taskService.createTask(mockUserId, invalidConfig))
        .rejects.toThrow('必须提供至少一个关键词');
    });

    it('应该拒绝空关键词', async () => {
      const invalidConfig = { ...validTaskConfig, keywords: ['', '有效关键词'] };

      await expect(taskService.createTask(mockUserId, invalidConfig))
        .rejects.toThrow('关键词不能为空');
    });

    it('应该拒绝过长的关键词', async () => {
      const longKeyword = 'a'.repeat(101);
      const invalidConfig = { ...validTaskConfig, keywords: [longKeyword] };

      await expect(taskService.createTask(mockUserId, invalidConfig))
        .rejects.toThrow('关键词长度不能超过100个字符');
    });

    it('应该拒绝负数的最小粉丝数', async () => {
      const invalidConfig = { 
        ...validTaskConfig, 
        filters: { ...validTaskConfig.filters, minFollowers: -1 } 
      };

      await expect(taskService.createTask(mockUserId, invalidConfig))
        .rejects.toThrow('最小粉丝数不能为负数');
    });

    it('应该拒绝无效的时间范围', async () => {
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const invalidConfig = { 
        ...validTaskConfig, 
        filters: { 
          ...validTaskConfig.filters, 
          timeRange: { startDate: now, endDate: yesterday } 
        } 
      };

      await expect(taskService.createTask(mockUserId, invalidConfig))
        .rejects.toThrow('开始时间必须早于结束时间');
    });

    it('应该拒绝超过限制的最大结果数', async () => {
      const invalidConfig = { ...validTaskConfig, maxResults: 20000 };

      await expect(taskService.createTask(mockUserId, invalidConfig))
        .rejects.toThrow('最大结果数不能超过10000');
    });
  });

  describe('startTask', () => {
    const mockTask: Task = {
      id: mockTaskId,
      userId: mockUserId,
      name: '测试任务',
      config: {} as TaskConfig,
      status: TaskStatus.PENDING,
      progress: 0,
      collectedCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    it('应该成功启动待执行的任务', async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskModel.updateStatus.mockResolvedValue();

      await taskService.startTask(mockTaskId);

      expect(MockedTaskModel.findById).toHaveBeenCalledWith(mockTaskId);
      expect(MockedTaskModel.updateStatus).toHaveBeenCalledWith(mockTaskId, TaskStatus.RUNNING);
    });

    it('应该拒绝启动不存在的任务', async () => {
      MockedTaskModel.findById.mockResolvedValue(null);

      await expect(taskService.startTask(mockTaskId))
        .rejects.toThrow('任务不存在');
    });

    it('应该拒绝启动已在运行的任务', async () => {
      const runningTask = { ...mockTask, status: TaskStatus.RUNNING };
      MockedTaskModel.findById.mockResolvedValue(runningTask);

      await expect(taskService.startTask(mockTaskId))
        .rejects.toThrow('任务已在运行中');
    });

    it('应该拒绝启动已完成的任务', async () => {
      const completedTask = { ...mockTask, status: TaskStatus.COMPLETED };
      MockedTaskModel.findById.mockResolvedValue(completedTask);

      await expect(taskService.startTask(mockTaskId))
        .rejects.toThrow('已完成的任务不能重新启动');
    });
  });

  describe('pauseTask', () => {
    const mockTask: Task = {
      id: mockTaskId,
      userId: mockUserId,
      name: '测试任务',
      config: {} as TaskConfig,
      status: TaskStatus.RUNNING,
      progress: 50,
      collectedCount: 100,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    it('应该成功暂停正在运行的任务', async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskModel.updateStatus.mockResolvedValue();

      await taskService.pauseTask(mockTaskId);

      expect(MockedTaskModel.findById).toHaveBeenCalledWith(mockTaskId);
      expect(MockedTaskModel.updateStatus).toHaveBeenCalledWith(mockTaskId, TaskStatus.PENDING);
    });

    it('应该拒绝暂停不存在的任务', async () => {
      MockedTaskModel.findById.mockResolvedValue(null);

      await expect(taskService.pauseTask(mockTaskId))
        .rejects.toThrow('任务不存在');
    });

    it('应该拒绝暂停非运行状态的任务', async () => {
      const pendingTask = { ...mockTask, status: TaskStatus.PENDING };
      MockedTaskModel.findById.mockResolvedValue(pendingTask);

      await expect(taskService.pauseTask(mockTaskId))
        .rejects.toThrow('只能暂停正在运行的任务');
    });
  });

  describe('getTaskStatus', () => {
    const mockTask: Task = {
      id: mockTaskId,
      userId: mockUserId,
      name: '测试任务',
      config: {} as TaskConfig,
      status: TaskStatus.RUNNING,
      progress: 75,
      collectedCount: 150,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    it('应该成功获取任务状态', async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);

      const result = await taskService.getTaskStatus(mockTaskId);

      expect(MockedTaskModel.findById).toHaveBeenCalledWith(mockTaskId);
      expect(result).toEqual(mockTask);
    });

    it('应该拒绝获取不存在任务的状态', async () => {
      MockedTaskModel.findById.mockResolvedValue(null);

      await expect(taskService.getTaskStatus(mockTaskId))
        .rejects.toThrow('任务不存在');
    });
  });

  describe('getTaskHistory', () => {
    const mockTasks: Task[] = [
      {
        id: 'task-1',
        userId: mockUserId,
        name: '任务1',
        config: {} as TaskConfig,
        status: TaskStatus.COMPLETED,
        progress: 100,
        collectedCount: 200,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'task-2',
        userId: mockUserId,
        name: '任务2',
        config: {} as TaskConfig,
        status: TaskStatus.RUNNING,
        progress: 50,
        collectedCount: 100,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    it('应该成功获取用户任务历史', async () => {
      MockedTaskModel.search.mockResolvedValue({
        tasks: mockTasks,
        total: 2
      });

      const result = await taskService.getTaskHistory(mockUserId, 1, 20);

      expect(MockedTaskModel.search).toHaveBeenCalledWith(mockUserId, undefined, undefined, 1, 20);
      expect(result).toEqual({
        tasks: mockTasks,
        total: 2,
        page: 1,
        totalPages: 1
      });
    });

    it('应该正确计算总页数', async () => {
      MockedTaskModel.search.mockResolvedValue({
        tasks: mockTasks,
        total: 25
      });

      const result = await taskService.getTaskHistory(mockUserId, 1, 10);

      expect(result.totalPages).toBe(3);
    });
  });

  describe('updateTaskProgress', () => {
    it('应该成功更新任务进度', async () => {
      MockedTaskModel.updateProgress.mockResolvedValue();

      await taskService.updateTaskProgress(mockTaskId, 75, 150);

      expect(MockedTaskModel.updateProgress).toHaveBeenCalledWith(mockTaskId, 75, 150);
    });

    it('应该拒绝无效的进度值', async () => {
      await expect(taskService.updateTaskProgress(mockTaskId, -1))
        .rejects.toThrow('进度值必须在0-100之间');

      await expect(taskService.updateTaskProgress(mockTaskId, 101))
        .rejects.toThrow('进度值必须在0-100之间');
    });
  });

  describe('completeTask', () => {
    it('应该成功完成任务', async () => {
      MockedTaskModel.update.mockResolvedValue({} as Task);

      await taskService.completeTask(mockTaskId, 200);

      expect(MockedTaskModel.update).toHaveBeenCalledWith(mockTaskId, {
        status: TaskStatus.COMPLETED,
        progress: 100,
        collectedCount: 200,
        completedAt: expect.any(Date)
      });
    });
  });

  describe('failTask', () => {
    it('应该成功标记任务失败', async () => {
      const errorMessage = '采集过程中发生错误';
      MockedTaskModel.updateStatus.mockResolvedValue();

      await taskService.failTask(mockTaskId, errorMessage);

      expect(MockedTaskModel.updateStatus).toHaveBeenCalledWith(
        mockTaskId, 
        TaskStatus.FAILED, 
        errorMessage
      );
    });
  });

  describe('deleteTask', () => {
    const mockTask: Task = {
      id: mockTaskId,
      userId: mockUserId,
      name: '测试任务',
      config: {} as TaskConfig,
      status: TaskStatus.COMPLETED,
      progress: 100,
      collectedCount: 200,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    it('应该成功删除已完成的任务', async () => {
      MockedTaskModel.findById.mockResolvedValue(mockTask);
      MockedTaskModel.delete.mockResolvedValue(true);

      await taskService.deleteTask(mockTaskId, mockUserId);

      expect(MockedTaskModel.findById).toHaveBeenCalledWith(mockTaskId);
      expect(MockedTaskModel.delete).toHaveBeenCalledWith(mockTaskId);
    });

    it('应该拒绝删除不存在的任务', async () => {
      MockedTaskModel.findById.mockResolvedValue(null);

      await expect(taskService.deleteTask(mockTaskId, mockUserId))
        .rejects.toThrow('任务不存在');
    });

    it('应该拒绝删除其他用户的任务', async () => {
      const otherUserTask = { ...mockTask, userId: 'other-user' };
      MockedTaskModel.findById.mockResolvedValue(otherUserTask);

      await expect(taskService.deleteTask(mockTaskId, mockUserId))
        .rejects.toThrow('无权限删除此任务');
    });

    it('应该拒绝删除正在运行的任务', async () => {
      const runningTask = { ...mockTask, status: TaskStatus.RUNNING };
      MockedTaskModel.findById.mockResolvedValue(runningTask);

      await expect(taskService.deleteTask(mockTaskId, mockUserId))
        .rejects.toThrow('不能删除正在运行的任务');
    });
  });

  describe('getTaskStats', () => {
    it('应该成功获取任务统计信息', async () => {
      const mockStats = {
        total: 10,
        pending: 2,
        running: 1,
        completed: 6,
        failed: 1
      };

      MockedTaskModel.getTaskStats.mockResolvedValue(mockStats);

      const result = await taskService.getTaskStats(mockUserId);

      expect(MockedTaskModel.getTaskStats).toHaveBeenCalledWith(mockUserId);
      expect(result).toEqual(mockStats);
    });
  });

  describe('getRunningTasks', () => {
    it('应该成功获取正在运行的任务', async () => {
      const runningTasks: Task[] = [
        {
          id: 'task-1',
          userId: 'user-1',
          name: '运行中任务1',
          config: {} as TaskConfig,
          status: TaskStatus.RUNNING,
          progress: 50,
          collectedCount: 100,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      MockedTaskModel.findByStatus.mockResolvedValue(runningTasks);

      const result = await taskService.getRunningTasks();

      expect(MockedTaskModel.findByStatus).toHaveBeenCalledWith(TaskStatus.RUNNING);
      expect(result).toEqual(runningTasks);
    });
  });

  describe('batchUpdateTaskStatus', () => {
    it('应该成功批量更新任务状态', async () => {
      const taskIds = ['task-1', 'task-2', 'task-3'];
      MockedTaskModel.updateStatus.mockResolvedValue();

      await taskService.batchUpdateTaskStatus(taskIds, TaskStatus.FAILED, '批量失败');

      expect(MockedTaskModel.updateStatus).toHaveBeenCalledTimes(3);
      taskIds.forEach(taskId => {
        expect(MockedTaskModel.updateStatus).toHaveBeenCalledWith(
          taskId, 
          TaskStatus.FAILED, 
          '批量失败'
        );
      });
    });
  });

  describe('searchTasks', () => {
    it('应该成功搜索任务', async () => {
      const mockTasks: Task[] = [
        {
          id: 'task-1',
          userId: mockUserId,
          name: '心理健康任务',
          config: {} as TaskConfig,
          status: TaskStatus.COMPLETED,
          progress: 100,
          collectedCount: 200,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      MockedTaskModel.search.mockResolvedValue({
        tasks: mockTasks,
        total: 1
      });

      const result = await taskService.searchTasks(
        mockUserId, 
        '心理健康', 
        TaskStatus.COMPLETED, 
        1, 
        10
      );

      expect(MockedTaskModel.search).toHaveBeenCalledWith(
        mockUserId, 
        '心理健康', 
        TaskStatus.COMPLETED, 
        1, 
        10
      );
      expect(result).toEqual({
        tasks: mockTasks,
        total: 1,
        page: 1,
        totalPages: 1
      });
    });
  });
});