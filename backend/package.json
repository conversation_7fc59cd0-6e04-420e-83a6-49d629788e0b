{"name": "social-content-collector-backend", "version": "1.0.0", "description": "Backend for social content collector system", "main": "dist/index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:models": "jest --testPathPattern=models.test.ts --runInBand", "migrate": "ts-node src/config/migrate.ts", "db:init": "ts-node src/config/initDatabase.ts", "db:migrate": "ts-node src/config/migrate.ts", "db:setup": "npm run db:init", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.0.0", "mysql2": "^3.6.0", "redis": "^4.6.7", "uuid": "^9.0.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.3", "@types/node": "^20.4.5", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.2", "jest": "^29.6.1", "supertest": "^7.1.4", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.1.6"}}