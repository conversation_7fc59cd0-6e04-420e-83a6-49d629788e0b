# 心理健康内容采集与分析系统 PRD

## 1. 产品概述

### 1.1 产品定位
一个专注于心理健康和情绪挑战领域的智能内容采集与分析平台，通过自动化采集社交媒体上的相关故事内容，分析其模式和传播规律，为内容创作者和心理健康从业者提供数据驱动的洞察。

### 1.2 目标用户
- **内容创作者**: 需要了解心理健康内容创作趋势的博主、作家
- **心理健康从业者**: 心理咨询师、治疗师、心理健康机构
- **媒体机构**: 专注心理健康领域的媒体公司和出版社
- **研究人员**: 心理学研究者、社会学研究者

### 1.3 核心价值
- 🎯 **精准定位**: 专注心理健康垂直领域，提供高质量内容洞察
- 🤖 **AI驱动**: 智能分析故事类型、讲述方式和用户互动模式
- 📊 **数据驱动**: 基于真实用户行为数据，预测内容传播潜力
- 🌍 **全球视野**: 专注美国市场，覆盖主流英文内容平台

## 2. 核心功能需求

### 2.1 内容采集模块
**目标平台**:

**优先级平台 (Phase 1)**:
- **Reddit**: r/depression, r/anxiety, r/mentalhealth, r/getmotivated, r/decidingtobebetter, r/selfimprovement
- **YouTube**: 心理健康相关频道和视频、个人成长vlog、治疗师频道
- **TikTok**: 心理健康相关短视频、个人故事分享、专业建议内容
- **Podcast平台**:
  - **Spotify**: The Moth, Mental Health Happy Hour, Therapy for Black Girls, The Hilarious World of Depression
  - **Apple Podcasts**: On Being, Ten Percent Happier, The Life Coach School Podcast
  - **Google Podcasts**: Terrible, Thanks for Asking, The Mental Illness Happy Hour
  - **其他播客平台**: Stitcher, Overcast中的心理健康相关节目

**扩展平台 (Phase 2+)**:
- **Medium**: 心理健康、个人成长相关文章
- **Instagram**: 心理健康博主和相关hashtags
- **X (Twitter)**: 心理健康相关话题和KOL
- **Character.AI**: 心理健康相关bot对话
- **Calm/Headspace**: 用户喜爱的故事内容
- **Clubhouse**: 心理健康相关房间和对话

**采集内容类型**:
- 📝 **文本内容**: Reddit帖子、YouTube视频描述、TikTok标题、播客节目描述
- 🎵 **音频内容**: 播客完整音频、音频转录文本、语音片段
- 📹 **视频内容**: YouTube长视频、TikTok短视频、视频字幕和转录
- 📊 **互动数据**: 点赞数、评论数、分享数、播放量、订阅数、收听时长
- 💬 **用户反馈**: 评论内容、用户回复、社区讨论、评分数据

**关键词策略**:
- 核心关键词: depression recovery, anxiety management, mental health journey, emotional healing
- 场景关键词: job loss recovery, breakup healing, social anxiety, loneliness solutions
- 情感关键词: hope, resilience, breakthrough, transformation, healing

### 2.2 AI分析模块
**故事分类系统**:
- 失业与职业困境恢复
- 失恋与情感创伤治愈
- 社交焦虑与人际关系
- 孤独感与社会连接
- 抑郁症康复历程
- 焦虑症管理经验
- 创伤后成长故事
- 自我接纳与成长

**讲述方式分析**:
- 叙述结构: 起承转合、英雄之旅、问题-解决模式
- 情感弧线: 低谷-转折-成长-希望
- 语言风格: 第一人称体验、专业建议、同伴支持
- 互动元素: 提问、征求建议、分享资源

**用户互动分析**:
- 评论情感倾向分析
- 用户参与度评分
- 内容传播路径追踪
- 付费转化潜力预测

### 2.3 数据过滤与质量控制
**真实度评估**:
- 内容一致性检查
- 用户历史行为分析
- 社区验证机制
- 专业内容标识

**互动质量筛选**:
- 高互动率内容优先 (评论率>5%, 分享率>2%)
- 深度互动内容识别 (长评论、多轮对话)
- 情感共鸣指标 (正面反馈比例)

**付费内容识别**:
- 付费课程/咨询推广
- 书籍/产品推荐转化
- 会员制内容平台
- 打赏/众筹内容

## 3. 产品功能设计

### 3.1 管理后台功能
**数据监控面板**:
- 实时采集状态监控
- 平台数据统计概览
- 热门内容趋势图表
- 系统性能指标

**内容管理**:
- 智能搜索与过滤
- 内容分类标签管理
- 批量操作与导出
- 内容质量评分

**分析报告**:
- 故事类型分布分析
- 讲述方式效果对比
- 用户互动模式洞察
- 付费转化预测报告

### 3.2 数据可视化
**趋势分析**:
- 内容热度时间线
- 话题流行度变化
- 平台表现对比
- 用户情感变化曲线

**故事模式分析**:
- 成功故事结构图
- 情感弧线可视化
- 关键词云图
- 传播路径网络图

### 3.3 导出功能
**数据格式**:
- Excel报表导出
- JSON/CSV原始数据
- PDF分析报告
- 可视化图表导出

**内容下载**:
- 文本内容批量下载
- 音频/视频链接整理
- 元数据完整导出
- 自定义字段选择

## 4. 技术架构设计

### 4.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据采集层     │    │   数据处理层     │    │   应用服务层     │
│                │    │                │    │                │
│ • 多平台爬虫    │───▶│ • 数据清洗      │───▶│ • Web管理后台   │
│ • API集成      │    │ • AI分析       │    │ • API服务      │
│ • 实时监控      │    │ • 质量评估      │    │ • 报告生成      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   消息队列       │    │   数据存储层     │    │   缓存层        │
│                │    │                │    │                │
│ • Celery       │    │ • PostgreSQL   │    │ • Redis        │
│ • Redis Queue  │    │ • 文件存储      │    │ • 内存缓存      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 4.2 技术栈选择

**后端服务**:
- **框架**: Python + FastAPI (高性能异步框架)
- **数据库**: PostgreSQL (关系型数据) + MongoDB (非结构化内容)
- **缓存**: Redis (会话缓存 + 任务队列)
- **消息队列**: Celery + Redis (异步任务处理)
- **AI服务**: OpenAI API + Hugging Face Transformers

**前端应用**:
- **框架**: React 18 + TypeScript
- **UI库**: Ant Design Pro (企业级UI组件)
- **可视化**: Chart.js + D3.js (数据可视化)
- **状态管理**: Redux Toolkit
- **构建工具**: Vite

**数据采集**:
- **爬虫框架**: Scrapy + Selenium (动态内容)
- **API集成**:
  - Reddit API (PRAW)
  - YouTube Data API v3
  - TikTok Research API
  - Spotify Web API
  - Apple Podcasts API
- **音频处理**: Whisper API (音频转录)
- **代理管理**: 代理池 + 请求限流
- **反爬虫**: User-Agent轮换 + 行为模拟

**AI/ML服务**:
- **文本分析**: spaCy + NLTK (预处理)
- **情感分析**: BERT + RoBERTa (预训练模型)
- **分类模型**: 自训练分类器
- **内容生成**: GPT-4 API (分析报告生成)

**部署运维**:
- **容器化**: Docker + Docker Compose
- **云服务**: AWS/GCP (弹性扩容)
- **CI/CD**: GitHub Actions
- **监控**: Prometheus + Grafana

## 5. 项目实施计划

### 5.1 开发阶段规划

**Phase 1: MVP开发 - 优先平台 (4-6周)**
- [ ] 基础架构搭建 (FastAPI + React + PostgreSQL)
- [ ] Reddit数据采集实现 (PRAW API)
- [ ] YouTube数据采集实现 (YouTube Data API v3)
- [ ] TikTok数据采集实现 (TikTok API/第三方工具)
- [ ] Podcast数据采集实现 (Spotify API + Apple Podcasts)
- [ ] 基础内容分类功能 (关键词匹配)
- [ ] 简单管理后台界面
- [ ] 基础数据存储和展示

**Phase 2: 核心功能开发 (6-8周)**
- [ ] 优先平台数据采集优化和完善
- [ ] AI分析模块完善 (OpenAI API集成)
- [ ] 音频转录功能 (Whisper API)
- [ ] 视频字幕提取和分析
- [ ] 高级过滤和搜索功能
- [ ] 数据可视化面板
- [ ] 用户互动分析功能

**Phase 3: 扩展平台集成 (4-6周)**
- [ ] Medium文章采集和分析
- [ ] Instagram内容采集
- [ ] X (Twitter) 数据集成
- [ ] Character.AI对话数据采集
- [ ] 性能优化和系统扩容
- [ ] 高级AI分析功能 (自定义模型)
- [ ] 自动化报告生成

**Phase 4: 商业化与优化 (持续)**
- [ ] API接口开发和文档完善
- [ ] 付费功能开发
- [ ] 数据产品化
- [ ] 客户支持系统
- [ ] 用户体验优化
- [ ] 市场推广准备

### 5.2 技术风险与应对

**数据采集风险**:
- 风险: 平台反爬虫机制、API限制
- 应对: 多代理池、请求限流、官方API优先

**合规性风险**:
- 风险: 数据隐私法规、平台条款违规
- 应对: 严格遵守GDPR/CCPA、仅采集公开数据

**成本控制风险**:
- 风险: AI API调用费用、存储成本
- 应对: 智能缓存、批量处理、成本监控

**数据质量风险**:
- 风险: 虚假内容、低质量数据
- 应对: 多维度质量评估、人工审核机制

## 6. 快速开始

### 6.1 环境要求

**基础环境**:
- Python >= 3.9
- Node.js >= 18.0.0
- PostgreSQL >= 13.0
- Redis >= 6.0
- Docker >= 20.0 (可选)

**API密钥准备**:
- OpenAI API Key
- Reddit API Credentials
- YouTube Data API Key
- 其他平台API密钥

### 6.2 本地开发环境搭建

```bash
# 1. 克隆项目
git clone <repository-url>
cd mental-health-content-analyzer

# 2. 后端环境配置
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# 3. 前端环境配置
cd ../frontend
npm install

# 4. 环境变量配置
cp .env.example .env
# 编辑 .env 文件，填入API密钥和数据库配置

# 5. 数据库初始化
python manage.py migrate
python manage.py create_superuser
```

### 6.3 启动服务

```bash
# 启动后端服务 (端口: 8000)
cd backend
python manage.py runserver

# 启动前端服务 (端口: 3000)
cd frontend
npm run dev

# 启动Celery任务队列
cd backend
celery -A app worker --loglevel=info
```

访问 http://localhost:3000 查看应用界面

## 7. 项目结构

```
mental-health-content-analyzer/
├── backend/                          # Python后端服务
│   ├── app/
│   │   ├── api/                     # API路由
│   │   │   ├── v1/
│   │   │   │   ├── endpoints/       # API端点
│   │   │   │   └── deps.py         # 依赖注入
│   │   │   └── api.py              # API路由汇总
│   │   ├── core/                   # 核心配置
│   │   │   ├── config.py           # 应用配置
│   │   │   ├── security.py         # 安全相关
│   │   │   └── database.py         # 数据库配置
│   │   ├── models/                 # 数据模型
│   │   │   ├── content.py          # 内容模型
│   │   │   ├── task.py             # 任务模型
│   │   │   └── analysis.py         # 分析结果模型
│   │   ├── services/               # 业务逻辑
│   │   │   ├── scrapers/           # 数据采集服务
│   │   │   │   ├── reddit_scraper.py
│   │   │   │   ├── youtube_scraper.py
│   │   │   │   ├── tiktok_scraper.py
│   │   │   │   ├── podcast_scraper.py
│   │   │   │   └── base_scraper.py
│   │   │   ├── analyzers/          # AI分析服务
│   │   │   │   ├── story_classifier.py
│   │   │   │   ├── sentiment_analyzer.py
│   │   │   │   └── pattern_detector.py
│   │   │   └── data_processor.py   # 数据处理服务
│   │   ├── tasks/                  # Celery异步任务
│   │   │   ├── scraping_tasks.py   # 采集任务
│   │   │   └── analysis_tasks.py   # 分析任务
│   │   └── utils/                  # 工具函数
│   ├── requirements.txt            # Python依赖
│   ├── alembic/                    # 数据库迁移
│   └── main.py                     # 应用入口
├── frontend/                       # React前端应用
│   ├── src/
│   │   ├── components/             # React组件
│   │   │   ├── Dashboard/          # 仪表板组件
│   │   │   ├── ContentList/        # 内容列表组件
│   │   │   ├── Analytics/          # 分析图表组件
│   │   │   └── TaskManager/        # 任务管理组件
│   │   ├── services/               # API服务
│   │   │   ├── api.ts              # API客户端
│   │   │   ├── contentService.ts   # 内容服务
│   │   │   └── taskService.ts      # 任务服务
│   │   ├── types/                  # TypeScript类型
│   │   ├── hooks/                  # React Hooks
│   │   ├── utils/                  # 工具函数
│   │   └── App.tsx                 # 主应用组件
│   ├── package.json
│   └── vite.config.ts
├── docker/                         # Docker配置
│   ├── Dockerfile.backend
│   ├── Dockerfile.frontend
│   └── docker-compose.yml
├── docs/                           # 项目文档
│   ├── api.md                      # API文档
│   ├── deployment.md               # 部署指南
│   └── development.md              # 开发指南
├── scripts/                        # 脚本文件
│   ├── setup.sh                    # 环境搭建脚本
│   └── deploy.sh                   # 部署脚本
├── .env.example                    # 环境变量模板
├── .gitignore
└── README.md
```

## 8. API接口设计

### 8.1 认证授权
```
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
POST /api/v1/auth/refresh        # 刷新Token
GET  /api/v1/auth/me            # 获取当前用户信息
```

### 8.2 任务管理
```
POST   /api/v1/tasks                    # 创建采集任务
GET    /api/v1/tasks                    # 获取任务列表
GET    /api/v1/tasks/{task_id}          # 获取任务详情
PUT    /api/v1/tasks/{task_id}          # 更新任务配置
DELETE /api/v1/tasks/{task_id}          # 删除任务
POST   /api/v1/tasks/{task_id}/start    # 启动任务
POST   /api/v1/tasks/{task_id}/pause    # 暂停任务
POST   /api/v1/tasks/{task_id}/stop     # 停止任务
GET    /api/v1/tasks/{task_id}/status   # 获取任务状态
GET    /api/v1/tasks/{task_id}/logs     # 获取任务日志
```

### 8.3 内容管理
```
GET    /api/v1/content                  # 获取内容列表
GET    /api/v1/content/{content_id}     # 获取内容详情
GET    /api/v1/content/search           # 搜索内容
GET    /api/v1/content/trending         # 获取热门内容
POST   /api/v1/content/batch-export     # 批量导出内容
PUT    /api/v1/content/{content_id}/tag # 添加标签
DELETE /api/v1/content/{content_id}     # 删除内容
```

### 8.4 分析服务
```
GET    /api/v1/analysis/overview        # 获取分析概览
GET    /api/v1/analysis/stories/types   # 故事类型分析
GET    /api/v1/analysis/stories/patterns # 故事模式分析
GET    /api/v1/analysis/engagement      # 用户互动分析
GET    /api/v1/analysis/trends          # 趋势分析
POST   /api/v1/analysis/custom          # 自定义分析
GET    /api/v1/analysis/reports/{id}    # 获取分析报告
POST   /api/v1/analysis/export          # 导出分析数据
```

### 8.5 平台管理
```
GET    /api/v1/platforms                # 获取支持的平台列表
GET    /api/v1/platforms/{platform}/config # 获取平台配置
PUT    /api/v1/platforms/{platform}/config # 更新平台配置
GET    /api/v1/platforms/{platform}/stats  # 获取平台统计数据
```

### 8.6 数据导出
```
POST   /api/v1/export/content           # 导出内容数据
POST   /api/v1/export/analysis          # 导出分析结果
GET    /api/v1/export/jobs              # 获取导出任务列表
GET    /api/v1/export/jobs/{job_id}     # 获取导出任务状态
GET    /api/v1/export/download/{job_id} # 下载导出文件
```

## 开发指南

### 代码规范

项目使用TypeScript + ESLint + Prettier进行代码规范管理。

### 测试

```bash
# 运行后端测试
cd backend
npm test

# 运行前端测试
cd frontend
npm test
```

### 构建部署

```bash
# 构建后端
cd backend
npm run build

# 构建前端
cd frontend
npm run build
```

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。